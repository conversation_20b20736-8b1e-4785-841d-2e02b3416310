<?php
/**
 * معالج التواصل
 * Contact Handler
 * 
 * هذا الملف يتعامل مع معالجة طلبات التواصل
 * بما في ذلك التحقق من البيانات وإرسال الرسائل
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// التحقق من نوع الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die('Method Not Allowed');
}

try {
    // الحصول على البيانات المرسلة
    $firstName = trim($_POST['firstName'] ?? '');
    $lastName = trim($_POST['lastName'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $privacy = isset($_POST['privacy']) && $_POST['privacy'] === 'on';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrfToken)) {
        throw new Exception('رمز الأمان غير صحيح');
    }
    
    // التحقق من صحة البيانات الأساسية
    if (empty($firstName) || empty($lastName) || empty($email) || empty($subject) || empty($message)) {
        throw new Exception('جميع الحقول المطلوبة يجب ملؤها');
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('صيغة البريد الإلكتروني غير صحيحة');
    }
    
    // التحقق من الموافقة على سياسة الخصوصية
    if (!$privacy) {
        throw new Exception('يجب الموافقة على سياسة الخصوصية');
    }
    
    // تنظيف البيانات
    $firstName = sanitize($firstName);
    $lastName = sanitize($lastName);
    $email = sanitize($email);
    $subject = sanitize($subject);
    $message = sanitize($message);
    $phone = sanitize($phone);
    
    // حفظ الرسالة في قاعدة البيانات
    $db = Database::getInstance();
    
    $sql = "INSERT INTO contact_messages (first_name, last_name, email, phone, subject, message, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $firstName,
        $lastName,
        $email,
        $phone,
        $subject,
        $message,
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    $result = $db->insert($sql, $params);
    
    if (!$result) {
        throw new Exception('حدث خطأ في حفظ الرسالة، يرجى المحاولة مرة أخرى');
    }
    
    // إرسال استجابة JSON إذا كان الطلب AJAX
    if (isAjaxRequest()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تم إرسال رسالتك بنجاح، سنتواصل معك قريباً'
        ]);
        exit;
    }
    
    // إعادة التوجيه مع رسالة نجاح
    setAlert('تم إرسال رسالتك بنجاح، سنتواصل معك قريباً', 'success');
    header('Location: ' . getAppUrl('contact'));
    exit;
    
} catch (Exception $e) {
    $error = $e->getMessage();
    
    // إرسال استجابة JSON إذا كان الطلب AJAX
    if (isAjaxRequest()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $error
        ]);
        exit;
    }
    
    // حفظ الخطأ في الجلسة وإعادة التوجيه
    $_SESSION['contact_error'] = $error;
    $_SESSION['contact_data'] = $_POST;
    
    // إعادة التوجيه لصفحة التواصل
    header('Location: ' . getAppUrl('contact'));
    exit;
}

/**
 * التحقق من رمز CSRF
 * @param string $token الرمز
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * التحقق من أن الطلب AJAX
 * @return bool
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}
?> 