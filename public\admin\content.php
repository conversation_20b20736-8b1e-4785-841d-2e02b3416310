<?php
/**
 * إدارة المحتوى - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/content');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$contentId = $_GET['id'] ?? 0;

if ($action && $contentId) {
    try {
        $db = db();
        
        switch ($action) {
            case 'publish':
                $db->update("content", ["status" => "published"], "id = ?", [$contentId]);
                setAlert('تم نشر المحتوى بنجاح', 'success');
                break;
                
            case 'unpublish':
                $db->update("content", ["status" => "draft"], "id = ?", [$contentId]);
                setAlert('تم إلغاء نشر المحتوى بنجاح', 'success');
                break;
                
            case 'delete':
                $db->delete("content", "id = ?", [$contentId]);
                setAlert('تم حذف المحتوى بنجاح', 'success');
                break;
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// معاملات البحث والتصفية
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(title LIKE ? OR content LIKE ? OR author_name LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if ($category) {
    $whereConditions[] = "category = ?";
    $params[] = $category;
}

if ($status) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات عامة
    $stats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM content")[0]['count'] ?? 0,
        'published' => $db->select("SELECT COUNT(*) as count FROM content WHERE status = 'published'")[0]['count'] ?? 0,
        'draft' => $db->select("SELECT COUNT(*) as count FROM content WHERE status = 'draft'")[0]['count'] ?? 0,
        'archived' => $db->select("SELECT COUNT(*) as count FROM content WHERE status = 'archived'")[0]['count'] ?? 0
    ];
    
    // إجمالي عدد المحتويات للصفحات
    $totalContent = $db->select("SELECT COUNT(*) as count FROM content $whereClause", $params)[0]['count'] ?? 0;
    $totalPages = ceil($totalContent / $perPage);
    
    // قائمة المحتويات
    $contents = $db->select("
        SELECT id, title, content, category, status, author_name, created_at, updated_at, views_count
        FROM content 
        $whereClause 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ", array_merge($params, [$perPage, $offset]));
    
} catch (Exception $e) {
    $contents = [];
    $totalContent = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'published' => 0, 'draft' => 0, 'archived' => 0];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إدارة المحتوى';
$currentPage = 'admin_content';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .content-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .content-card {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
        transition: all 0.3s ease;
    }

    .content-card:hover {
        background: #e8f0ff;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-file-alt me-2"></i>
                                إدارة المحتوى
                            </h2>
                            <p class="mb-0">إدارة جميع المحتويات والمواد في المنصة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['total'] ?></h3>
                    <p class="mb-0">إجمالي المحتويات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['published'] ?></h3>
                    <p class="mb-0">المحتويات المنشورة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-edit fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['draft'] ?></h3>
                    <p class="mb-0">المسودات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-secondary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-archive fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['archived'] ?></h3>
                    <p class="mb-0">المحتويات المؤرشفة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-container">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="البحث في العنوان أو المحتوى">
                        </div>
                        <div class="col-md-2">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">الكل</option>
                                <option value="articles" <?= $category === 'articles' ? 'selected' : '' ?>>مقالات</option>
                                <option value="guides" <?= $category === 'guides' ? 'selected' : '' ?>>دليل</option>
                                <option value="news" <?= $category === 'news' ? 'selected' : '' ?>>أخبار</option>
                                <option value="resources" <?= $category === 'resources' ? 'selected' : '' ?>>موارد</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="published" <?= $status === 'published' ? 'selected' : '' ?>>منشور</option>
                                <option value="draft" <?= $status === 'draft' ? 'selected' : '' ?>>مسودة</option>
                                <option value="archived" <?= $status === 'archived' ? 'selected' : '' ?>>مؤرشف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <a href="<?= url('admin/content') ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-refresh"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المحتويات -->
    <div class="row">
        <div class="col-12">
            <div class="card table-container">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة المحتويات
                        <span class="badge bg-light text-primary ms-2"><?= $totalContent ?> محتوى</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($contents)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد محتويات</h5>
                        <p class="text-muted">لم يتم العثور على محتويات تطابق معايير البحث</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المحتوى</th>
                                    <th>الفئة</th>
                                    <th>الكاتب</th>
                                    <th>الحالة</th>
                                    <th>المشاهدات</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($contents as $content): ?>
                                <tr class="content-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="content-avatar me-3">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($content['title']) ?></h6>
                                                <small class="text-muted"><?= substr(htmlspecialchars($content['content']), 0, 100) ?>...</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= htmlspecialchars($content['category']) ?></span>
                                    </td>
                                    <td><?= htmlspecialchars($content['author_name']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $content['status'] === 'published' ? 'success' : ($content['status'] === 'draft' ? 'warning' : 'secondary') ?>">
                                            <?= $content['status'] === 'published' ? 'منشور' : ($content['status'] === 'draft' ? 'مسودة' : 'مؤرشف') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-eye text-muted"></i>
                                        <?= $content['views_count'] ?? 0 ?>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($content['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($content['status'] === 'draft'): ?>
                                            <a href="?action=publish&id=<?= $content['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <?php elseif ($content['status'] === 'published'): ?>
                                            <a href="?action=unpublish&id=<?= $content['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action">
                                                <i class="fas fa-pause"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="<?= url('admin/content/edit/' . $content['id']) ?>" 
                                               class="btn btn-sm btn-info btn-action">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <a href="<?= url('admin/content/view/' . $content['id']) ?>" 
                                               class="btn btn-sm btn-secondary btn-action">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <a href="?action=delete&id=<?= $content['id'] ?>" 
                                               class="btn btn-sm btn-danger btn-action" 
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المحتوى؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 