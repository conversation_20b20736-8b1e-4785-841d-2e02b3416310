# حل مشكلة إعادة توجيه المشرف

## المشكلة
بعد تسجيل الدخول كأدمن، يتم توجيه المستخدم إلى مسار معالج تسجيل الدخول بدلاً من لوحة التحكم.

## السبب
1. جدول `users` لا يحتوي على حقل `user_type`
2. الكود يتوقع وجود هذا الحقل لتحديد نوع المستخدم
3. مسار إعادة التوجيه غير صحيح

## الحل

### الخطوة 1: تحديث قاعدة البيانات
قم بتشغيل الملف التالي لتحديث قاعدة البيانات:

```sql
-- تحديث قاعدة البيانات لإضافة حقل user_type
USE nafsi_platform;

-- إضافة حقل user_type إلى جدول users
ALTER TABLE users ADD COLUMN user_type ENUM('regular', 'specialist', 'admin') DEFAULT 'regular' AFTER profile_image;

-- تحديث المستخدم المشرف الموجود
UPDATE users SET user_type = 'admin' WHERE email = '<EMAIL>';

-- تحديث جميع المستخدمين الموجودين في جدول admins
UPDATE users u 
JOIN admins a ON u.id = a.user_id 
SET u.user_type = 'admin';

-- تحديث جميع المستخدمين الموجودين في جدول specialists
UPDATE users u 
JOIN specialists s ON u.id = s.user_id 
SET u.user_type = 'specialist';
```

### الخطوة 2: التغييرات المطبقة

#### 1. تحديث ملف قاعدة البيانات
- تم إضافة حقل `user_type` إلى جدول `users`
- تم تحديث بيانات المستخدم المشرف

#### 2. تحديث ملف Auth.php
- تم تغيير مسار إعادة التوجيه للمشرفين من `/admin/dashboard` إلى `/admin`

#### 3. تحديث ملف login_handler.php
- تم إضافة تضمين ملف `config.php` للحصول على دالة `getAppUrl`

### الخطوة 3: اختبار الحل
1. قم بتشغيل أوامر SQL أعلاه
2. جرب تسجيل الدخول كأدمن:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `admin123`
3. يجب أن يتم توجيهك إلى لوحة التحكم: `http://localhost/nafsi_platform/public/admin/`

## ملاحظات
- تأكد من أن قاعدة البيانات محدثة قبل اختبار الحل
- إذا كنت تستخدم قاعدة بيانات مختلفة، قم بتعديل اسم قاعدة البيانات في أوامر SQL
- تأكد من أن جميع الملفات محدثة في المشروع 