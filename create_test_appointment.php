<?php
/**
 * إنشاء موعد تجريبي لاختبار صفحة تفاصيل الموعد
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>إنشاء موعد تجريبي</h2>";
    
    // بيانات الموعد التجريبي
    $appointmentData = [
        'client_id' => 13, // مريم علي
        'therapist_id' => 3, // أحمد محمد
        'appointment_date' => '2024-12-25',
        'appointment_time' => '10:00',
        'status' => 'confirmed',
        'notes' => 'موعد تجريبي لاختبار صفحة التفاصيل',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // بناء استعلام INSERT
    $columns = implode(', ', array_keys($appointmentData));
    $placeholders = ':' . implode(', :', array_keys($appointmentData));
    $sql = "INSERT INTO appointments ($columns) VALUES ($placeholders)";
    
    echo "<h3>إدراج موعد تجريبي...</h3>";
    $result = $db->insert($sql, $appointmentData);
    
    if ($result === false) {
        echo "<p style='color: red;'>فشل في إدراج الموعد التجريبي</p>";
        $error = $db->getLastError();
        if ($error) {
            echo "<p style='color: red;'>خطأ SQL: " . $error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>تم إدراج الموعد التجريبي بنجاح! (ID: $result)</p>";
        
        // عرض تفاصيل الموعد
        $appointment = $db->selectOne("
            SELECT a.*, 
                   c.first_name as client_first_name, c.last_name as client_last_name,
                   t.first_name as therapist_first_name, t.last_name as therapist_last_name
            FROM appointments a
            JOIN users c ON a.client_id = c.id
            JOIN users t ON a.therapist_id = t.id
            WHERE a.id = ?
        ", [$result]);
        
        if ($appointment) {
            echo "<h3>تفاصيل الموعد المضاف:</h3>";
            echo "<p><strong>العميل:</strong> " . $appointment['client_first_name'] . ' ' . $appointment['client_last_name'] . "</p>";
            echo "<p><strong>المعالج:</strong> " . $appointment['therapist_first_name'] . ' ' . $appointment['therapist_last_name'] . "</p>";
            echo "<p><strong>التاريخ:</strong> " . $appointment['appointment_date'] . "</p>";
            echo "<p><strong>الوقت:</strong> " . $appointment['appointment_time'] . "</p>";
            echo "<p><strong>الحالة:</strong> " . $appointment['status'] . "</p>";
            echo "<p><strong>الملاحظات:</strong> " . $appointment['notes'] . "</p>";
        }
        
        echo "<h3>رابط صفحة التفاصيل:</h3>";
        echo "<p><a href='http://localhost/nafsi_platform/admin/appointment/$result' target='_blank'>عرض تفاصيل الموعد #$result</a></p>";
        
        echo "<h3>رابط قائمة المواعيد:</h3>";
        echo "<p><a href='http://localhost/nafsi_platform/admin/appointments' target='_blank'>العودة لقائمة المواعيد</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 