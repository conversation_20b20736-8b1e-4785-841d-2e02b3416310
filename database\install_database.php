<?php
/**
 * سكريبت تثبيت قاعدة البيانات
 * Database Installation Script
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'nafsi_platform';

// دالة تنفيذ ملف SQL
function executeSqlFile($pdo, $filename) {
    echo "جاري تنفيذ: $filename\n";
    
    if (!file_exists($filename)) {
        echo "❌ الملف غير موجود: $filename\n";
        return false;
    }
    
    $sql = file_get_contents($filename);
    
    try {
        // تقسيم الاستعلامات
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        echo "✅ تم تنفيذ: $filename بنجاح\n";
        return true;
        
    } catch (PDOException $e) {
        echo "❌ خطأ في تنفيذ: $filename\n";
        echo "الخطأ: " . $e->getMessage() . "\n";
        return false;
    }
}

// دالة إنشاء اتصال قاعدة البيانات
function createConnection($host, $user, $pass, $dbname = null) {
    try {
        if ($dbname) {
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        } else {
            $dsn = "mysql:host=$host;charset=utf8mb4";
        }
        
        $pdo = new PDO($dsn, $user, $pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        return $pdo;
        
    } catch (PDOException $e) {
        echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
        return null;
    }
}

// دالة التحقق من وجود قاعدة البيانات
function databaseExists($pdo, $dbname) {
    try {
        $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// دالة إنشاء قاعدة البيانات
function createDatabase($pdo, $dbname) {
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ تم إنشاء قاعدة البيانات: $dbname\n";
        return true;
    } catch (PDOException $e) {
        echo "❌ خطأ في إنشاء قاعدة البيانات: " . $e->getMessage() . "\n";
        return false;
    }
}

// دالة اختيار قاعدة البيانات
function useDatabase($pdo, $dbname) {
    try {
        $pdo->exec("USE `$dbname`");
        echo "✅ تم اختيار قاعدة البيانات: $dbname\n";
        return true;
    } catch (PDOException $e) {
        echo "❌ خطأ في اختيار قاعدة البيانات: " . $e->getMessage() . "\n";
        return false;
    }
}

// دالة التحقق من وجود الجداول
function checkTables($pdo) {
    $tables = ['users', 'specialists', 'admins', 'bookings', 'sessions'];
    $existing = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $existing[] = $table;
            }
        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }
    
    return $existing;
}

// دالة النسخ الاحتياطي
function backupDatabase($pdo, $dbname) {
    $backup_file = "backup_" . date('Y-m-d_H-i-s') . ".sql";
    
    try {
        // الحصول على جميع الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $backup_content = "-- نسخة احتياطية من قاعدة البيانات: $dbname\n";
        $backup_content .= "-- تاريخ النسخ: " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($tables as $table) {
            // هيكل الجدول
            $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
            $create_table = $stmt->fetch();
            $backup_content .= $create_table['Create Table'] . ";\n\n";
            
            // بيانات الجدول
            $stmt = $pdo->query("SELECT * FROM `$table`");
            $rows = $stmt->fetchAll();
            
            if (!empty($rows)) {
                $backup_content .= "-- بيانات جدول: $table\n";
                foreach ($rows as $row) {
                    $columns = implode('`, `', array_keys($row));
                    $values = implode("', '", array_map('addslashes', $row));
                    $backup_content .= "INSERT INTO `$table` (`$columns`) VALUES ('$values');\n";
                }
                $backup_content .= "\n";
            }
        }
        
        file_put_contents($backup_file, $backup_content);
        echo "✅ تم إنشاء نسخة احتياطية: $backup_file\n";
        return true;
        
    } catch (PDOException $e) {
        echo "❌ خطأ في إنشاء النسخة الاحتياطية: " . $e->getMessage() . "\n";
        return false;
    }
}

// دالة عرض القائمة الرئيسية
function showMenu() {
    echo "\n=== سكريبت تثبيت قاعدة البيانات ===\n";
    echo "1. إنشاء قاعدة البيانات الأساسية\n";
    echo "2. إضافة الجداول الإضافية\n";
    echo "3. تحديث نوع المستخدم\n";
    echo "4. إنشاء نسخة احتياطية\n";
    echo "5. التحقق من حالة قاعدة البيانات\n";
    echo "6. تنفيذ جميع الخطوات\n";
    echo "0. خروج\n";
    echo "اختر رقم العملية: ";
}

// دالة تنفيذ جميع الخطوات
function installAll($pdo, $dbname) {
    echo "\n=== بدء التثبيت الكامل ===\n";
    
    // 1. إنشاء قاعدة البيانات
    if (!createDatabase($pdo, $dbname)) {
        return false;
    }
    
    // 2. اختيار قاعدة البيانات
    if (!useDatabase($pdo, $dbname)) {
        return false;
    }
    
    // 3. تنفيذ الملفات
    $files = [
        'create_database.sql',
        'additional_tables.sql',
        'contact_messages_table.sql',
        'update_user_type.sql'
    ];
    
    foreach ($files as $file) {
        if (!executeSqlFile($pdo, $file)) {
            return false;
        }
    }
    
    echo "\n✅ تم التثبيت بنجاح!\n";
    return true;
}

// دالة التحقق من حالة قاعدة البيانات
function checkDatabaseStatus($pdo, $dbname) {
    echo "\n=== حالة قاعدة البيانات ===\n";
    
    // التحقق من وجود قاعدة البيانات
    if (databaseExists($pdo, $dbname)) {
        echo "✅ قاعدة البيانات موجودة: $dbname\n";
        
        // اختيار قاعدة البيانات
        if (useDatabase($pdo, $dbname)) {
            // التحقق من الجداول
            $existing_tables = checkTables($pdo);
            
            if (!empty($existing_tables)) {
                echo "✅ الجداول الموجودة:\n";
                foreach ($existing_tables as $table) {
                    echo "  - $table\n";
                }
            } else {
                echo "⚠️ لا توجد جداول في قاعدة البيانات\n";
            }
        }
    } else {
        echo "❌ قاعدة البيانات غير موجودة: $dbname\n";
    }
}

// البرنامج الرئيسي
echo "=== سكريبت تثبيت قاعدة البيانات ===\n";
echo "منصة نفسي - Nafsi Platform\n\n";

// الاتصال بقاعدة البيانات
$pdo = createConnection($db_host, $db_user, $db_pass);

if (!$pdo) {
    echo "❌ فشل الاتصال بقاعدة البيانات\n";
    exit(1);
}

echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";

// عرض القائمة
while (true) {
    showMenu();
    $choice = trim(fgets(STDIN));
    
    switch ($choice) {
        case '1':
            // إنشاء قاعدة البيانات الأساسية
            if (createDatabase($pdo, $db_name)) {
                useDatabase($pdo, $db_name);
                executeSqlFile($pdo, 'create_database.sql');
            }
            break;
            
        case '2':
            // إضافة الجداول الإضافية
            if (useDatabase($pdo, $db_name)) {
                executeSqlFile($pdo, 'additional_tables.sql');
            }
            break;
            
        case '3':
            // تحديث نوع المستخدم
            if (useDatabase($pdo, $db_name)) {
                executeSqlFile($pdo, 'update_user_type.sql');
            }
            break;
            
        case '4':
            // إنشاء نسخة احتياطية
            if (useDatabase($pdo, $db_name)) {
                backupDatabase($pdo, $db_name);
            }
            break;
            
        case '5':
            // التحقق من حالة قاعدة البيانات
            checkDatabaseStatus($pdo, $db_name);
            break;
            
        case '6':
            // تنفيذ جميع الخطوات
            installAll($pdo, $db_name);
            break;
            
        case '0':
            echo "👋 شكراً لاستخدام السكريبت!\n";
            exit(0);
            
        default:
            echo "❌ اختيار غير صحيح\n";
            break;
    }
    
    echo "\nاضغط Enter للمتابعة...";
    fgets(STDIN);
}
?> 