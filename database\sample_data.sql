-- بيانات تجريبية لمنصة نفسي
-- Sample Data for Nafsi Platform

USE nafsi_platform;

-- إنشاء جدول المواعيد إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    therapist_id INT NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- مفاتيح خارجية
    FOREIGN KEY (client_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (therapist_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- فهارس للتحسين
    INDEX idx_client_id (client_id),
    INDEX idx_therapist_id (therapist_id),
    INDEX idx_appointment_date (appointment_date),
    INDEX idx_status (status),
    INDEX idx_client_therapist_date (client_id, therapist_id, appointment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة 10 معالجين
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, date_of_birth, gender, user_type, is_active, is_verified, created_at, updated_at) VALUES
('dr.ahmed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'محمد', '+966501234567', '1985-03-15', 'male', 'therapist', 1, 1, NOW(), NOW()),
('dr.sara', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة', 'أحمد', '+966502345678', '1988-07-22', 'female', 'therapist', 1, 1, NOW(), NOW()),
('dr.khalid', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد', 'علي', '+966503456789', '1982-11-08', 'male', 'therapist', 1, 1, NOW(), NOW()),
('dr.fatima', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة', 'حسن', '+966504567890', '1990-05-12', 'female', 'therapist', 1, 1, NOW(), NOW()),
('dr.omar', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'عمر', 'عبدالله', '+966505678901', '1987-09-30', 'male', 'therapist', 1, 1, NOW(), NOW()),
('dr.layla', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ليلى', 'محمد', '+966506789012', '1986-12-18', 'female', 'therapist', 1, 1, NOW(), NOW()),
('dr.youssef', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'يوسف', 'أحمد', '+966507890123', '1984-02-25', 'male', 'therapist', 1, 1, NOW(), NOW()),
('dr.nour', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'نور', 'علي', '+966508901234', '1989-08-14', 'female', 'therapist', 1, 1, NOW(), NOW()),
('dr.hassan', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'حسن', 'محمد', '+966509012345', '1983-06-20', 'male', 'therapist', 1, 1, NOW(), NOW()),
('dr.zeina', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'زينة', 'أحمد', '+966500123456', '1991-01-10', 'female', 'therapist', 1, 1, NOW(), NOW());

-- إضافة 10 عملاء
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, date_of_birth, gender, user_type, is_active, is_verified, created_at, updated_at) VALUES
('mariam', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مريم', 'علي', '+966501111111', '1995-04-15', 'female', 'user', 1, 1, NOW(), NOW()),
('ali', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'علي', 'محمد', '+966502222222', '1992-08-20', 'male', 'user', 1, 1, NOW(), NOW()),
('nada', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ندى', 'أحمد', '+966503333333', '1998-12-10', 'female', 'user', 1, 1, NOW(), NOW()),
('mohammed', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد', 'عبدالله', '+966504444444', '1990-03-25', 'male', 'user', 1, 1, NOW(), NOW()),
('reem', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ريم', 'حسن', '+966505555555', '1996-07-18', 'female', 'user', 1, 1, NOW(), NOW()),
('ahmed_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد', 'علي', '+966506666666', '1993-11-05', 'male', 'user', 1, 1, NOW(), NOW()),
('sara_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة', 'محمد', '+966507777777', '1997-09-30', 'female', 'user', 1, 1, NOW(), NOW()),
('omar_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'عمر', 'أحمد', '+966508888888', '1991-02-14', 'male', 'user', 1, 1, NOW(), NOW()),
('layla_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ليلى', 'علي', '+966509999999', '1994-06-22', 'female', 'user', 1, 1, NOW(), NOW()),
('khalid_user', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'خالد', 'محمد', '+966500000000', '1989-12-08', 'male', 'user', 1, 1, NOW(), NOW());

-- إضافة 20 موعد مختلف (مؤجلة، مكتملة، ملغية)
INSERT INTO appointments (client_id, therapist_id, appointment_date, appointment_time, status, notes, created_at, updated_at) VALUES
-- مواعيد مؤجلة (pending)
(11, 2, '2024-01-15', '10:00:00', 'pending', 'جلسة استشارية أولية - مشاكل في النوم', NOW(), NOW()),
(12, 3, '2024-01-16', '14:30:00', 'pending', 'جلسة علاج سلوكي - قلق اجتماعي', NOW(), NOW()),
(13, 4, '2024-01-17', '16:00:00', 'pending', 'جلسة دعم نفسي - ضغوط العمل', NOW(), NOW()),
(14, 5, '2024-01-18', '11:30:00', 'pending', 'جلسة استشارة أسرية', NOW(), NOW()),
(15, 6, '2024-01-19', '13:00:00', 'pending', 'جلسة علاج معرفي - اكتئاب خفيف', NOW(), NOW()),

-- مواعيد مكتملة (completed)
(16, 7, '2024-01-10', '09:00:00', 'completed', 'جلسة مكتملة - تحسن في الحالة النفسية', NOW(), NOW()),
(17, 8, '2024-01-11', '15:00:00', 'completed', 'جلسة مكتملة - تم التغلب على القلق', NOW(), NOW()),
(18, 9, '2024-01-12', '17:30:00', 'completed', 'جلسة مكتملة - تحسن في العلاقات الاجتماعية', NOW(), NOW()),
(19, 10, '2024-01-13', '10:30:00', 'completed', 'جلسة مكتملة - تحسن في النوم', NOW(), NOW()),
(20, 2, '2024-01-14', '14:00:00', 'completed', 'جلسة مكتملة - تحسن في الثقة بالنفس', NOW(), NOW()),

-- مواعيد ملغية (cancelled)
(11, 3, '2024-01-05', '11:00:00', 'cancelled', 'تم الإلغاء من قبل العميل - ظروف طارئة', NOW(), NOW()),
(12, 4, '2024-01-06', '16:30:00', 'cancelled', 'تم الإلغاء من قبل المعالج - مرض مفاجئ', NOW(), NOW()),
(13, 5, '2024-01-07', '12:00:00', 'cancelled', 'تم الإلغاء من قبل العميل - سفر مفاجئ', NOW(), NOW()),
(14, 6, '2024-01-08', '15:00:00', 'cancelled', 'تم الإلغاء من قبل المعالج - ظروف عائلية', NOW(), NOW()),
(15, 7, '2024-01-09', '13:30:00', 'cancelled', 'تم الإلغاء من قبل العميل - مشاكل تقنية', NOW(), NOW()),

-- مواعيد مؤكدة (confirmed)
(16, 8, '2024-01-20', '09:30:00', 'confirmed', 'جلسة مؤكدة - استشارة نفسية', NOW(), NOW()),
(17, 9, '2024-01-21', '14:00:00', 'confirmed', 'جلسة مؤكدة - علاج سلوكي', NOW(), NOW()),
(18, 10, '2024-01-22', '16:30:00', 'confirmed', 'جلسة مؤكدة - دعم نفسي', NOW(), NOW()),
(19, 2, '2024-01-23', '11:00:00', 'confirmed', 'جلسة مؤكدة - استشارة أسرية', NOW(), NOW()),
(20, 3, '2024-01-24', '15:30:00', 'confirmed', 'جلسة مؤكدة - علاج معرفي', NOW(), NOW());

-- عرض البيانات المضافة للتأكد
SELECT '=== المعالجين ===' as info;
SELECT id, username, email, first_name, last_name, user_type, is_active FROM users WHERE user_type = 'therapist';

SELECT '=== العملاء ===' as info;
SELECT id, username, email, first_name, last_name, user_type, is_active FROM users WHERE user_type = 'user';

SELECT '=== المواعيد ===' as info;
SELECT 
    a.id,
    CONCAT(c.first_name, ' ', c.last_name) as client_name,
    CONCAT(t.first_name, ' ', t.last_name) as therapist_name,
    a.appointment_date,
    a.appointment_time,
    a.status,
    a.notes
FROM appointments a
JOIN users c ON a.client_id = c.id
JOIN users t ON a.therapist_id = t.id
ORDER BY a.appointment_date, a.appointment_time;

SELECT '=== إحصائيات المواعيد ===' as info;
SELECT 
    status,
    COUNT(*) as count
FROM appointments
GROUP BY status; 