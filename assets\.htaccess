# منصة نفسي - إعدادات Apache لمجلد الأصول

# إعدادات MIME Types
AddType text/css .css
AddType application/javascript .js
AddType image/png .png
AddType image/jpg .jpg
AddType image/jpeg .jpeg
AddType image/gif .gif
AddType image/svg+xml .svg
AddType image/x-icon .ico
AddType font/woff .woff
AddType font/woff2 .woff2
AddType font/ttf .ttf
AddType font/eot .eot

# السماح بالوصول للملفات الثابتة
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# إعدادات التخزين المؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule> 