<?php
/**
 * نقطة دخول التطبيق الرئيسية
 * Main Application Entry Point
 */

// تعريف ثوابت التطبيق (إذا لم تكن معرفة مسبقاً)
if (!defined('NAFSI_APP')) {
    define('NAFSI_APP', true);
}
if (!defined('APP_ROOT')) {
    define('APP_ROOT', __DIR__ . '/app');
}
if (!defined('PUBLIC_ROOT')) {
    define('PUBLIC_ROOT', __DIR__ . '/public');
}

// تضمين ملف التهيئة
require_once APP_ROOT . '/init.php';

// معالجة الطلبات
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// إزالة query string من URI
$requestUri = strtok($requestUri, '?');

// إزالة مسار المشروع من URI إذا كان موجوداً
$projectPath = '/nafsi_platform';
if (strpos($requestUri, $projectPath) === 0) {
    $requestUri = substr($requestUri, strlen($projectPath));
}

$requestUri = trim($requestUri, '/');

// معالجة طلبات المعالجات (handlers)
if (!empty($requestUri) && strpos($requestUri, 'handlers/') === 0) {
    $handlerPath = str_replace('handlers/', '', $requestUri);
    $handlerFile = APP_ROOT . '/handlers/' . $handlerPath . '.php';

    if (file_exists($handlerFile)) {
        require_once $handlerFile;
        exit;
    } else {
        http_response_code(404);
        die('Handler not found');
    }
}

// معالجة طلبات لوحة التحكم للمدير
if (!empty($requestUri) && strpos($requestUri, 'admin') === 0) {
    $adminPath = str_replace('admin', '', $requestUri);
    $adminPath = trim($adminPath, '/');
    
    // معالجة المسارات مع المعاملات مثل admin/appointment/25
    $pathParts = explode('/', $adminPath);
    $mainPath = $pathParts[0] ?: 'dashboard';
    
    // استخراج المعاملات من المسار
    $params = [];
    if (count($pathParts) > 1) {
        for ($i = 1; $i < count($pathParts); $i += 2) {
            if (isset($pathParts[$i + 1])) {
                $params[$pathParts[$i]] = $pathParts[$i + 1];
            } else {
                $params['id'] = $pathParts[$i];
            }
        }
    }
    
    $adminFile = PUBLIC_ROOT . '/admin/' . $mainPath . '.php';

    if (file_exists($adminFile)) {
        require_once $adminFile;
        exit;
    } else {
        http_response_code(404);
        loadPage('404', ['pageTitle' => 'الصفحة غير موجودة']);
        exit;
    }
}

// معالجة طلبات لوحة التحكم للمعالجين
if (!empty($requestUri) && strpos($requestUri, 'therapist') === 0) {
    $therapistPath = str_replace('therapist', '', $requestUri);
    $therapistPath = trim($therapistPath, '/');
    $therapistPath = $therapistPath ?: 'dashboard';

    $therapistFile = PUBLIC_ROOT . '/therapist/' . $therapistPath . '.php';

    if (file_exists($therapistFile)) {
        require_once $therapistFile;
        exit;
    } else {
        http_response_code(404);
        loadPage('404', ['pageTitle' => 'الصفحة غير موجودة']);
        exit;
    }
}

// تحديد الصفحة المطلوبة
$page = $_GET['page'] ?? $requestUri;

// إذا كان المسار فارغ، اجعله home
if (empty($page)) {
    $page = 'home';
}

// إزالة .php من اسم الصفحة إذا كان موجوداً
$page = str_replace('.php', '', $page);

// قائمة الصفحات المسموح بها
$allowedPages = [
    'home' => 'الصفحة الرئيسية',
    'about' => 'عن المنصة',
    'contact' => 'التواصل',
    'login' => 'تسجيل الدخول',
    'register' => 'إنشاء حساب',
    'library' => 'المكتبة التعليمية',
    'specialists' => 'الأخصائيين',
    'privacy' => 'سياسة الخصوصية',
    'terms' => 'الشروط والأحكام',
    'dashboard' => 'لوحة التحكم',
    'profile' => 'الملف الشخصي',
    'logout' => 'تسجيل الخروج',
    'forgot_password' => 'نسيت كلمة المرور',
    'specialist_profile' => 'ملف الأخصائي',
    'specialist_application' => 'تقديم طلب انضمام',
    'article' => 'مقال',
    'video' => 'فيديو'
];

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // معالجة طلبات تسجيل الدخول
    if ($page === 'login') {
        require_once APP_ROOT . '/handlers/login_handler.php';
        exit;
    }
    // معالجة طلبات أخرى يمكن إضافتها هنا
}

// التحقق من صحة الصفحة المطلوبة
if (!array_key_exists($page, $allowedPages)) {
    $page = '404';
}

// تحميل الصفحة المطلوبة
loadPage($page, [
    'pageTitle' => $allowedPages[$page] ?? 'الصفحة غير موجودة',
    'currentPage' => $page
]);
?>