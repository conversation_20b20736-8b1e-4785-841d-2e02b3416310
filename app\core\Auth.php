<?php
/**
 * كلاس المصادقة
 * Authentication Class
 * 
 * هذا الكلاس يتعامل مع جميع عمليات المصادقة
 * بما في ذلك تسجيل الدخول، تسجيل الخروج،
 * التحقق من الصلاحيات، وإدارة الرموز
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Auth {
    private $db;
    private $session;
    private $user;
    
    /**
     * أنواع المستخدمين
     */
    const USER_TYPE_REGULAR = 'regular';
    const USER_TYPE_SPECIALIST = 'specialist';
    const USER_TYPE_ADMIN = 'admin';
    
    /**
     * حالات تسجيل الدخول
     */
    const LOGIN_SUCCESS = 'success';
    const LOGIN_INVALID_CREDENTIALS = 'invalid_credentials';
    const LOGIN_ACCOUNT_DISABLED = 'account_disabled';
    const LOGIN_TOO_MANY_ATTEMPTS = 'too_many_attempts';
    const LOGIN_ACCOUNT_LOCKED = 'account_locked';
    
    public function __construct() {
        $this->db = db();
        $this->session = new Session();
        $this->user = new User();
    }
    
    /**
     * تسجيل الدخول
     * @param string $email البريد الإلكتروني
     * @param string $password كلمة المرور
     * @param bool $rememberMe تذكرني
     * @return array
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            // التحقق من عدد محاولات تسجيل الدخول
            if ($this->isLoginBlocked($email)) {
                return [
                    'status' => self::LOGIN_TOO_MANY_ATTEMPTS,
                    'message' => 'تم حظر تسجيل الدخول مؤقتاً بسبب كثرة المحاولات'
                ];
            }
            
            // التحقق من صحة البيانات
            if (empty($email) || empty($password)) {
                return [
                    'status' => self::LOGIN_INVALID_CREDENTIALS,
                    'message' => 'البريد الإلكتروني وكلمة المرور مطلوبان'
                ];
            }
            
            // البحث عن المستخدم
            $user = $this->user->getByEmail($email);
            if (!$user) {
                $this->logFailedLogin($email);
                return [
                    'status' => self::LOGIN_INVALID_CREDENTIALS,
                    'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
                ];
            }
            
            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password'])) {
                $this->logFailedLogin($email);
                return [
                    'status' => self::LOGIN_INVALID_CREDENTIALS,
                    'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
                ];
            }
            
            // التحقق من حالة الحساب
            if ($user['status'] !== 'active') {
                return [
                    'status' => self::LOGIN_ACCOUNT_DISABLED,
                    'message' => 'الحساب غير مفعل أو محظور'
                ];
            }
            
            // إنشاء الجلسة
            $this->createSession($user, $rememberMe);
            
            // تسجيل تسجيل الدخول الناجح
            $this->logSuccessfulLogin($user['id']);
            
            // إعادة تعيين محاولات تسجيل الدخول الفاشلة
            $this->resetFailedLoginAttempts($email);
            
            return [
                'status' => self::LOGIN_SUCCESS,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $user,
                'redirect' => $this->getRedirectUrl($user['user_type'])
            ];
            
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => 'حدث خطأ أثناء تسجيل الدخول'
            ];
        }
    }
    
    /**
     * تسجيل الخروج
     * @return bool
     */
    public function logout() {
        try {
            // حذف الجلسة
            $this->session->destroy();
            
            // حذف رموز "تذكرني"
            if (isset($_COOKIE['remember_token'])) {
                $this->deleteRememberToken($_COOKIE['remember_token']);
                setcookie('remember_token', '', time() - 3600, '/');
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Logout Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من تسجيل الدخول
     * @return bool
     */
    public function isLoggedIn() {
        // التحقق من الجلسة
        if ($this->session->get('user_id')) {
            return true;
        }
        
        // التحقق من رموز "تذكرني"
        if (isset($_COOKIE['remember_token'])) {
            return $this->validateRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     * @return array|false
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $userId = $this->session->get('user_id');
        if (!$userId) {
            // محاولة الحصول من رموز "تذكرني"
            if (isset($_COOKIE['remember_token'])) {
                $userId = $this->getUserIdFromRememberToken($_COOKIE['remember_token']);
            }
        }
        
        if ($userId) {
            return $this->user->getById($userId);
        }
        
        return false;
    }
    
    /**
     * التحقق من الصلاحيات
     * @param string $permission الصلاحية المطلوبة
     * @return bool
     */
    public function hasPermission($permission) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        // المشرفون لديهم جميع الصلاحيات
        if ($user['user_type'] === self::USER_TYPE_ADMIN) {
            return true;
        }
        
        // التحقق من الصلاحيات حسب نوع المستخدم
        $permissions = $this->getUserPermissions($user['user_type']);
        
        return in_array($permission, $permissions);
    }
    
    /**
     * التحقق من نوع المستخدم
     * @param string $userType نوع المستخدم المطلوب
     * @return bool
     */
    public function isUserType($userType) {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }
        
        return $user['user_type'] === $userType;
    }
    
    /**
     * حماية الصفحة
     * @param string $requiredPermission الصلاحية المطلوبة
     * @param string $redirectUrl رابط إعادة التوجيه
     */
    public function requirePermission($requiredPermission, $redirectUrl = '/login') {
        if (!$this->isLoggedIn()) {
            setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
            redirect('login');
        }

        if (!$this->hasPermission($requiredPermission)) {
            setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
            redirect('dashboard');
        }
    }
    
    /**
     * حماية الصفحة حسب نوع المستخدم
     * @param string $requiredUserType نوع المستخدم المطلوب
     * @param string $redirectUrl رابط إعادة التوجيه
     */
    public function requireUserType($requiredUserType, $redirectUrl = '/login') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirectUrl");
            exit;
        }
        
        if (!$this->isUserType($requiredUserType)) {
            header("Location: /403");
            exit;
        }
    }
    
    /**
     * إنشاء جلسة جديدة
     * @param array $user بيانات المستخدم
     * @param bool $rememberMe تذكرني
     */
    private function createSession($user, $rememberMe = false) {
        // إنشاء الجلسة
        $this->session->set('user_id', $user['id']);
        $this->session->set('user_type', $user['user_type']);
        $this->session->set('user_name', $user['first_name'] . ' ' . $user['last_name']);
        $this->session->set('login_time', time());
        
        // إنشاء رمز "تذكرني" إذا كان مطلوباً
        if ($rememberMe) {
            $token = $this->createRememberToken($user['id']);
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
        }
        
        // تسجيل نشاط تسجيل الدخول
        $this->logUserActivity($user['id'], 'login');
    }
    
    /**
     * إنشاء رمز "تذكرني"
     * @param int $userId معرف المستخدم
     * @return string
     */
    private function createRememberToken($userId) {
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60));
        
        $sql = "INSERT INTO remember_tokens (user_id, token, expires_at) 
                VALUES (:user_id, :token, :expires_at)";
        
        $params = [
            ':user_id' => $userId,
            ':token' => $token,
            ':expires_at' => $expires
        ];
        
        $this->db->insert($sql, $params);
        
        return $token;
    }
    
    /**
     * التحقق من صحة رمز "تذكرني"
     * @param string $token الرمز
     * @return bool
     */
    private function validateRememberToken($token) {
        $sql = "SELECT user_id FROM remember_tokens 
                WHERE token = :token AND expires_at > NOW()";
        
        $result = $this->db->selectOne($sql, [':token' => $token]);
        
        if ($result) {
            // إنشاء جلسة جديدة
            $user = $this->user->getById($result['user_id']);
            if ($user && $user['status'] === 'active') {
                $this->createSession($user, false);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * الحصول على معرف المستخدم من رمز "تذكرني"
     * @param string $token الرمز
     * @return int|false
     */
    private function getUserIdFromRememberToken($token) {
        $sql = "SELECT user_id FROM remember_tokens 
                WHERE token = :token AND expires_at > NOW()";
        
        $result = $this->db->selectOne($sql, [':token' => $token]);
        
        return $result ? $result['user_id'] : false;
    }
    
    /**
     * حذف رمز "تذكرني"
     * @param string $token الرمز
     */
    private function deleteRememberToken($token) {
        $sql = "DELETE FROM remember_tokens WHERE token = :token";
        $this->db->execute($sql, [':token' => $token]);
    }
    
    /**
     * التحقق من حظر تسجيل الدخول
     * @param string $email البريد الإلكتروني
     * @return bool
     */
    private function isLoginBlocked($email) {
        $sql = "SELECT COUNT(*) as attempts FROM failed_logins 
                WHERE email = :email AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)";
        
        $result = $this->db->selectOne($sql, [':email' => $email]);
        
        return $result['attempts'] >= 5; // 5 محاولات في 15 دقيقة
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     * @param string $email البريد الإلكتروني
     */
    private function logFailedLogin($email) {
        $sql = "INSERT INTO failed_logins (email, ip_address, user_agent, created_at) 
                VALUES (:email, :ip_address, :user_agent, NOW())";
        
        $params = [
            ':email' => $email,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * تسجيل تسجيل دخول ناجح
     * @param int $userId معرف المستخدم
     */
    private function logSuccessfulLogin($userId) {
        $sql = "INSERT INTO login_logs (user_id, ip_address, user_agent, created_at) 
                VALUES (:user_id, :ip_address, :user_agent, NOW())";
        
        $params = [
            ':user_id' => $userId,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     * @param string $email البريد الإلكتروني
     */
    private function resetFailedLoginAttempts($email) {
        $sql = "DELETE FROM failed_logins WHERE email = :email";
        $this->db->execute($sql, [':email' => $email]);
    }
    
    /**
     * تسجيل نشاط المستخدم
     * @param int $userId معرف المستخدم
     * @param string $activity النشاط
     */
    private function logUserActivity($userId, $activity) {
        $sql = "INSERT INTO user_activities (user_id, activity, ip_address, user_agent, created_at) 
                VALUES (:user_id, :activity, :ip_address, :user_agent, NOW())";
        
        $params = [
            ':user_id' => $userId,
            ':activity' => $activity,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * الحصول على رابط إعادة التوجيه حسب نوع المستخدم
     * @param string $userType نوع المستخدم
     * @return string
     */
    private function getRedirectUrl($userType) {
        switch ($userType) {
            case 'admin':
                return 'admin/dashboard';
            case 'therapist':
                return 'therapist/dashboard';
            case 'user':
            default:
                return 'dashboard';
        }
    }
    
    /**
     * الحصول على صلاحيات المستخدم حسب نوعه
     * @param string $userType نوع المستخدم
     * @return array
     */
    private function getUserPermissions($userType) {
        $permissions = [
            self::USER_TYPE_REGULAR => [
                'view_profile',
                'edit_profile',
                'book_session',
                'view_specialists',
                'rate_specialist',
                'view_content'
            ],
            self::USER_TYPE_SPECIALIST => [
                'view_profile',
                'edit_profile',
                'manage_schedule',
                'view_bookings',
                'manage_specialist_profile',
                'view_earnings'
            ],
            self::USER_TYPE_ADMIN => [
                'manage_users',
                'manage_specialists',
                'manage_content',
                'view_reports',
                'manage_system',
                'manage_settings'
            ]
        ];
        
        return $permissions[$userType] ?? [];
    }
    
    /**
     * إنشاء رمز JWT
     * @param array $payload البيانات
     * @param int $expiration مدة الصلاحية (بالثواني)
     * @return string
     */
    public function createJWT($payload, $expiration = 3600) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload['exp'] = time() + $expiration;
        $payload['iat'] = time();
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, JWT_SECRET, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * التحقق من صحة رمز JWT
     * @param string $token الرمز
     * @return array|false
     */
    public function validateJWT($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        $validSignature = hash_hmac('sha256', $header . "." . $payload, JWT_SECRET, true);
        $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
        
        if ($signature !== $validSignature) {
            return false;
        }
        
        $payloadData = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        if (!$payloadData || !isset($payloadData['exp']) || $payloadData['exp'] < time()) {
            return false;
        }
        
        return $payloadData;
    }
    
    /**
     * استعادة كلمة المرور
     * @param string $email البريد الإلكتروني
     * @return bool
     */
    public function requestPasswordReset($email) {
        $user = $this->user->getByEmail($email);
        if (!$user) {
            return false;
        }
        
        // إنشاء رمز استعادة كلمة المرور
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + (60 * 60)); // ساعة واحدة
        
        $sql = "INSERT INTO password_resets (user_id, token, expires_at) 
                VALUES (:user_id, :token, :expires_at)";
        
        $params = [
            ':user_id' => $user['id'],
            ':token' => $token,
            ':expires_at' => $expires
        ];
        
        $result = $this->db->insert($sql, $params);
        
        if ($result) {
            // إرسال رسالة استعادة كلمة المرور
            $this->sendPasswordResetEmail($user['email'], $user['first_name'], $token);
            return true;
        }
        
        return false;
    }
    
    /**
     * إعادة تعيين كلمة المرور
     * @param string $token الرمز
     * @param string $newPassword كلمة المرور الجديدة
     * @return bool
     */
    public function resetPassword($token, $newPassword) {
        $sql = "SELECT user_id FROM password_resets 
                WHERE token = :token AND expires_at > NOW() AND used = 0";
        
        $result = $this->db->selectOne($sql, [':token' => $token]);
        
        if (!$result) {
            return false;
        }
        
        // تحديث كلمة المرور
        $success = $this->user->changePassword($result['user_id'], '', $newPassword);
        
        if ($success) {
            // تحديث حالة الرمز
            $sql = "UPDATE password_resets SET used = 1 WHERE token = :token";
            $this->db->execute($sql, [':token' => $token]);
            return true;
        }
        
        return false;
    }
    
    /**
     * إرسال رسالة استعادة كلمة المرور
     * @param string $email البريد الإلكتروني
     * @param string $firstName الاسم الأول
     * @param string $token الرمز
     */
    private function sendPasswordResetEmail($email, $firstName, $token) {
        // سيتم تنفيذ هذا لاحقاً مع نظام البريد الإلكتروني
        // TODO: إرسال رسالة استعادة كلمة المرور
    }
}
?> 