<?php
/**
 * الشريط الجانبي للوحة تحكم المدير
 * Admin Dashboard Sidebar
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// الحصول على الصفحة الحالية
$currentPage = $_GET['page'] ?? 'dashboard';
$currentSection = $_GET['section'] ?? '';

// قائمة روابط التنقل
$navItems = [
    'dashboard' => [
        'title' => 'لوحة التحكم',
        'icon' => 'fas fa-tachometer-alt',
        'url' => url('admin/dashboard'),
        'active' => $currentPage === 'dashboard'
    ],
    'users' => [
        'title' => 'إدارة المستخدمين',
        'icon' => 'fas fa-users',
        'url' => url('admin/users'),
        'active' => $currentPage === 'users'
    ],
    'specialists' => [
        'title' => 'إدارة الأخصائيين',
        'icon' => 'fas fa-user-md',
        'url' => url('admin/specialists'),
        'active' => $currentPage === 'specialists'
    ],
    'appointments' => [
        'title' => 'إدارة المواعيد',
        'icon' => 'fas fa-calendar-alt',
        'url' => url('admin/appointments'),
        'active' => $currentPage === 'appointments'
    ],
    'add_appointment' => [
        'title' => 'إضافة موعد جديد',
        'icon' => 'fas fa-plus-circle',
        'url' => url('admin/add_appointment'),
        'active' => $currentPage === 'add_appointment'
    ],
    'contact_messages' => [
        'title' => 'رسائل التواصل',
        'icon' => 'fas fa-envelope',
        'url' => url('admin/contact_messages'),
        'active' => $currentPage === 'contact_messages'
    ],
    'content' => [
        'title' => 'إدارة المحتوى',
        'icon' => 'fas fa-file-alt',
        'url' => url('admin/content'),
        'active' => $currentPage === 'content'
    ],
    'library' => [
        'title' => 'إدارة المكتبة',
        'icon' => 'fas fa-book',
        'url' => url('admin/library'),
        'active' => $currentPage === 'library'
    ],
    'reports' => [
        'title' => 'التقارير',
        'icon' => 'fas fa-chart-bar',
        'url' => url('admin/reports'),
        'active' => $currentPage === 'reports'
    ],
    'settings' => [
        'title' => 'إعدادات النظام',
        'icon' => 'fas fa-cog',
        'url' => url('admin/settings'),
        'active' => $currentPage === 'settings'
    ],
    'profile' => [
        'title' => 'الملف الشخصي',
        'icon' => 'fas fa-user',
        'url' => url('admin/profile'),
        'active' => $currentPage === 'profile'
    ]
];

// الحصول على معلومات المستخدم الحالي
$currentUser = $_SESSION['user'] ?? null;

// التأكد من وجود معلومات المستخدم
if (!$currentUser) {
    $currentUser = [
        'first_name' => 'المدير',
        'email' => '<EMAIL>',
        'user_type' => 'admin'
    ];
}
?>

    <link href="<?= asset('css/admin-dashboard.css') ?>" rel="stylesheet">

<nav class="admin-sidebar" id="adminSidebar">
    <!-- رأس الشريط الجانبي -->
    <div class="admin-sidebar-header">
        <div class="logo-icon">
            <i class="fas fa-brain"></i>
        </div>
        <h4>منصة نفسي</h4>
        <small style="color: rgba(255,255,255,0.7);">لوحة تحكم المدير</small>
    </div>

    <!-- قائمة التنقل -->
    <div class="admin-sidebar-nav">
        <?php foreach ($navItems as $key => $item): ?>
            <div class="nav-item">
                <a href="<?= $item['url'] ?>" class="nav-link <?= $item['active'] ? 'active' : '' ?>">
                    <i class="<?= $item['icon'] ?>"></i>
                    <span><?= $item['title'] ?></span>
                    
                    <?php if ($key === 'contact_messages'): ?>
                        <!-- شارة التنبيه للرسائل الجديدة -->
                        <?php
                        try {
                            $db = Database::getInstance();
                            $newMessages = $db->select("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
                            $newCount = $newMessages[0]['count'] ?? 0;
                            if ($newCount > 0):
                        ?>
                            <span class="notification-badge"><?= $newCount ?></span>
                        <?php endif; ?>
                        <?php } catch (Exception $e) {} ?>
                    <?php endif; ?>
                </a>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- معلومات المستخدم -->
    <div class="admin-sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-details">
                <div class="user-name">
                    <?= htmlspecialchars($currentUser['first_name'] ?? 'المدير') ?>
                </div>
                <div class="user-role">مدير النظام</div>
            </div>
        </div>
        
        <div class="mt-2">
            <a href="<?= url('admin/profile') ?>" class="btn btn-sm btn-outline-light me-2">
                <i class="fas fa-user-edit"></i> <span>الملف الشخصي</span>
            </a>
            <a href="<?= url('logout') ?>" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>
</nav>

<!-- زر إخفاء/إظهار الشريط الجانبي -->
<button class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebar()">
    <i class="fas fa-bars"></i>
</button>

<script>
// دالة إخفاء/إظهار الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    const toggle = document.getElementById('sidebarToggle');
    const mainContent = document.querySelector('.admin-main-content');
    
    sidebar.classList.toggle('collapsed');
    toggle.classList.toggle('collapsed');
    
    if (mainContent) {
        mainContent.classList.toggle('sidebar-collapsed');
    }
    
    // حفظ الحالة في localStorage
    const isCollapsed = sidebar.classList.contains('collapsed');
    localStorage.setItem('adminSidebarCollapsed', isCollapsed);
}

// دالة للشاشات الصغيرة
function toggleMobileSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('mobile-open');
}

// استعادة حالة الشريط الجانبي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('adminSidebar');
    const toggle = document.getElementById('sidebarToggle');
    const mainContent = document.querySelector('.admin-main-content');
    const isCollapsed = localStorage.getItem('adminSidebarCollapsed') === 'true';
    
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        toggle.classList.add('collapsed');
        if (mainContent) {
            mainContent.classList.add('sidebar-collapsed');
        }
    }
    
    // إخفاء الشريط الجانبي على الشاشات الصغيرة
    handleResize();
});

// معالجة تغيير حجم النافذة
function handleResize() {
    const sidebar = document.getElementById('adminSidebar');
    const toggle = document.getElementById('sidebarToggle');
    
    if (window.innerWidth <= 768) {
        // على الشاشات الصغيرة، إخفاء الشريط الجانبي افتراضياً
        if (!sidebar.classList.contains('mobile-open')) {
            sidebar.style.transform = 'translateX(100%)';
        }
        
        // تعديل موضع زر التبديل
        toggle.style.right = '20px';
        toggle.style.top = '20px';
    } else {
        // على الشاشات الكبيرة، إعادة تعيين الموضع
        sidebar.style.transform = '';
        toggle.style.right = '';
        toggle.style.top = '';
    }
}

// إضافة مستمع لتغيير حجم النافذة
window.addEventListener('resize', handleResize);

// إضافة مستمع للنقر خارج الشريط الجانبي على الشاشات الصغيرة
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('adminSidebar');
    const toggle = document.getElementById('sidebarToggle');
    
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
            sidebar.classList.remove('mobile-open');
        }
    }
});

// تحسين الأداء عند التمرير
let scrollTimeout;
window.addEventListener('scroll', function() {
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }
    
    scrollTimeout = setTimeout(function() {
        // يمكن إضافة تحسينات إضافية هنا
    }, 100);
});
</script> 