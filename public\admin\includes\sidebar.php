<?php
/**
 * الشريط الجانبي الجديد للوحة تحكم المدير
 * New Admin Dashboard Sidebar
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// الحصول على الصفحة الحالية
$currentPage = $_GET['page'] ?? 'dashboard';
$currentSection = $_GET['section'] ?? '';

// قائمة روابط التنقل
$navItems = [
    'dashboard' => [
        'title' => 'لوحة التحكم',
        'icon' => 'fas fa-tachometer-alt',
        'url' => url('admin/dashboard'),
        'active' => $currentPage === 'dashboard'
    ],
    'users' => [
        'title' => 'إدارة المستخدمين',
        'icon' => 'fas fa-users',
        'url' => url('admin/users'),
        'active' => $currentPage === 'users'
    ],
    'specialists' => [
        'title' => 'إدارة الأخصائيين',
        'icon' => 'fas fa-user-md',
        'url' => url('admin/specialists'),
        'active' => $currentPage === 'specialists'
    ],
    'appointments' => [
        'title' => 'إدارة المواعيد',
        'icon' => 'fas fa-calendar-alt',
        'url' => url('admin/appointments'),
        'active' => $currentPage === 'appointments'
    ],
    'add_appointment' => [
        'title' => 'إضافة موعد جديد',
        'icon' => 'fas fa-plus-circle',
        'url' => url('admin/add_appointment'),
        'active' => $currentPage === 'add_appointment'
    ],
    'contact_messages' => [
        'title' => 'رسائل التواصل',
        'icon' => 'fas fa-envelope',
        'url' => url('admin/contact_messages'),
        'active' => $currentPage === 'contact_messages'
    ],
    'content' => [
        'title' => 'إدارة المحتوى',
        'icon' => 'fas fa-file-alt',
        'url' => url('admin/content'),
        'active' => $currentPage === 'content'
    ],
    'library' => [
        'title' => 'إدارة المكتبة',
        'icon' => 'fas fa-book',
        'url' => url('admin/library'),
        'active' => $currentPage === 'library'
    ],
    'reports' => [
        'title' => 'التقارير',
        'icon' => 'fas fa-chart-bar',
        'url' => url('admin/reports'),
        'active' => $currentPage === 'reports'
    ],
    'settings' => [
        'title' => 'إعدادات النظام',
        'icon' => 'fas fa-cog',
        'url' => url('admin/settings'),
        'active' => $currentPage === 'settings'
    ],
    'profile' => [
        'title' => 'الملف الشخصي',
        'icon' => 'fas fa-user',
        'url' => url('admin/profile'),
        'active' => $currentPage === 'profile'
    ]
];

// الحصول على معلومات المستخدم الحالي
$currentUser = $_SESSION['user'] ?? null;

// التأكد من وجود معلومات المستخدم
if (!$currentUser) {
    $currentUser = [
        'first_name' => 'المدير',
        'email' => '<EMAIL>',
        'user_type' => 'admin'
    ];
}

// الحصول على عدد الرسائل الجديدة
$newMessagesCount = 0;
try {
    $db = Database::getInstance();
    $newMessages = $db->select("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
    $newMessagesCount = $newMessages[0]['count'] ?? 0;
} catch (Exception $e) {
    $newMessagesCount = 0;
}
?>

<!-- تضمين ملف CSS الخاص بالشريط الجانبي -->
<link href="<?= asset('css/admin-sidebar.css') ?>" rel="stylesheet">

<!-- خلفية ضبابية للشاشات الصغيرة -->
<div class="sidebar-backdrop" id="sidebarBackdrop"></div>

<!-- الشريط الجانبي الجديد -->
<nav class="admin-sidebar" id="adminSidebar">
    <!-- رأس الشريط الجانبي -->
    <div class="sidebar-header">
        <div class="logo">
            <i class="fas fa-brain"></i>
        </div>
        <h3>منصة نفسي</h3>
        <p>لوحة تحكم المدير</p>
    </div>

    <!-- قائمة التنقل -->
    <div class="sidebar-nav">
        <?php foreach ($navItems as $key => $item): ?>
            <div class="nav-item">
                <a href="<?= $item['url'] ?>" class="nav-link <?= $item['active'] ? 'active' : '' ?>">
                    <i class="<?= $item['icon'] ?>"></i>
                    <span><?= $item['title'] ?></span>
                    
                    <?php if ($key === 'contact_messages' && $newMessagesCount > 0): ?>
                        <span class="notification-badge"><?= $newMessagesCount ?></span>
                    <?php endif; ?>
                </a>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- قسم المستخدم -->
    <div class="sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="user-details">
                <div class="user-name">
                    <?= htmlspecialchars($currentUser['first_name'] ?? 'المدير') ?>
                </div>
                <div class="user-role">مدير النظام</div>
            </div>
        </div>
        
        <a href="<?= url('logout') ?>" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
        </a>
    </div>
</nav>

<!-- زر إخفاء/إظهار الشريط الجانبي -->
<button class="sidebar-toggle" id="sidebarToggle">
    <i class="fas fa-bars"></i>
</button>

<script>
// متغيرات عامة
const sidebar = document.getElementById('adminSidebar');
const toggle = document.getElementById('sidebarToggle');
const backdrop = document.getElementById('sidebarBackdrop');
const mainContent = document.querySelector('.admin-main-content');

// دالة إخفاء/إظهار الشريط الجانبي للشاشات الكبيرة
function toggleSidebar() {
    sidebar.classList.toggle('collapsed');
    toggle.classList.toggle('collapsed');

    if (mainContent) {
        mainContent.classList.toggle('sidebar-collapsed');
    }

    // حفظ الحالة في localStorage
    const isCollapsed = sidebar.classList.contains('collapsed');
    localStorage.setItem('adminSidebarCollapsed', isCollapsed);
}

// دالة للشاشات الصغيرة
function toggleMobileSidebar() {
    sidebar.classList.toggle('mobile-open');
    backdrop.classList.toggle('show');

    // تحديث أيقونة الزر
    const icon = toggle.querySelector('i');
    if (sidebar.classList.contains('mobile-open')) {
        icon.className = 'fas fa-times';
        document.body.style.overflow = 'hidden'; // منع التمرير
    } else {
        icon.className = 'fas fa-bars';
        document.body.style.overflow = ''; // إعادة التمرير
    }
}

// دالة إغلاق الشريط الجانبي على الشاشات الصغيرة
function closeMobileSidebar() {
    if (window.innerWidth <= 768) {
        sidebar.classList.remove('mobile-open');
        backdrop.classList.remove('show');

        // إعادة تعيين أيقونة الزر
        const icon = toggle.querySelector('i');
        icon.className = 'fas fa-bars';

        // إعادة التمرير
        document.body.style.overflow = '';
    }
}

// معالجة تغيير حجم النافذة
function handleResize() {
    if (window.innerWidth <= 768) {
        // على الشاشات الصغيرة، تغيير وظيفة الزر
        toggle.onclick = toggleMobileSidebar;
    } else {
        // على الشاشات الكبيرة، استعادة الوظيفة الأصلية
        toggle.onclick = toggleSidebar;

        // إزالة كلاس mobile-open والخلفية الضبابية
        sidebar.classList.remove('mobile-open');
        backdrop.classList.remove('show');

        // إعادة تعيين أيقونة الزر
        const icon = toggle.querySelector('i');
        icon.className = 'fas fa-bars';

        // إعادة التمرير
        document.body.style.overflow = '';
    }
}

// استعادة حالة الشريط الجانبي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const isCollapsed = localStorage.getItem('adminSidebarCollapsed') === 'true';

    if (isCollapsed && window.innerWidth > 768) {
        sidebar.classList.add('collapsed');
        toggle.classList.add('collapsed');
        if (mainContent) {
            mainContent.classList.add('sidebar-collapsed');
        }
    }

    // تطبيق معالجة الحجم
    handleResize();
});

// إضافة مستمع لتغيير حجم النافذة
window.addEventListener('resize', handleResize);

// إضافة مستمع للنقر خارج الشريط الجانبي على الشاشات الصغيرة
document.addEventListener('click', function(event) {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
            closeMobileSidebar();
        }
    }
});

// إضافة مستمع للنقر على الخلفية الضبابية
backdrop.addEventListener('click', closeMobileSidebar);

// تحسين الأداء عند التمرير
let scrollTimeout;
window.addEventListener('scroll', function() {
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }

    scrollTimeout = setTimeout(function() {
        // يمكن إضافة تحسينات إضافية هنا
    }, 100);
});

console.log('✅ الشريط الجانبي الجديد تم تحميله بنجاح!');
</script>
