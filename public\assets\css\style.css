/* ========================================
   منصة نفسي - التنسيق العام
   Nafsi Platform - Main Stylesheet
   ======================================== */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* تنسيق Header */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 0.5rem;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    transform: translateY(-2px);
}

.navbar-toggler {
    border: none;
    color: white;
}

/* تنسيق Main Content */
main {
    flex: 1;
    padding: 2rem 0;
    min-height: calc(100vh - 200px); /* ارتفاع الصفحة ناقص header و footer */
}

/* تنسيق Footer */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin-top: auto;
    padding: 3rem 0 1rem;
}

footer h5, footer h6 {
    color: #ecf0f1;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer .text-muted {
    color: #bdc3c7 !important;
}

footer a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #3498db;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3498db;
    transform: translateY(-2px);
}

/* تنسيق Footer الجديد - Compact */
.footer-compact {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    margin-top: auto;
    padding: 2rem 0 1rem;
    font-size: 0.9rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.footer-brand .brand-title {
    color: #ecf0f1;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.footer-brand .brand-description {
    color: #bdc3c7;
    font-size: 0.85rem;
    line-height: 1.4;
}

.footer-links h6,
.footer-contact h6,
.footer-social h6 {
    color: #ecf0f1;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links ul li {
    margin-bottom: 0.4rem;
}

.footer-links ul li a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.85rem;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: #3498db;
}

.contact-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.contact-item i {
    color: #3498db;
    font-size: 0.9rem;
    width: 16px;
}

.contact-item span {
    color: #bdc3c7;
}

.social-links {
    display: flex;
    gap: 0.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.social-link:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright p {
    color: #bdc3c7;
    font-size: 0.8rem;
    margin: 0;
}

.footer-legal {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-legal a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.8rem;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: #3498db;
}

.footer-legal .separator {
    color: #bdc3c7;
    font-size: 0.8rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-items {
        align-items: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* تنسيق الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

/* تنسيق النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
}

/* تنسيق البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.5rem;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تنسيق الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* تنسيق التنقل */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 8px;
    margin: 0 0.25rem;
    border: none;
    color: #667eea;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

/* تنسيق الشريط الجانبي */
.sidebar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    height: fit-content;
}

.sidebar .nav-link {
    color: #6c757d;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(-5px);
}

/* تنسيق الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-card .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stats-card .label {
    color: #6c757d;
    font-weight: 500;
}

/* تنسيق صفحة تسجيل الدخول */
.login-section {
    min-height: calc(100vh - 200px);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 2rem 0;
}

.login-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1.5rem;
    text-align: center;
}

/* تنسيق صفحة التسجيل */
.register-section {
    min-height: calc(100vh - 200px);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.register-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 2rem 0;
}

/* تنسيق صفحة الأخصائيين */
.specialist-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.specialist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.specialist-image {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
}

.specialist-info {
    padding: 1.5rem;
}

.specialist-rating {
    color: #f39c12;
    font-size: 1.2rem;
}

/* تنسيق صفحة المكتبة */
.library-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.library-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.library-image {
    height: 150px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.library-info {
    padding: 1.5rem;
}

/* تنسيق صفحة التواصل */
.contact-info {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
}

.contact-item i {
    font-size: 1.5rem;
    margin-left: 1rem;
    color: #667eea;
}

.contact-item:hover i {
    color: white;
}

/* تنسيق Back to Top Button */
#backToTop {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

#backToTop:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
}

/* تنسيق Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تنسيق Responsive */
@media (max-width: 768px) {
    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }
    
    .navbar-nav .nav-link {
        margin: 0.5rem 0;
    }
    
    .login-card,
    .register-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .sidebar {
        margin-bottom: 2rem;
    }
    
    footer {
        text-align: center;
    }
    
    footer .col-md-6 {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }
    
    .card {
        margin: 0.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* تنسيق الطباعة */
@media print {
    .navbar,
    .footer,
    .btn,
    #backToTop {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* تنسيق الوضع المظلم (Dark Mode) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card,
    .login-card,
    .register-card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #4d4d4d;
        color: #ffffff;
    }
    
    .table {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .table tbody tr:hover {
        background-color: #3d3d3d;
    }
}

/* تنسيق إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تنسيق التركيز للوحة المفاتيح */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* تنسيق النص المحدد */
::selection {
    background: #667eea;
    color: white;
}

::-moz-selection {
    background: #667eea;
    color: white;
}
