<?php
/**
 * إعدادات النظام - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/settings');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = db();
        
        // تحديث إعدادات الموقع
        if (isset($_POST['site_name'])) {
            $siteName = trim($_POST['site_name']);
            $siteDescription = trim($_POST['site_description'] ?? '');
            $siteEmail = trim($_POST['site_email'] ?? '');
            $sitePhone = trim($_POST['site_phone'] ?? '');
            $siteAddress = trim($_POST['site_address'] ?? '');
            
            // تحديث الإعدادات في قاعدة البيانات
            $settings = [
                'site_name' => $siteName,
                'site_description' => $siteDescription,
                'site_email' => $siteEmail,
                'site_phone' => $sitePhone,
                'site_address' => $siteAddress
            ];
            
            foreach ($settings as $key => $value) {
                $db->update("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            }
            
            setAlert('تم تحديث إعدادات الموقع بنجاح', 'success');
        }
        
        // تحديث إعدادات النظام
        if (isset($_POST['maintenance_mode'])) {
            $maintenanceMode = $_POST['maintenance_mode'] === 'on' ? 'true' : 'false';
            $db->update("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?", [$maintenanceMode, 'maintenance_mode']);
            setAlert('تم تحديث وضع الصيانة بنجاح', 'success');
        }
        
        // تحديث إعدادات الأمان
        if (isset($_POST['session_timeout'])) {
            $sessionTimeout = intval($_POST['session_timeout']);
            $db->update("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?", [$sessionTimeout, 'session_timeout']);
            setAlert('تم تحديث إعدادات الأمان بنجاح', 'success');
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// الحصول على الإعدادات الحالية
try {
    $db = db();
    $settings = $db->select("SELECT setting_key, setting_value FROM system_settings");
    $currentSettings = [];
    if ($settings && is_array($settings)) {
        foreach ($settings as $setting) {
            $currentSettings[$setting['setting_key']] = $setting['setting_value'];
        }
    } else {
        $currentSettings = [];
    }
} catch (Exception $e) {
    $currentSettings = [];
    setAlert('حدث خطأ في تحميل الإعدادات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إعدادات النظام';
$currentPage = 'admin_settings';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .settings-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .settings-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .form-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات النظام
                            </h2>
                            <p class="mb-0">إدارة إعدادات المنصة والنظام</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات الموقع -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-globe me-2"></i>
                        إعدادات الموقع
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="site_name" class="form-label">اسم الموقع</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                       value="<?= htmlspecialchars($currentSettings['site_name'] ?? 'منصة نفسي') ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="site_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="site_email" name="site_email" 
                                       value="<?= htmlspecialchars($currentSettings['site_email'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="site_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="site_phone" name="site_phone" 
                                       value="<?= htmlspecialchars($currentSettings['site_phone'] ?? '') ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="site_address" class="form-label">العنوان</label>
                                <input type="text" class="form-control" id="site_address" name="site_address" 
                                       value="<?= htmlspecialchars($currentSettings['site_address'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="site_description" class="form-label">وصف الموقع</label>
                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?= htmlspecialchars($currentSettings['site_description'] ?? '') ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ إعدادات الموقع
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إعدادات النظام -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card settings-card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                       <?= ($currentSettings['maintenance_mode'] ?? 'false') === 'true' ? 'checked' : '' ?>>
                                <label class="form-check-label" for="maintenance_mode">
                                    وضع الصيانة
                                </label>
                                <small class="form-text text-muted d-block">
                                    عند تفعيل هذا الخيار، سيتم إيقاف الموقع للمستخدمين العاديين
                                </small>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>
                            حفظ إعدادات النظام
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card settings-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        إعدادات الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="session_timeout" class="form-label">مهلة الجلسة (بالدقائق)</label>
                            <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                   value="<?= htmlspecialchars($currentSettings['session_timeout'] ?? '30') ?>" min="5" max="480">
                            <small class="form-text text-muted">
                                الوقت الذي تبقى فيه الجلسة نشطة قبل انتهاء صلاحيتها
                            </small>
                        </div>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-2"></i>
                            حفظ إعدادات الأمان
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card settings-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary"><?= $currentSettings['total_users'] ?? 0 ?></h4>
                                <p class="text-muted">إجمالي المستخدمين</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-calendar fa-2x text-success mb-2"></i>
                                <h4 class="text-success"><?= $currentSettings['total_appointments'] ?? 0 ?></h4>
                                <p class="text-muted">إجمالي المواعيد</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-user-md fa-2x text-info mb-2"></i>
                                <h4 class="text-info"><?= $currentSettings['total_therapists'] ?? 0 ?></h4>
                                <p class="text-muted">إجمالي المعالجين</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning"><?= $currentSettings['active_sessions'] ?? 0 ?></h4>
                                <p class="text-muted">الجلسات النشطة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 