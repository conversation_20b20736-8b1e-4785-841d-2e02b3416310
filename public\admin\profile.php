<?php
/**
 * صفحة الملف الشخصي للمدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/profile');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = db();
        
        // تحديث المعلومات الشخصية
        if (isset($_POST['update_profile'])) {
            $firstName = trim($_POST['first_name']);
            $lastName = trim($_POST['last_name']);
            $email = trim($_POST['email']);
            $phone = trim($_POST['phone'] ?? '');
            $dateOfBirth = trim($_POST['date_of_birth'] ?? '');
            $gender = trim($_POST['gender'] ?? '');
            $address = trim($_POST['address'] ?? '');
            
            // التحقق من صحة البيانات
            if (empty($firstName) || empty($lastName)) {
                setAlert('الاسم الأول والأخير مطلوبان', 'danger');
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                setAlert('البريد الإلكتروني غير صحيح', 'danger');
            } else {
                // التحقق من عدم تكرار البريد الإلكتروني
                $existingUser = $db->select("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $currentUser['id']]);
                if ($existingUser && count($existingUser) > 0) {
                    setAlert('البريد الإلكتروني مستخدم بالفعل', 'danger');
                } else {
                    // تحديث البيانات
                    $updateData = [
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'phone' => $phone,
                        'date_of_birth' => $dateOfBirth ?: null,
                        'gender' => $gender,
                        'address' => $address,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $result = $db->update("UPDATE users SET first_name = ?, last_name = ?, email = ?, phone = ?, date_of_birth = ?, gender = ?, address = ?, updated_at = ? WHERE id = ?", 
                        [$firstName, $lastName, $email, $phone, $dateOfBirth, $gender, $address, date('Y-m-d H:i:s'), $currentUser['id']]);
                    
                    if ($result) {
                        setAlert('تم تحديث الملف الشخصي بنجاح', 'success');
                        // تحديث بيانات المستخدم في الجلسة
                        $currentUser = array_merge($currentUser, $updateData);
                        $_SESSION['user'] = $currentUser;
                    } else {
                        setAlert('حدث خطأ في تحديث الملف الشخصي', 'danger');
                    }
                }
            }
        }
        
        // تغيير كلمة المرور
        if (isset($_POST['change_password'])) {
            $currentPassword = $_POST['current_password'];
            $newPassword = $_POST['new_password'];
            $confirmPassword = $_POST['confirm_password'];
            
            // التحقق من كلمة المرور الحالية
            if (!password_verify($currentPassword, $currentUser['password'])) {
                setAlert('كلمة المرور الحالية غير صحيحة', 'danger');
            } elseif (strlen($newPassword) < 6) {
                setAlert('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل', 'danger');
            } elseif ($newPassword !== $confirmPassword) {
                setAlert('كلمة المرور الجديدة غير متطابقة', 'danger');
            } else {
                // تشفير كلمة المرور الجديدة
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                $result = $db->update("UPDATE users SET password = ?, updated_at = ? WHERE id = ?", 
                    [$hashedPassword, date('Y-m-d H:i:s'), $currentUser['id']]);
                
                if ($result) {
                    setAlert('تم تغيير كلمة المرور بنجاح', 'success');
                } else {
                    setAlert('حدث خطأ في تغيير كلمة المرور', 'danger');
                }
            }
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// الحصول على إحصائيات المدير
try {
    $db = db();
    
    // عدد المستخدمين
    $totalUsers = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'");
    $totalUsersCount = $totalUsers[0]['count'] ?? 0;
    
    // عدد المعالجين
    $totalTherapists = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'");
    $totalTherapistsCount = $totalTherapists[0]['count'] ?? 0;
    
    // عدد المواعيد
    $totalAppointments = $db->select("SELECT COUNT(*) as count FROM appointments");
    $totalAppointmentsCount = $totalAppointments[0]['count'] ?? 0;
    
} catch (Exception $e) {
    $totalUsersCount = 0;
    $totalTherapistsCount = 0;
    $totalAppointmentsCount = 0;
}

// تعيين متغيرات الصفحة
$pageTitle = 'الملف الشخصي - المدير';
$currentPage = 'admin_profile';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .profile-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .profile-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .user-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: white;
        border: 4px solid rgba(255,255,255,0.3);
    }

    .stats-item {
        text-align: center;
        padding: 1rem;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
</style>

<!-- تضمين ملف CSS مخصص للملف الشخصي -->
<link rel="stylesheet" href="<?= asset('css/profile.css') ?>">

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الملف الشخصي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card profile-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <div class="user-avatar mx-auto mb-3">
                                <i class="fas fa-user-shield"></i>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h2 class="mb-2"><?= htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']) ?></h2>
                            <p class="mb-1"><i class="fas fa-envelope me-2"></i><?= htmlspecialchars($currentUser['email']) ?></p>
                            <?php if (!empty($currentUser['phone'])): ?>
                            <p class="mb-1"><i class="fas fa-phone me-2"></i><?= htmlspecialchars($currentUser['phone']) ?></p>
                            <?php endif; ?>
                            <p class="mb-0"><i class="fas fa-calendar me-2"></i>مدير منذ: <?= date('Y/m/d', strtotime($currentUser['created_at'])) ?></p>
                        </div>
                        <div class="col-md-3 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المدير -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        إحصائيات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stats-item">
                                <div class="stats-number"><?= $totalUsersCount ?></div>
                                <p class="text-muted mb-0">إجمالي المستخدمين</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-item">
                                <div class="stats-number"><?= $totalTherapistsCount ?></div>
                                <p class="text-muted mb-0">إجمالي المعالجين</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-item">
                                <div class="stats-number"><?= $totalAppointmentsCount ?></div>
                                <p class="text-muted mb-0">إجمالي المواعيد</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الملف الشخصي -->
        <div class="col-md-8 mb-4">
            <div class="card profile-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        معلومات الملف الشخصي
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= htmlspecialchars($currentUser['first_name']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= htmlspecialchars($currentUser['last_name']) ?>" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($currentUser['email']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= htmlspecialchars($currentUser['phone'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="<?= htmlspecialchars($currentUser['date_of_birth'] ?? '') ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?= ($currentUser['gender'] ?? '') === 'male' ? 'selected' : '' ?>>ذكر</option>
                                    <option value="female" <?= ($currentUser['gender'] ?? '') === 'female' ? 'selected' : '' ?>>أنثى</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($currentUser['address'] ?? '') ?></textarea>
                        </div>
                        <button type="submit" name="update_profile" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- تغيير كلمة المرور -->
        <div class="col-md-4 mb-4">
            <div class="card profile-card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lock me-2"></i>
                        تغيير كلمة المرور
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" name="change_password" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </form>
                </div>
            </div>

            <!-- معلومات الحساب -->
            <div class="card profile-card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحساب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>نوع الحساب:</strong>
                        <span class="badge bg-danger ms-2">مدير النظام</span>
                    </div>
                    <div class="mb-3">
                        <strong>تاريخ التسجيل:</strong>
                        <p class="text-muted mb-0"><?= date('Y/m/d H:i', strtotime($currentUser['created_at'])) ?></p>
                    </div>
                    <?php if (!empty($currentUser['updated_at'])): ?>
                    <div class="mb-3">
                        <strong>آخر تحديث:</strong>
                        <p class="text-muted mb-0"><?= date('Y/m/d H:i', strtotime($currentUser['updated_at'])) ?></p>
                    </div>
                    <?php endif; ?>
                    <div class="mb-0">
                        <strong>حالة الحساب:</strong>
                        <span class="badge bg-success ms-2">نشط</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 