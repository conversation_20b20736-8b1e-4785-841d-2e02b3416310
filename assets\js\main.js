/**
 * منصة نفسي - JavaScript الرئيسي
 * Nafsi Platform - Main JavaScript
 */

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    
    // تهيئة جميع المكونات
    initBackToTop();
    initTooltips();
    initFormValidation();
    initAlerts();
    initLoadingStates();
    
    console.log('منصة نفسي - تم تحميل JavaScript بنجاح');
});

/**
 * زر العودة للأعلى
 */
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        // إظهار/إخفاء الزر عند التمرير
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('d-none');
            } else {
                backToTopBtn.classList.add('d-none');
            }
        });
        
        // التمرير للأعلى عند النقر
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

/**
 * تهيئة التلميحات
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * التحقق من صحة النماذج
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * إدارة التنبيهات
 */
function initAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * حالات التحميل
 */
function initLoadingStates() {
    // إضافة حالة التحميل للأزرار
    const loadingButtons = document.querySelectorAll('[data-loading]');
    loadingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="loading me-2"></span>جاري التحميل...';
            this.disabled = true;
            
            // إعادة النص الأصلي بعد ثانيتين (يمكن تعديلها حسب الحاجة)
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 2000);
        });
    });
}

/**
 * دالة مساعدة لإرسال طلبات AJAX
 */
function ajaxRequest(url, method = 'GET', data = null, options = {}) {
    const defaultOptions = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    if (data && method !== 'GET') {
        defaultOptions.body = JSON.stringify(data);
    }
    
    const finalOptions = { ...defaultOptions, ...options };
    
    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('خطأ في الطلب:', error);
            throw error;
        });
}

/**
 * دالة مساعدة لعرض رسائل التنبيه
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertContainer.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : (type === 'error' ? 'exclamation-circle' : 'info-circle')} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // إخفاء التنبيه تلقائياً
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertContainer);
        bsAlert.close();
    }, duration);
}

/**
 * دالة مساعدة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * دالة مساعدة للتحقق من قوة كلمة المرور
 */
function checkPasswordStrength(password) {
    const strength = {
        score: 0,
        feedback: []
    };
    
    if (password.length >= 8) strength.score++;
    else strength.feedback.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    
    if (/[A-Z]/.test(password)) strength.score++;
    else strength.feedback.push('يجب أن تحتوي على حرف كبير');
    
    if (/[a-z]/.test(password)) strength.score++;
    else strength.feedback.push('يجب أن تحتوي على حرف صغير');
    
    if (/[0-9]/.test(password)) strength.score++;
    else strength.feedback.push('يجب أن تحتوي على رقم');
    
    if (/[^A-Za-z0-9]/.test(password)) strength.score++;
    else strength.feedback.push('يجب أن تحتوي على رمز خاص');
    
    return strength;
}

/**
 * دالة مساعدة لتنسيق التاريخ
 */
function formatDate(date) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    return new Date(date).toLocaleDateString('ar-SA', options);
}

/**
 * دالة مساعدة لتنسيق المبلغ
 */
function formatMoney(amount, currency = 'SAR') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * دالة مساعدة لتحويل النص إلى slug
 */
function createSlug(text) {
    return text
        .toLowerCase()
        .replace(/[^\u0600-\u06FF\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
}

/**
 * دالة مساعدة لتحميل الملفات
 */
function uploadFile(file, url, onProgress = null, onSuccess = null, onError = null) {
    const formData = new FormData();
    formData.append('file', file);
    
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable && onProgress) {
            const percentComplete = (e.loaded / e.total) * 100;
            onProgress(percentComplete);
        }
    });
    
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                if (onSuccess) onSuccess(response);
            } catch (e) {
                if (onError) onError('خطأ في تحليل الاستجابة');
            }
        } else {
            if (onError) onError(`خطأ في الرفع: ${xhr.status}`);
        }
    });
    
    xhr.addEventListener('error', function() {
        if (onError) onError('خطأ في الاتصال');
    });
    
    xhr.open('POST', url);
    xhr.send(formData);
}

/**
 * دالة مساعدة للبحث المباشر
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * دالة مساعدة للتحقق من الاتصال بالإنترنت
 */
function checkOnlineStatus() {
    if (!navigator.onLine) {
        showAlert('لا يوجد اتصال بالإنترنت', 'warning');
    }
    
    window.addEventListener('online', function() {
        showAlert('تم استعادة الاتصال بالإنترنت', 'success');
    });
    
    window.addEventListener('offline', function() {
        showAlert('انقطع الاتصال بالإنترنت', 'warning');
    });
}

// تهيئة فحص الاتصال
checkOnlineStatus();

/**
 * دالة مساعدة لحفظ البيانات في التخزين المحلي
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('خطأ في حفظ البيانات:', e);
        return false;
    }
}

/**
 * دالة مساعدة لاسترجاع البيانات من التخزين المحلي
 */
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('خطأ في استرجاع البيانات:', e);
        return null;
    }
}

/**
 * دالة مساعدة لحذف البيانات من التخزين المحلي
 */
function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (e) {
        console.error('خطأ في حذف البيانات:', e);
        return false;
    }
}

// تصدير الدوال للاستخدام العام
window.NafsiPlatform = {
    ajaxRequest,
    showAlert,
    isValidEmail,
    checkPasswordStrength,
    formatDate,
    formatMoney,
    createSlug,
    uploadFile,
    debounce,
    saveToLocalStorage,
    getFromLocalStorage,
    removeFromLocalStorage
}; 