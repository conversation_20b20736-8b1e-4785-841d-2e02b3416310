<?php
/**
 * إدارة المكتبة - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/library');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$libraryId = $_GET['id'] ?? 0;

if ($action && $libraryId) {
    try {
        $db = db();
        
        switch ($action) {
            case 'approve':
                $db->update("library", [
                    "status" => "active",
                    "approved_by" => $currentUser['id'],
                    "approved_at" => date('Y-m-d H:i:s')
                ], "id = ?", [$libraryId]);
                setAlert('تم الموافقة على المادة بنجاح', 'success');
                break;
                
            case 'reject':
                $db->update("library", [
                    "status" => "inactive",
                    "approved_by" => $currentUser['id'],
                    "approved_at" => date('Y-m-d H:i:s')
                ], "id = ?", [$libraryId]);
                setAlert('تم رفض المادة بنجاح', 'success');
                break;
                
            case 'feature':
                $db->update("library", ["is_featured" => 1], "id = ?", [$libraryId]);
                setAlert('تم تمييز المادة كمميزة بنجاح', 'success');
                break;
                
            case 'unfeature':
                $db->update("library", ["is_featured" => 0], "id = ?", [$libraryId]);
                setAlert('تم إلغاء تمييز المادة بنجاح', 'success');
                break;
                
            case 'archive':
                $db->update("library", ["status" => "archived"], "id = ?", [$libraryId]);
                setAlert('تم أرشفة المادة بنجاح', 'success');
                break;
                
            case 'delete':
                $db->delete("library", "id = ?", [$libraryId]);
                setAlert('تم حذف المادة بنجاح', 'success');
                break;
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// معاملات البحث والتصفية
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';
$fileType = $_GET['file_type'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(title LIKE ? OR description LIKE ? OR author LIKE ? OR publisher LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if ($category) {
    $whereConditions[] = "category = ?";
    $params[] = $category;
}

if ($status) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

if ($fileType) {
    $whereConditions[] = "file_type = ?";
    $params[] = $fileType;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات عامة
    $stats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM library")[0]['count'] ?? 0,
        'active' => $db->select("SELECT COUNT(*) as count FROM library WHERE status = 'active'")[0]['count'] ?? 0,
        'pending' => $db->select("SELECT COUNT(*) as count FROM library WHERE status = 'inactive'")[0]['count'] ?? 0,
        'archived' => $db->select("SELECT COUNT(*) as count FROM library WHERE status = 'archived'")[0]['count'] ?? 0,
        'featured' => $db->select("SELECT COUNT(*) as count FROM library WHERE is_featured = 1")[0]['count'] ?? 0
    ];
    
    // إجمالي عدد المواد للصفحات
    $totalLibrary = $db->select("SELECT COUNT(*) as count FROM library $whereClause", $params)[0]['count'] ?? 0;
    $totalPages = ceil($totalLibrary / $perPage);
    
    // قائمة المواد
    $libraryItems = $db->select("
        SELECT l.*, u.first_name, u.last_name, u.username as creator_name
        FROM library l
        LEFT JOIN users u ON l.created_by = u.id
        $whereClause 
        ORDER BY l.created_at DESC 
        LIMIT ? OFFSET ?
    ", array_merge($params, [$perPage, $offset]));
    
    // الحصول على التصنيفات المتاحة
    $categories = $db->select("SELECT DISTINCT category FROM library WHERE category IS NOT NULL AND category != '' ORDER BY category");
    
} catch (Exception $e) {
    $libraryItems = [];
    $totalLibrary = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'active' => 0, 'pending' => 0, 'archived' => 0, 'featured' => 0];
    $categories = [];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إدارة المكتبة';
$currentPage = 'admin_library';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<!-- تضمين الشريط الجانبي -->
<?php include 'includes/sidebar.php'; ?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .library-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .library-card {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
        transition: all 0.3s ease;
    }

    .library-card:hover {
        background: #e8f0ff;
    }

    .file-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .featured-badge {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
    }
</style>

<div class="admin-main-content">
    <div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-book-open me-3"></i>
                            إدارة المكتبة
                        </h1>
                        <p class="mb-0 opacity-75">إدارة المواد التعليمية والكتب في المكتبة</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= url('admin/library/add') ?>" class="btn btn-light btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مادة جديدة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format($stats['total']) ?></h4>
                    <small class="text-muted">إجمالي المواد</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format($stats['active']) ?></h4>
                    <small class="text-muted">نشط</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format($stats['pending']) ?></h4>
                    <small class="text-muted">في الانتظار</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-archive fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format($stats['archived']) ?></h4>
                    <small class="text-muted">مؤرشف</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format($stats['featured']) ?></h4>
                    <small class="text-muted">مميز</small>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-download fa-2x"></i>
                    </div>
                    <h4 class="mb-1"><?= number_format(array_sum(array_column($libraryItems, 'download_count'))) ?></h4>
                    <small class="text-muted">إجمالي التحميلات</small>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="search-container p-4">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?= htmlspecialchars($search) ?>" 
                               placeholder="البحث في العنوان، الوصف، المؤلف...">
                    </div>
                    <div class="col-md-2">
                        <label for="category" class="form-label">التصنيف</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع التصنيفات</option>
                            <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat['category']) ?>" 
                                    <?= $category === $cat['category'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat['category']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>نشط</option>
                            <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>في الانتظار</option>
                            <option value="archived" <?= $status === 'archived' ? 'selected' : '' ?>>مؤرشف</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="file_type" class="form-label">نوع الملف</label>
                        <select class="form-select" id="file_type" name="file_type">
                            <option value="">جميع الأنواع</option>
                            <option value="pdf" <?= $fileType === 'pdf' ? 'selected' : '' ?>>PDF</option>
                            <option value="doc" <?= $fileType === 'doc' ? 'selected' : '' ?>>DOC</option>
                            <option value="docx" <?= $fileType === 'docx' ? 'selected' : '' ?>>DOCX</option>
                            <option value="epub" <?= $fileType === 'epub' ? 'selected' : '' ?>>EPUB</option>
                            <option value="video" <?= $fileType === 'video' ? 'selected' : '' ?>>فيديو</option>
                            <option value="audio" <?= $fileType === 'audio' ? 'selected' : '' ?>>صوتي</option>
                            <option value="link" <?= $fileType === 'link' ? 'selected' : '' ?>>رابط</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                        <a href="<?= url('admin/library') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- جدول المواد -->
    <div class="row">
        <div class="col-12">
            <div class="table-container">
                <div class="card border-0">
                    <div class="card-header bg-transparent border-0 py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    المواد التعليمية
                                    <span class="badge bg-primary ms-2"><?= number_format($totalLibrary) ?></span>
                                </h5>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <small class="text-muted">
                                    عرض <?= number_format($offset + 1) ?> - <?= number_format(min($offset + $perPage, $totalLibrary)) ?> 
                                    من <?= number_format($totalLibrary) ?> مادة
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (empty($libraryItems)): ?>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مواد في المكتبة</h5>
                        <p class="text-muted">ابدأ بإضافة مواد جديدة للمكتبة</p>
                        <a href="<?= url('admin/library/add') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مادة جديدة
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المادة</th>
                                    <th>المؤلف</th>
                                    <th>التصنيف</th>
                                    <th>نوع الملف</th>
                                    <th>الحالة</th>
                                    <th>التحميلات</th>
                                    <th>التقييم</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($libraryItems as $item): ?>
                                <tr class="library-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="library-avatar me-3">
                                                <?= strtoupper(substr($item['title'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">
                                                    <?= htmlspecialchars($item['title']) ?>
                                                    <?php if ($item['is_featured']): ?>
                                                    <span class="featured-badge ms-2">مميز</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($item['creator_name'] ?? 'غير محدد') ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($item['author'] ?? 'غير محدد') ?></td>
                                    <td>
                                        <span class="badge bg-info"><?= htmlspecialchars($item['category'] ?? 'غير محدد') ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary file-type-badge">
                                            <?= strtoupper($item['file_type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $item['status'] === 'active' ? 'success' : ($item['status'] === 'inactive' ? 'warning' : 'secondary') ?>">
                                            <?= $item['status'] === 'active' ? 'نشط' : ($item['status'] === 'inactive' ? 'في الانتظار' : 'مؤرشف') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-download text-muted"></i>
                                        <?= number_format($item['download_count']) ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="text-warning me-1">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star<?= $i <= $item['rating'] ? '' : '-o' ?>" style="font-size: 0.75rem;"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <small class="text-muted">(<?= $item['total_ratings'] ?>)</small>
                                        </div>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($item['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($item['status'] === 'inactive'): ?>
                                            <a href="?action=approve&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action" 
                                               title="موافقة">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <?php elseif ($item['status'] === 'active'): ?>
                                            <a href="?action=reject&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action" 
                                               title="رفض">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (!$item['is_featured']): ?>
                                            <a href="?action=feature&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-info btn-action" 
                                               title="تمييز كمميز">
                                                <i class="fas fa-star"></i>
                                            </a>
                                            <?php else: ?>
                                            <a href="?action=unfeature&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-outline-info btn-action" 
                                               title="إلغاء التمييز">
                                                <i class="fas fa-star-o"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="<?= url('admin/library/edit/' . $item['id']) ?>" 
                                               class="btn btn-sm btn-primary btn-action" 
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <a href="<?= url('admin/library/view/' . $item['id']) ?>" 
                                               class="btn btn-sm btn-secondary btn-action" 
                                               title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($item['status'] !== 'archived'): ?>
                                            <a href="?action=archive&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-outline-secondary btn-action" 
                                               title="أرشفة">
                                                <i class="fas fa-archive"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="?action=delete&id=<?= $item['id'] ?>" 
                                               class="btn btn-sm btn-danger btn-action" 
                                               onclick="return confirm('هل أنت متأكد من حذف هذه المادة؟ هذا الإجراء لا يمكن التراجع عنه.')"
                                               title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>&file_type=<?= urlencode($fileType) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>&file_type=<?= urlencode($fileType) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>&file_type=<?= urlencode($fileType) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 