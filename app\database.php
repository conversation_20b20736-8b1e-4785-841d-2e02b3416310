<?php
/**
 * كلاس إدارة قاعدة البيانات
 * Database Management Class
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Database {
    private static $instance = null;
    private $connection;
    private $lastQuery;
    private $lastError;
    
    private function __construct() {
        $this->connect();
    }
    
    /**
     * الحصول على نسخة واحدة من الكلاس (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            $this->lastError = null;
            
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Connection Error: " . $e->getMessage());
            }
            throw new Exception("فشل الاتصال بقاعدة البيانات");
        }
    }
    
    /**
     * الحصول على الاتصال
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * تحضير استعلام
     */
    public function prepare($sql) {
        try {
            return $this->connection->prepare($sql);
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Prepare Error: " . $e->getMessage());
            }
            throw new Exception("خطأ في تحضير الاستعلام");
        }
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Select Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام SELECT للحصول على صف واحد
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database SelectOne Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Insert Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Update Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Delete Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام عام
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastQuery = $sql;
            return true;
        } catch (PDOException $e) {
            $this->lastError = $e->getMessage();
            if (isDebugMode()) {
                error_log("Database Execute Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * بدء المعاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * التراجع عن المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * الحصول على آخر خطأ
     */
    public function getLastError() {
        return $this->lastError;
    }
    
    /**
     * الحصول على آخر استعلام
     */
    public function getLastQuery() {
        return $this->lastQuery;
    }
    
    /**
     * التحقق من وجود جدول
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->select($sql, [$tableName]);
        return !empty($result);
    }
    
    /**
     * الحصول على عدد الصفوف في جدول
     */
    public function count($table, $where = '', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM `$table`";
        if (!empty($where)) {
            $sql .= " WHERE $where";
        }
        $result = $this->selectOne($sql, $params);
        return $result ? $result['count'] : 0;
    }
    
    /**
     * إغلاق الاتصال
     */
    public function close() {
        $this->connection = null;
    }
    
    /**
     * منع النسخ
     */
    private function __clone() {}
    
    /**
     * منع إعادة الإنشاء
     */
    public function __wakeup() {
        throw new Exception("لا يمكن إعادة إنشاء هذا الكائن");
    }
}

// دالة مساعدة للحصول على قاعدة البيانات
function db() {
    return Database::getInstance();
} 