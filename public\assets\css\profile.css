/* ملف CSS مخصص لصفحات الملف الشخصي */

.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    margin-bottom: 2rem;
}

.profile-card {
    border-radius: 15px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
}

.profile-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.user-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    border: 4px solid rgba(255,255,255,0.3);
    margin: 0 auto 1rem;
}

.stats-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin: 0.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.form-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    padding: 2rem;
}

.profile-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.profile-info h6 {
    color: #495057;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.profile-info p {
    color: #6c757d;
    margin-bottom: 0;
}

.badge-custom {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* تحسينات للأزرار */
.btn-profile {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسينات للنماذج */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسينات للبطاقات */
.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .user-avatar {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .profile-header .card-body {
        padding: 1rem;
    }
}

/* تأثيرات إضافية */
.profile-card .card-header {
    position: relative;
    overflow: hidden;
}

.profile-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.profile-card:hover .card-header::before {
    left: 100%;
}

/* تحسينات للصور */
.profile-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 1rem;
}

/* تحسينات للقوائم */
.profile-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.profile-list li:last-child {
    border-bottom: none;
}

.profile-list .label {
    font-weight: 600;
    color: #495057;
}

.profile-list .value {
    color: #6c757d;
}

/* تحسينات للجداول */
.profile-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.profile-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.profile-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.profile-table tr:last-child td {
    border-bottom: none;
}

/* تحسينات للرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* تحسينات للشريط الجانبي */
.sidebar-profile {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 2rem;
}

.sidebar-profile .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1rem;
    border: 3px solid rgba(255,255,255,0.3);
}

/* تحسينات للتنقل */
.profile-nav {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.profile-nav .nav-link {
    color: #6c757d;
    border-radius: 8px;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.profile-nav .nav-link:hover {
    background: #f8f9fa;
    color: #667eea;
}

.profile-nav .nav-link.active {
    background: #667eea;
    color: white;
}

/* تحسينات للطباعة */
@media print {
    .profile-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
    
    .profile-header {
        background: #f8f9fa !important;
        color: #495057 !important;
    }
} 