<?php
/**
 * إدارة الأخصائيين - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/specialists');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$specialistId = $_GET['id'] ?? 0;

if ($action && $specialistId) {
    try {
        $db = db();
        
        switch ($action) {
            case 'approve':
                $db->update("users", ["status" => "active"], "id = ? AND user_type = 'therapist'", [$specialistId]);
                setAlert('تم الموافقة على الأخصائي بنجاح', 'success');
                break;
                
            case 'reject':
                $db->update("users", ["status" => "inactive"], "id = ? AND user_type = 'therapist'", [$specialistId]);
                setAlert('تم رفض الأخصائي بنجاح', 'success');
                break;
                
            case 'delete':
                $db->delete("users", "id = ? AND user_type = 'therapist'", [$specialistId]);
                setAlert('تم حذف الأخصائي بنجاح', 'success');
                break;
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// معاملات البحث والتصفية
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$whereConditions = ["user_type = 'therapist'"];
$params = [];

if ($search) {
    $whereConditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if ($status) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات عامة
    $stats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'")[0]['count'] ?? 0,
        'active' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist' AND status = 'active'")[0]['count'] ?? 0,
        'pending' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist' AND status = 'pending'")[0]['count'] ?? 0,
        'inactive' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist' AND status = 'inactive'")[0]['count'] ?? 0
    ];
    
    // إجمالي عدد الأخصائيين للصفحات
    $totalSpecialists = $db->select("SELECT COUNT(*) as count FROM users $whereClause", $params)[0]['count'] ?? 0;
    $totalPages = ceil($totalSpecialists / $perPage);
    
    // قائمة الأخصائيين
    $specialists = $db->select("
        SELECT id, first_name, last_name, email, status, created_at, last_login,
               profile_image, bio, specialization, experience_years
        FROM users 
        $whereClause 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ", array_merge($params, [$perPage, $offset]));
    
} catch (Exception $e) {
    $specialists = [];
    $totalSpecialists = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'active' => 0, 'pending' => 0, 'inactive' => 0];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إدارة الأخصائيين';
$currentPage = 'admin_specialists';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .specialist-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .specialist-card {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
        transition: all 0.3s ease;
    }

    .specialist-card:hover {
        background: #e8f0ff;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-user-md me-2"></i>
                                إدارة الأخصائيين
                            </h2>
                            <p class="mb-0">إدارة جميع الأخصائيين النفسيين في المنصة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['total'] ?></h3>
                    <p class="mb-0">إجمالي الأخصائيين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['active'] ?></h3>
                    <p class="mb-0">الأخصائيين النشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['pending'] ?></h3>
                    <p class="mb-0">في انتظار الموافقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-secondary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-times fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['inactive'] ?></h3>
                    <p class="mb-0">غير النشطين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-container">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="البحث بالاسم أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>نشط</option>
                                <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <a href="<?= url('admin/specialists') ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-refresh"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأخصائيين -->
    <div class="row">
        <div class="col-12">
            <div class="card table-container">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة الأخصائيين
                        <span class="badge bg-light text-primary ms-2"><?= $totalSpecialists ?> أخصائي</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($specialists)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد أخصائيين</h5>
                        <p class="text-muted">لم يتم العثور على أخصائيين يطابقون معايير البحث</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الأخصائي</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>التخصص</th>
                                    <th>الخبرة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($specialists as $specialist): ?>
                                <tr class="specialist-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="specialist-avatar me-3">
                                                <?= strtoupper(substr($specialist['first_name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">د. <?= htmlspecialchars($specialist['first_name'] . ' ' . $specialist['last_name']) ?></h6>
                                                <small class="text-muted">ID: <?= $specialist['id'] ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($specialist['email']) ?></td>
                                    <td>
                                        <?= htmlspecialchars($specialist['specialization'] ?? 'غير محدد') ?>
                                    </td>
                                    <td>
                                        <?= $specialist['experience_years'] ? $specialist['experience_years'] . ' سنوات' : 'غير محدد' ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $specialist['status'] === 'active' ? 'success' : ($specialist['status'] === 'pending' ? 'warning' : 'secondary') ?>">
                                            <?= $specialist['status'] === 'active' ? 'نشط' : ($specialist['status'] === 'pending' ? 'في الانتظار' : 'غير نشط') ?>
                                        </span>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($specialist['created_at'])) ?></td>
                                    <td>
                                        <?= $specialist['last_login'] ? date('Y-m-d H:i', strtotime($specialist['last_login'])) : 'لم يسجل دخول' ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($specialist['status'] === 'pending'): ?>
                                            <a href="?action=approve&id=<?= $specialist['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="?action=reject&id=<?= $specialist['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action"
                                               onclick="return confirm('هل أنت متأكد من رفض هذا الأخصائي؟')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <?php elseif ($specialist['status'] === 'inactive'): ?>
                                            <a href="?action=approve&id=<?= $specialist['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            <?php else: ?>
                                            <a href="?action=reject&id=<?= $specialist['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action"
                                               onclick="return confirm('هل أنت متأكد من إيقاف هذا الأخصائي؟')">
                                                <i class="fas fa-pause"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="<?= url('admin/specialist/' . $specialist['id']) ?>" 
                                               class="btn btn-sm btn-info btn-action">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <a href="?action=delete&id=<?= $specialist['id'] ?>" 
                                               class="btn btn-sm btn-danger btn-action" 
                                               onclick="return confirm('هل أنت متأكد من حذف هذا الأخصائي؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 