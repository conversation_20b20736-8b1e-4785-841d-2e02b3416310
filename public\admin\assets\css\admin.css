/* أنماط لوحة التحكم */
:root {
    --admin-primary: #2563eb;
    --admin-secondary: #64748b;
    --admin-success: #059669;
    --admin-warning: #d97706;
    --admin-danger: #dc2626;
    --admin-info: #0891b2;
    --admin-dark: #1e293b;
    --admin-light: #f8fafc;
    --admin-border: #e2e8f0;
}

/* التخطيط العام */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--admin-light);
}

/* الشريط الجانبي */
.sidebar {
    width: 250px;
    background-color: var(--admin-dark);
    color: white;
    padding: 1rem;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.main-content {
    flex: 1;
    margin-right: 250px;
    padding: 2rem;
}

/* البطاقات */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    border: 1px solid var(--admin-border);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--admin-primary);
}

/* الإجراءات السريعة */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: transform 0.2s;
    border: 1px solid var(--admin-border);
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.action-icon {
    font-size: 2rem;
    color: var(--admin-primary);
    margin-bottom: 1rem;
}

/* التنقل */
.nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 0.25rem;
    transition: all 0.2s;
}

.nav-link:hover {
    color: white !important;
    background-color: rgba(255,255,255,0.1);
}

.nav-link.active {
    background-color: var(--admin-primary);
    color: white !important;
}

/* الأزرار */
.btn-outline-light {
    border-color: rgba(255,255,255,0.3);
}

.btn-outline-light:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

/* الجداول */
.table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table th {
    background-color: var(--admin-light);
    border-bottom: 2px solid var(--admin-border);
    font-weight: 600;
}

/* النماذج */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--admin-border);
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* البطاقات */
.card {
    border-radius: 8px;
    border: 1px solid var(--admin-border);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
    background-color: var(--admin-light);
    border-bottom: 1px solid var(--admin-border);
    font-weight: 600;
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* تأثيرات إضافية */
.text-primary {
    color: var(--admin-primary) !important;
}

.text-success {
    color: var(--admin-success) !important;
}

.text-warning {
    color: var(--admin-warning) !important;
}

.text-danger {
    color: var(--admin-danger) !important;
}

.text-info {
    color: var(--admin-info) !important;
}

/* تحسينات للأداء */
* {
    box-sizing: border-box;
}

/* تحسين الخطوط */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    font-display: swap;
} 