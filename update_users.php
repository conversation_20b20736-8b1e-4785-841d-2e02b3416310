<?php
/**
 * ملف لتحديث البيانات في جدول users
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>تحديث البيانات في جدول users</h2>";
    
    // فحص هيكل الجدول
    echo "<h3>فحص هيكل الجدول:</h3>";
    $columns = $db->select("DESCRIBE users");
    if ($columns === false) {
        echo "<p style='color: red;'>خطأ في فحص هيكل الجدول</p>";
        return;
    } else {
        echo "<p>أعمدة جدول users:</p>";
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        }
    }
    
    // عرض البيانات قبل التحديث
    echo "<h3>البيانات قبل التحديث:</h3>";
    $beforeUpdate = $db->select("
        SELECT id, username, email, first_name, last_name, user_type
        FROM users 
        WHERE username IN ('mariam', 'ali', 'nada', 'mohammed', 'reem', 'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user')
        OR username LIKE 'dr.%'
        ORDER BY username
    ");
    
    if ($beforeUpdate === false) {
        echo "<p style='color: red;'>خطأ في استعلام البيانات قبل التحديث</p>";
    } else {
        echo "<p>عدد المستخدمين المطلوب تحديثهم: " . count($beforeUpdate) . "</p>";
        foreach ($beforeUpdate as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // تحديث العملاء
    echo "<h3>تحديث العملاء:</h3>";
    $clientUsernames = ['mariam', 'ali', 'nada', 'mohammed', 'reem', 'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user'];
    
    foreach ($clientUsernames as $username) {
        $result = $db->update("users", 
            ["user_type" => "regular"], 
            "username = ?", 
            [$username]
        );
        
        if ($result) {
            echo "<p style='color: green;'>✓ تم تحديث المستخدم: $username</p>";
        } else {
            echo "<p style='color: orange;'>⚠ لم يتم تحديث المستخدم: $username</p>";
        }
    }
    
    // تحديث المعالجين
    echo "<h3>تحديث المعالجين:</h3>";
    $result = $db->update("users", 
        ["user_type" => "specialist"], 
        "username LIKE 'dr.%'", 
        []
    );
    
    if ($result) {
        echo "<p style='color: green;'>✓ تم تحديث المعالجين</p>";
    } else {
        echo "<p style='color: orange;'>⚠ لم يتم تحديث المعالجين</p>";
    }
    
    // عرض النتائج بعد التحديث
    echo "<h3>النتائج بعد التحديث:</h3>";
    
    // العملاء
    $clients = $db->select("
        SELECT id, first_name, last_name, email, user_type
        FROM users 
        WHERE (user_type = 'regular' OR user_type IS NULL OR user_type = '')
        ORDER BY first_name, last_name
    ");
    
    echo "<h4>العملاء (" . count($clients) . "):</h4>";
    foreach ($clients as $client) {
        echo "- " . $client['first_name'] . ' ' . $client['last_name'] . " (" . $client['email'] . ")<br>";
    }
    
    // المعالجين
    $therapists = $db->select("
        SELECT id, first_name, last_name, email, user_type, specialization
        FROM users 
        WHERE user_type = 'specialist'
        ORDER BY first_name, last_name
    ");
    
    if ($therapists === false) {
        echo "<h4>المعالجين (0):</h4>";
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
    } else {
        echo "<h4>المعالجين (" . count($therapists) . "):</h4>";
        foreach ($therapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ")<br>";
        }
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم تحديث البيانات بنجاح!</p>";
    echo "<p>يمكنك الآن الوصول إلى صفحة إضافة المواعيد وستجد العملاء والمعالجين متاحين.</p>";
    echo "<p><a href='/nafsi_platform/admin/add_appointment' style='color: blue;'>اذهب إلى صفحة إضافة المواعيد</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 