<?php
/**
 * لوحة تحكم المعالج النفسي
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/therapist/dashboard');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المعالج
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'therapist') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على إحصائيات المعالج
try {
    $db = db();
    
    // إحصائيات المواعيد
    $totalAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE therapist_id = ?", [$currentUser['id']])[0]['count'] ?? 0;
    $todayAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE therapist_id = ? AND DATE(appointment_date) = CURDATE()", [$currentUser['id']])[0]['count'] ?? 0;
    $completedAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE therapist_id = ? AND status = 'completed'", [$currentUser['id']])[0]['count'] ?? 0;
    $pendingAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE therapist_id = ? AND status = 'pending'", [$currentUser['id']])[0]['count'] ?? 0;
    
    // عدد العملاء
    $totalClients = $db->select("SELECT COUNT(DISTINCT client_id) as count FROM appointments WHERE therapist_id = ?", [$currentUser['id']])[0]['count'] ?? 0;
    
    // المواعيد اليوم
    $todaySchedule = $db->select("
        SELECT a.*, u.first_name, u.last_name, u.email 
        FROM appointments a 
        JOIN users u ON a.client_id = u.id 
        WHERE a.therapist_id = ? AND DATE(a.appointment_date) = CURDATE() 
        ORDER BY a.appointment_time ASC
    ", [$currentUser['id']]);
    
    // المواعيد القادمة
    $upcomingAppointments = $db->select("
        SELECT a.*, u.first_name, u.last_name 
        FROM appointments a 
        JOIN users u ON a.client_id = u.id 
        WHERE a.therapist_id = ? AND a.appointment_date > CURDATE() 
        ORDER BY a.appointment_date ASC, a.appointment_time ASC 
        LIMIT 5
    ", [$currentUser['id']]);
    
} catch (Exception $e) {
    $totalAppointments = $todayAppointments = $completedAppointments = $pendingAppointments = 0;
    $totalClients = 0;
    $todaySchedule = $upcomingAppointments = [];
}

// تعيين متغيرات الصفحة
$pageTitle = 'لوحة تحكم المعالج';
$currentPage = 'therapist_dashboard';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .therapist-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(17, 153, 142, 0.3);
    }
    
    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .appointment-card {
        border-left: 4px solid #11998e;
        background: #f0fffe;
        transition: all 0.3s ease;
    }
    
    .appointment-card:hover {
        background: #e6fffd;
        transform: translateX(-5px);
    }
    
    .time-badge {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 20px;
        padding: 5px 15px;
        font-weight: 600;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- بطاقة الترحيب بالمعالج -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card therapist-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <div class="user-avatar">
                                <i class="fas fa-user-md"></i>
                            </div>
                        </div>
                        <div class="col-md-10">
                            <h2 class="mb-2">مرحباً، د. <?= htmlspecialchars($currentUser['first_name']) ?>!</h2>
                            <p class="mb-1">
                                <i class="fas fa-user-md"></i> 
                                معالج نفسي
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-envelope"></i> 
                                <?= htmlspecialchars($currentUser['email']) ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-calendar"></i> 
                                آخر تسجيل دخول: <?= $currentUser['last_login'] ? date('Y-m-d H:i', strtotime($currentUser['last_login'])) : 'لم يتم تسجيل الدخول من قبل' ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المعالج -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-primary mx-auto">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3 class="mb-1"><?= $totalAppointments ?></h3>
                    <p class="mb-0">إجمالي المواعيد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-success mx-auto">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1"><?= $todayAppointments ?></h3>
                    <p class="mb-0">مواعيد اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-info mx-auto">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="mb-1"><?= $totalClients ?></h3>
                    <p class="mb-0">العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-warning mx-auto">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1"><?= $completedAppointments ?></h3>
                    <p class="mb-0">الجلسات المكتملة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الأقسام الرئيسية -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-day"></i> مواعيد اليوم</h5>
                    <a href="<?= url('therapist/appointments') ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-eye"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($todaySchedule)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>لا توجد مواعيد لليوم</p>
                        <a href="<?= url('therapist/schedule') ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة موعد
                        </a>
                    </div>
                    <?php else: ?>
                    <?php foreach ($todaySchedule as $appointment): ?>
                    <div class="appointment-card p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user text-primary"></i>
                                    <?= htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-envelope"></i>
                                    <?= htmlspecialchars($appointment['email']) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-calendar"></i>
                                    <?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?>
                                </p>
                            </div>
                            <div class="text-end">
                                <div class="time-badge mb-2">
                                    <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                </div>
                                <span class="badge bg-<?= $appointment['status'] === 'confirmed' ? 'success' : ($appointment['status'] === 'completed' ? 'info' : 'warning') ?>">
                                    <?= $appointment['status'] === 'confirmed' ? 'مؤكدة' : ($appointment['status'] === 'completed' ? 'مكتملة' : 'في الانتظار') ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> المواعيد القادمة</h5>
                    <a href="<?= url('therapist/appointments') ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-eye"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($upcomingAppointments)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>لا توجد مواعيد قادمة</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($upcomingAppointments as $appointment): ?>
                    <div class="appointment-card p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user text-success"></i>
                                    <?= htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-calendar"></i>
                                    <?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-clock"></i>
                                    <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                </p>
                            </div>
                            <span class="badge bg-<?= $appointment['status'] === 'confirmed' ? 'success' : 'warning' ?>">
                                <?= $appointment['status'] === 'confirmed' ? 'مؤكدة' : 'في الانتظار' ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة للمعالج -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-tools"></i> إدارة الممارسة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('therapist/appointments') ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-calendar"></i><br>
                                إدارة المواعيد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('therapist/clients') ?>" class="btn btn-outline-success w-100">
                                <i class="fas fa-users"></i><br>
                                إدارة العملاء
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('therapist/schedule') ?>" class="btn btn-outline-info w-100">
                                <i class="fas fa-clock"></i><br>
                                الجدول الزمني
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('therapist/profile') ?>" class="btn btn-outline-warning w-100">
                                <i class="fas fa-user-edit"></i><br>
                                الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?>
