<?php
/**
 * إدارة المواعيد - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/appointments');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$appointmentId = $_GET['id'] ?? 0;

// معالجة إضافة موعد جديد
if (isset($_POST['action']) && $_POST['action'] === 'add_appointment') {
    try {
        $db = db();
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['client_id', 'therapist_id', 'appointment_date', 'appointment_time'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("حقل $field مطلوب");
            }
        }
        
        // التحقق من أن العميل والمعالج موجودان
        $client = $db->select("SELECT id FROM users WHERE id = ? AND user_type = 'user'", [$_POST['client_id']]);
        if (empty($client)) {
            throw new Exception("العميل غير موجود أو غير صحيح");
        }
        
        $therapist = $db->select("SELECT id FROM users WHERE id = ? AND user_type = 'therapist'", [$_POST['therapist_id']]);
        if (empty($therapist)) {
            throw new Exception("المعالج غير موجود أو غير صحيح");
        }
        
        // التحقق من أن التاريخ والوقت في المستقبل
        $appointmentDateTime = $_POST['appointment_date'] . ' ' . $_POST['appointment_time'];
        if (strtotime($appointmentDateTime) <= time()) {
            throw new Exception("يجب أن يكون الموعد في المستقبل");
        }
        
        // التحقق من عدم وجود تعارض في المواعيد
        $conflictingAppointment = $db->select("
            SELECT id FROM appointments 
            WHERE therapist_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'
        ", [$_POST['therapist_id'], $_POST['appointment_date'], $_POST['appointment_time']]);
        
        if (!empty($conflictingAppointment)) {
            throw new Exception("هذا الموعد محجوز بالفعل للمعالج المحدد");
        }
        
        // إعداد البيانات للإدراج
        $appointmentData = [
            'client_id' => $_POST['client_id'],
            'therapist_id' => $_POST['therapist_id'],
            'appointment_date' => $_POST['appointment_date'],
            'appointment_time' => $_POST['appointment_time'],
            'status' => 'confirmed', // تأكيد الموعد تلقائياً
            'notes' => trim($_POST['notes'] ?? ''),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // إدراج الموعد الجديد
        $columns = implode(', ', array_keys($appointmentData));
        $placeholders = ':' . implode(', :', array_keys($appointmentData));
        $sql = "INSERT INTO appointments ($columns) VALUES ($placeholders)";
        
        $appointmentId = $db->insert($sql, $appointmentData);
        
        if ($appointmentId) {
            setAlert('تم إضافة الموعد الجديد بنجاح', 'success');
        } else {
            throw new Exception("فشل في إضافة الموعد");
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

if ($action && $appointmentId) {
    try {
        $db = db();
        
        switch ($action) {
            case 'confirm':
                $db->update("UPDATE appointments SET status = 'confirmed' WHERE id = ?", [$appointmentId]);
                setAlert('تم تأكيد الموعد بنجاح', 'success');
                break;
                
            case 'cancel':
                $db->update("UPDATE appointments SET status = 'cancelled' WHERE id = ?", [$appointmentId]);
                setAlert('تم إلغاء الموعد بنجاح', 'success');
                break;
                
            case 'complete':
                $db->update("UPDATE appointments SET status = 'completed' WHERE id = ?", [$appointmentId]);
                setAlert('تم إكمال الموعد بنجاح', 'success');
                break;
                
            case 'delete':
                $db->delete("DELETE FROM appointments WHERE id = ?", [$appointmentId]);
                setAlert('تم حذف الموعد بنجاح', 'success');
                break;
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// معاملات البحث والتصفية
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$date = $_GET['date'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(c.first_name LIKE ? OR c.last_name LIKE ? OR t.first_name LIKE ? OR t.last_name LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if ($status) {
    $whereConditions[] = "a.status = ?";
    $params[] = $status;
}

if ($date) {
    $whereConditions[] = "DATE(a.appointment_date) = ?";
    $params[] = $date;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات عامة
    $stats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM appointments")[0]['count'] ?? 0,
        'confirmed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'confirmed'")[0]['count'] ?? 0,
        'completed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'completed'")[0]['count'] ?? 0,
        'cancelled' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'cancelled'")[0]['count'] ?? 0
    ];
    
    // إجمالي عدد المواعيد للصفحات
    $totalAppointments = $db->select("
        SELECT COUNT(*) as count 
        FROM appointments a 
        LEFT JOIN users c ON a.client_id = c.id 
        LEFT JOIN users t ON a.therapist_id = t.id 
        $whereClause
    ", $params)[0]['count'] ?? 0;
    $totalPages = ceil($totalAppointments / $perPage);
    
    // قائمة المواعيد
    $appointments = $db->select("
        SELECT a.*, 
               c.first_name as client_first_name, c.last_name as client_last_name, c.email as client_email,
               t.first_name as therapist_first_name, t.last_name as therapist_last_name, t.email as therapist_email
        FROM appointments a 
        LEFT JOIN users c ON a.client_id = c.id 
        LEFT JOIN users t ON a.therapist_id = t.id 
        $whereClause 
        ORDER BY a.appointment_date DESC, a.appointment_time DESC 
        LIMIT ? OFFSET ?
    ", array_merge($params, [$perPage, $offset]));
    
} catch (Exception $e) {
    $appointments = [];
    $totalAppointments = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'confirmed' => 0, 'completed' => 0, 'cancelled' => 0];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// الحصول على قائمة العملاء والمعالجين
try {
    $clients = $db->select("SELECT id, first_name, last_name, email FROM users WHERE user_type = 'user' AND is_active = 1 ORDER BY first_name, last_name");
    $therapists = $db->select("SELECT id, first_name, last_name, email FROM users WHERE user_type = 'therapist' AND is_active = 1 ORDER BY first_name, last_name");
} catch (Exception $e) {
    $clients = [];
    $therapists = [];
    setAlert('حدث خطأ في تحميل قوائم العملاء والمعالجين: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إدارة المواعيد';
$currentPage = 'admin_appointments';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .appointment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .appointment-card {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
        transition: all 0.3s ease;
    }

    .appointment-card:hover {
        background: #e8f0ff;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-calendar-alt me-2"></i>
                                إدارة المواعيد
                            </h2>
                            <p class="mb-0">إدارة جميع المواعيد في المنصة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light me-2">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                            <a href="<?= url('admin/add_appointment') ?>" class="btn btn-success">
                                <i class="fas fa-plus"></i> إضافة موعد جديد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['total'] ?></h3>
                    <p class="mb-0">إجمالي المواعيد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['confirmed'] ?></h3>
                    <p class="mb-0">المواعيد المؤكدة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['completed'] ?></h3>
                    <p class="mb-0">المواعيد المكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['cancelled'] ?></h3>
                    <p class="mb-0">المواعيد الملغاة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-container">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="البحث بالاسم">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                <option value="confirmed" <?= $status === 'confirmed' ? 'selected' : '' ?>>مؤكدة</option>
                                <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>مكتملة</option>
                                <option value="cancelled" <?= $status === 'cancelled' ? 'selected' : '' ?>>ملغاة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="<?= htmlspecialchars($date) ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <a href="<?= url('admin/appointments') ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-refresh"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المواعيد -->
    <div class="row">
        <div class="col-12">
            <div class="card table-container">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة المواعيد
                        <span class="badge bg-light text-primary ms-2"><?= $totalAppointments ?> موعد</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($appointments)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مواعيد</h5>
                        <p class="text-muted">لم يتم العثور على مواعيد تطابق معايير البحث</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الموعد</th>
                                    <th>العميل</th>
                                    <th>المعالج</th>
                                    <th>التاريخ والوقت</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($appointments as $appointment): ?>
                                <tr class="appointment-card">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="appointment-avatar me-3">
                                                <i class="fas fa-calendar"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">موعد #<?= $appointment['id'] ?></h6>
                                                <small class="text-muted"><?= $appointment['notes'] ?: 'لا توجد ملاحظات' ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($appointment['client_first_name'] . ' ' . $appointment['client_last_name']) ?></h6>
                                            <small class="text-muted"><?= htmlspecialchars($appointment['client_email']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0">د. <?= htmlspecialchars($appointment['therapist_first_name'] . ' ' . $appointment['therapist_last_name']) ?></h6>
                                            <small class="text-muted"><?= htmlspecialchars($appointment['therapist_email']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?></h6>
                                            <small class="text-muted"><?= date('H:i', strtotime($appointment['appointment_time'])) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $appointment['status'] === 'confirmed' ? 'success' : ($appointment['status'] === 'completed' ? 'info' : ($appointment['status'] === 'cancelled' ? 'danger' : 'warning')) ?>">
                                            <?= $appointment['status'] === 'confirmed' ? 'مؤكدة' : ($appointment['status'] === 'completed' ? 'مكتملة' : ($appointment['status'] === 'cancelled' ? 'ملغاة' : 'في الانتظار')) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($appointment['status'] === 'pending'): ?>
                                            <a href="?action=confirm&id=<?= $appointment['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action">
                                                <i class="fas fa-check"></i>
                                            </a>
                                            <a href="?action=cancel&id=<?= $appointment['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action"
                                               onclick="return confirm('هل أنت متأكد من إلغاء هذا الموعد؟')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <?php elseif ($appointment['status'] === 'confirmed'): ?>
                                            <a href="?action=complete&id=<?= $appointment['id'] ?>" 
                                               class="btn btn-sm btn-info btn-action">
                                                <i class="fas fa-clipboard-check"></i>
                                            </a>
                                            <a href="?action=cancel&id=<?= $appointment['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action"
                                               onclick="return confirm('هل أنت متأكد من إلغاء هذا الموعد؟')">
                                                <i class="fas fa-times"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="<?= url('admin/appointment/' . $appointment['id']) ?>" 
                                               class="btn btn-sm btn-info btn-action">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <a href="?action=delete&id=<?= $appointment['id'] ?>" 
                                               class="btn btn-sm btn-danger btn-action" 
                                               onclick="return confirm('هل أنت متأكد من حذف هذا الموعد؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date=<?= urlencode($date) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal إضافة موعد جديد -->
<div class="modal fade" id="addAppointmentModal" tabindex="-1" aria-labelledby="addAppointmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addAppointmentModalLabel">
                    <i class="fas fa-calendar-plus me-2"></i>
                    إضافة موعد جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="addAppointmentForm">
                <input type="hidden" name="action" value="add_appointment">
                <div class="modal-body">
                    <div class="row">
                        <!-- العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="client_id" class="form-label">
                                <i class="fas fa-user text-primary"></i>
                                العميل <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="client_id" name="client_id" required>
                                <option value="">اختر العميل</option>
                                <?php foreach ($clients as $client): ?>
                                <option value="<?= $client['id'] ?>">
                                    <?= htmlspecialchars($client['first_name'] . ' ' . $client['last_name']) ?> 
                                    (<?= htmlspecialchars($client['email']) ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- المعالج -->
                        <div class="col-md-6 mb-3">
                            <label for="therapist_id" class="form-label">
                                <i class="fas fa-user-md text-primary"></i>
                                المعالج <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="therapist_id" name="therapist_id" required>
                                <option value="">اختر المعالج</option>
                                <?php foreach ($therapists as $therapist): ?>
                                <option value="<?= $therapist['id'] ?>">
                                    د. <?= htmlspecialchars($therapist['first_name'] . ' ' . $therapist['last_name']) ?> 
                                    (<?= htmlspecialchars($therapist['email']) ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- التاريخ -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">
                                <i class="fas fa-calendar text-primary"></i>
                                التاريخ <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                   required min="<?= date('Y-m-d') ?>">
                        </div>
                        
                        <!-- الوقت -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_time" class="form-label">
                                <i class="fas fa-clock text-primary"></i>
                                الوقت <span class="text-danger">*</span>
                            </label>
                            <input type="time" class="form-control" id="appointment_time" name="appointment_time" 
                                   required>
                        </div>
                        
                        <!-- الملاحظات -->
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label">
                                <i class="fas fa-sticky-note text-primary"></i>
                                الملاحظات
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="أدخل ملاحظات إضافية حول الموعد (اختياري)"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إضافة الموعد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript للتفاعل -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة النموذج
    const addAppointmentForm = document.getElementById('addAppointmentForm');
    if (addAppointmentForm) {
        addAppointmentForm.addEventListener('submit', function(e) {
            const clientId = document.getElementById('client_id');
            const therapistId = document.getElementById('therapist_id');
            const appointmentDate = document.getElementById('appointment_date');
            const appointmentTime = document.getElementById('appointment_time');
            
            // التحقق من اختيار العميل
            if (!clientId.value) {
                e.preventDefault();
                alert('يرجى اختيار العميل');
                clientId.focus();
                return false;
            }
            
            // التحقق من اختيار المعالج
            if (!therapistId.value) {
                e.preventDefault();
                alert('يرجى اختيار المعالج');
                therapistId.focus();
                return false;
            }
            
            // التحقق من التاريخ
            if (!appointmentDate.value) {
                e.preventDefault();
                alert('يرجى اختيار تاريخ الموعد');
                appointmentDate.focus();
                return false;
            }
            
            // التحقق من أن التاريخ في المستقبل
            const selectedDate = new Date(appointmentDate.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                e.preventDefault();
                alert('يجب أن يكون تاريخ الموعد في المستقبل');
                appointmentDate.focus();
                return false;
            }
            
            // التحقق من الوقت
            if (!appointmentTime.value) {
                e.preventDefault();
                alert('يرجى اختيار وقت الموعد');
                appointmentTime.focus();
                return false;
            }
        });
    }
    
    // إعادة تعيين النموذج عند إغلاق Modal
    const addAppointmentModal = document.getElementById('addAppointmentModal');
    if (addAppointmentModal) {
        addAppointmentModal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('addAppointmentForm').reset();
        });
    }
    
    // تعيين الحد الأدنى للتاريخ كاليوم الحالي
    const appointmentDateInput = document.getElementById('appointment_date');
    if (appointmentDateInput) {
        appointmentDateInput.min = new Date().toISOString().split('T')[0];
    }
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?>
