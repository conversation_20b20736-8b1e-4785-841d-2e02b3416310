<?php
/**
 * صفحة 404 - الصفحة غير موجودة
 */

// تضمين الملفات المطلوبة
if (!defined('NAFSI_APP')) {
    require_once __DIR__ . '/../app/init.php';
}

// تعيين متغيرات الصفحة
$pageTitle = '404 - الصفحة غير موجودة';
$currentPage = '404';

// تضمين header
include_once 'includes/header.php';
?>

<style>
    .error-container {
        text-align: center;
        background: white;
        padding: 3rem;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        max-width: 500px;
        width: 90%;
        margin: 2rem auto;
    }
    
    .error-code {
        font-size: 6rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 1rem;
        line-height: 1;
    }
    
    .error-title {
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 1rem;
    }
    
    .error-message {
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .search-box {
        margin: 2rem 0;
        position: relative;
    }
    
    .search-box input {
        width: 100%;
        padding: 1rem 1.5rem;
        border: 2px solid #e1e5e9;
        border-radius: 25px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .search-box input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .search-box button {
        position: absolute;
        left: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #667eea;
        font-size: 1.2rem;
    }
    
    .quick-links {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e1e5e9;
    }
    
    .quick-links h5 {
        color: #333;
        margin-bottom: 1rem;
    }
    
    .quick-links a {
        display: inline-block;
        margin: 0.25rem;
        padding: 0.5rem 1rem;
        background: #f8f9fa;
        color: #667eea;
        text-decoration: none;
        border-radius: 15px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .quick-links a:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
    }
</style>

<div class="container">
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h1 class="error-title">الصفحة غير موجودة</h1>
        
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
            <br>
            تأكد من صحة الرابط أو جرب البحث عن المحتوى المطلوب.
        </p>
        
        <!-- البحث -->
        <div class="search-box">
            <form action="search.php" method="GET">
                <input type="text" name="q" placeholder="ابحث في الموقع..." required>
                <button type="submit">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        
        <!-- الأزرار -->
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>
                الصفحة الرئيسية
            </a>
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصفحة السابقة
            </a>
        </div>
        
        <!-- الروابط السريعة -->
        <div class="quick-links">
            <h5>روابط سريعة:</h5>
            <a href="about.php">عن المنصة</a>
            <a href="contact.php">تواصل معنا</a>
            <a href="login.php">تسجيل الدخول</a>
            <a href="register.php">إنشاء حساب</a>
            <a href="specialists.php">الأخصائيين</a>
            <a href="library.php">المكتبة</a>
        </div>
    </div>
</div>

<script>
    // إضافة تأثيرات بصرية
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.querySelector('.error-container');
        container.style.opacity = '0';
        container.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            container.style.transition = 'all 0.5s ease';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }, 100);
    });
    
    // البحث التلقائي
    const searchInput = document.querySelector('.search-box input');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = this.value.trim();
            if (query) {
                window.location.href = `search.php?q=${encodeURIComponent(query)}`;
            }
        }
    });
</script>

<?php include_once 'includes/footer.php'; ?> 