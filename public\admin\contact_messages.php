<?php
/**
 * صفحة إدارة رسائل التواصل
 * Contact Messages Management Page
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /admin/contact_messages');
    exit;
}

// التحقق من صلاحيات المدير
if (!isLoggedIn() || $_SESSION['user_type'] !== 'admin') {
    header('Location: ' . url('login'));
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

$db = Database::getInstance();

// معالجة تحديث حالة الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $messageId = (int)($_POST['message_id'] ?? 0);
    $action = $_POST['action'];
    
    if ($messageId > 0) {
        switch ($action) {
            case 'mark_read':
                $db->update("UPDATE contact_messages SET status = 'read' WHERE id = ?", [$messageId]);
                setAlert('تم تحديث حالة الرسالة بنجاح', 'success');
                break;
                
            case 'mark_replied':
                $db->update("UPDATE contact_messages SET status = 'replied' WHERE id = ?", [$messageId]);
                setAlert('تم تحديث حالة الرسالة بنجاح', 'success');
                break;
                
            case 'mark_closed':
                $db->update("UPDATE contact_messages SET status = 'closed' WHERE id = ?", [$messageId]);
                setAlert('تم إغلاق الرسالة بنجاح', 'success');
                break;
                
            case 'add_note':
                $note = trim($_POST['admin_note'] ?? '');
                if (!empty($note)) {
                    $db->update("UPDATE contact_messages SET admin_notes = ?, status = 'read' WHERE id = ?", [$note, $messageId]);
                    setAlert('تم إضافة الملاحظة بنجاح', 'success');
                }
                break;
        }
    }
    
    header('Location: ' . url('admin/contact_messages'));
    exit;
}

// الحصول على معاملات التصفية والترتيب
$status = $_GET['status'] ?? '';
$subject = $_GET['subject'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام SQL
$whereConditions = [];
$params = [];

if (!empty($status)) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

if (!empty($subject)) {
    $whereConditions[] = "subject = ?";
    $params[] = $subject;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على إجمالي عدد الرسائل
$countSql = "SELECT COUNT(*) as total FROM contact_messages $whereClause";
$totalResult = $db->select($countSql, $params);
$totalMessages = $totalResult[0]['total'] ?? 0;
$totalPages = ceil($totalMessages / $limit);

// الحصول على الرسائل
$sql = "SELECT * FROM contact_messages $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;

$messages = $db->select($sql, $params);

// الحصول على إحصائيات الرسائل
$statsSql = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_count,
    SUM(CASE WHEN status = 'replied' THEN 1 ELSE 0 END) as replied_count,
    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
FROM contact_messages";

$stats = $db->select($statsSql);
$messageStats = $stats[0] ?? [];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة رسائل التواصل - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?= asset('css/admin.css') ?>" rel="stylesheet">
    <link href="<?= asset('css/admin-dashboard.css') ?>" rel="stylesheet">
    <style>
        /* أنماط خاصة بصفحة رسائل التواصل */
        
        .message-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .message-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .message-card.new {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .message-card.read {
            border-left-color: #ffc107;
        }
        
        .message-card.replied {
            border-left-color: #28a745;
        }
        
        .message-card.closed {
            border-left-color: #6c757d;
            opacity: 0.7;
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .message-preview {
            max-height: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .filters-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="admin-main-content" id="adminMainContent">
        <div class="container-fluid">
            <!-- عرض التنبيهات -->
            <?php if ($alert): ?>
            <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
                <?= htmlspecialchars($alert['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

        <!-- رأس الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card page-header">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">
                                    <i class="fas fa-envelope me-2"></i>
                                    إدارة رسائل التواصل
                                </h2>
                                <p class="mb-0">عرض وإدارة الرسائل المرسلة من صفحة التواصل</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                    <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number"><?= $messageStats['total'] ?? 0 ?></div>
                            <div class="text-muted">إجمالي الرسائل</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number text-danger"><?= $messageStats['new_count'] ?? 0 ?></div>
                            <div class="text-muted">رسائل جديدة</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number text-warning"><?= $messageStats['read_count'] ?? 0 ?></div>
                            <div class="text-muted">رسائل مقروءة</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card text-center">
                            <div class="stats-number text-success"><?= $messageStats['replied_count'] ?? 0 ?></div>
                            <div class="text-muted">رسائل مجاب عليها</div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card filters-card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label text-white">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="new" <?= $status === 'new' ? 'selected' : '' ?>>جديدة</option>
                                    <option value="read" <?= $status === 'read' ? 'selected' : '' ?>>مقروءة</option>
                                    <option value="replied" <?= $status === 'replied' ? 'selected' : '' ?>>مجاب عليها</option>
                                    <option value="closed" <?= $status === 'closed' ? 'selected' : '' ?>>مغلقة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="subject" class="form-label text-white">الموضوع</label>
                                <select class="form-select" id="subject" name="subject">
                                    <option value="">جميع المواضيع</option>
                                    <option value="general" <?= $subject === 'general' ? 'selected' : '' ?>>استفسار عام</option>
                                    <option value="technical" <?= $subject === 'technical' ? 'selected' : '' ?>>مشكلة تقنية</option>
                                    <option value="billing" <?= $subject === 'billing' ? 'selected' : '' ?>>مشكلة في الدفع</option>
                                    <option value="support" <?= $subject === 'support' ? 'selected' : '' ?>>طلب دعم</option>
                                    <option value="partnership" <?= $subject === 'partnership' ? 'selected' : '' ?>>شراكة</option>
                                    <option value="other" <?= $subject === 'other' ? 'selected' : '' ?>>أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label text-white">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-light">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <a href="<?= url('admin/contact_messages') ?>" class="btn btn-outline-light">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة الرسائل -->
                <div class="row">
                    <?php if (empty($messages)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد رسائل تواصل
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <div class="col-12 mb-3">
                                <div class="card message-card <?= $message['status'] ?>">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">
                                                        <?= htmlspecialchars($message['first_name'] . ' ' . $message['last_name']) ?>
                                                        <span class="badge status-badge bg-<?= getStatusColor($message['status']) ?> ms-2">
                                                            <?= getStatusText($message['status']) ?>
                                                        </span>
                                                    </h6>
                                                    <small class="text-muted">
                                                        <?= formatDate($message['created_at']) ?>
                                                    </small>
                                                </div>
                                                
                                                <div class="mb-2">
                                                    <strong>البريد الإلكتروني:</strong> 
                                                    <a href="mailto:<?= htmlspecialchars($message['email']) ?>">
                                                        <?= htmlspecialchars($message['email']) ?>
                                                    </a>
                                                    <?php if (!empty($message['phone'])): ?>
                                                        | <strong>الهاتف:</strong> <?= htmlspecialchars($message['phone']) ?>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="mb-2">
                                                    <strong>الموضوع:</strong> 
                                                    <span class="badge bg-secondary"><?= getSubjectText($message['subject']) ?></span>
                                                </div>
                                                
                                                <div class="message-preview">
                                                    <strong>الرسالة:</strong><br>
                                                    <?= nl2br(htmlspecialchars($message['message'])) ?>
                                                </div>
                                                
                                                <?php if (!empty($message['admin_notes'])): ?>
                                                    <div class="mt-2 p-2 bg-light rounded">
                                                        <strong>ملاحظات المدير:</strong><br>
                                                        <?= nl2br(htmlspecialchars($message['admin_notes'])) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="d-flex flex-column gap-2">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewMessage(<?= $message['id'] ?>)">
                                                        <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                                    </button>
                                                    
                                                    <?php if ($message['status'] === 'new'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                                            <input type="hidden" name="action" value="mark_read">
                                                            <button type="submit" class="btn btn-sm btn-outline-warning">
                                                                <i class="fas fa-check me-1"></i>تحديد كمقروءة
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($message['status'] !== 'replied'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                                            <input type="hidden" name="action" value="mark_replied">
                                                            <button type="submit" class="btn btn-sm btn-outline-success">
                                                                <i class="fas fa-reply me-1"></i>تحديد كمجاب عليها
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($message['status'] !== 'closed'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                                            <input type="hidden" name="action" value="mark_closed">
                                                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                                                <i class="fas fa-times me-1"></i>إغلاق
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                                            onclick="addNote(<?= $message['id'] ?>)">
                                                        <i class="fas fa-sticky-note me-1"></i>إضافة ملاحظة
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- ترقيم الصفحات -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&status=<?= $status ?>&subject=<?= $subject ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal لعرض تفاصيل الرسالة -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لإضافة ملاحظة -->
    <div class="modal fade" id="noteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة ملاحظة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_note">
                        <input type="hidden" name="message_id" id="noteMessageId">
                        <div class="mb-3">
                            <label for="admin_note" class="form-label">الملاحظة</label>
                            <textarea class="form-control" id="admin_note" name="admin_note" rows="4" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الملاحظة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewMessage(messageId) {
            // يمكن إضافة AJAX هنا لتحميل تفاصيل الرسالة
            // للآن سنعرض رسالة بسيطة
            document.getElementById('messageModalBody').innerHTML = 
                '<p>سيتم إضافة تفاصيل الرسالة هنا...</p>';
            new bootstrap.Modal(document.getElementById('messageModal')).show();
        }
        
        function addNote(messageId) {
            document.getElementById('noteMessageId').value = messageId;
            new bootstrap.Modal(document.getElementById('noteModal')).show();
        }
        
        function exportMessages() {
            // يمكن إضافة وظيفة التصدير هنا
            alert('سيتم إضافة وظيفة التصدير قريباً');
        }
        
        // تحديث المحتوى الرئيسي عند إخفاء/إظهار الشريط الجانبي
        function updateMainContent() {
            const sidebar = document.getElementById('adminSidebar');
            const mainContent = document.getElementById('adminMainContent');
            
            if (sidebar && mainContent) {
                if (sidebar.classList.contains('collapsed')) {
                    mainContent.classList.add('sidebar-collapsed');
                } else {
                    mainContent.classList.remove('sidebar-collapsed');
                }
            }
        }
        
        // تشغيل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateMainContent();
        });
        
        // مراقبة تغييرات الشريط الجانبي
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    updateMainContent();
                }
            });
        });
        
        const sidebar = document.getElementById('adminSidebar');
        if (sidebar) {
            observer.observe(sidebar, { attributes: true });
        }
    </script>
</body>
</html>

<?php
/**
 * دوال مساعدة
 */

function getStatusColor($status) {
    switch ($status) {
        case 'new': return 'danger';
        case 'read': return 'warning';
        case 'replied': return 'success';
        case 'closed': return 'secondary';
        default: return 'secondary';
    }
}

function getStatusText($status) {
    switch ($status) {
        case 'new': return 'جديدة';
        case 'read': return 'مقروءة';
        case 'replied': return 'مجاب عليها';
        case 'closed': return 'مغلقة';
        default: return 'غير معروفة';
    }
}

function getSubjectText($subject) {
    switch ($subject) {
        case 'general': return 'استفسار عام';
        case 'technical': return 'مشكلة تقنية';
        case 'billing': return 'مشكلة في الدفع';
        case 'support': return 'طلب دعم';
        case 'partnership': return 'شراكة';
        case 'other': return 'أخرى';
        default: return 'غير محدد';
    }
}

function formatDate($date) {
    return date('Y/m/d H:i', strtotime($date));
}
?> 