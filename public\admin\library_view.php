<?php
/**
 * عرض تفاصيل المادة - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/library/view');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على معرف المادة
$libraryId = intval($_GET['id'] ?? 0);
if (!$libraryId) {
    setAlert('معرف المادة غير صحيح', 'danger');
    header('Location: ' . url('admin/library'));
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على بيانات المادة
try {
    $db = db();
    $libraryItem = $db->select("
        SELECT l.*, u.first_name, u.last_name, u.username as creator_name,
               a.first_name as approver_first_name, a.last_name as approver_last_name
        FROM library l
        LEFT JOIN users u ON l.created_by = u.id
        LEFT JOIN users a ON l.approved_by = a.id
        WHERE l.id = ?
    ", [$libraryId]);
    
    if (empty($libraryItem)) {
        setAlert('المادة غير موجودة', 'danger');
        header('Location: ' . url('admin/library'));
        exit;
    }
    
    $libraryItem = $libraryItem[0];
    
    // الحصول على التقييمات
    $ratings = $db->select("
        SELECT lr.*, u.first_name, u.last_name, u.username
        FROM library_ratings lr
        LEFT JOIN users u ON lr.user_id = u.id
        WHERE lr.library_id = ?
        ORDER BY lr.created_at DESC
        LIMIT 10
    ", [$libraryId]);
    
    // الحصول على إحصائيات التحميلات
    $downloadStats = $db->select("
        SELECT DATE(ld.downloaded_at) as download_date, COUNT(*) as download_count
        FROM library_downloads ld
        WHERE ld.library_id = ?
        GROUP BY DATE(ld.downloaded_at)
        ORDER BY download_date DESC
        LIMIT 30
    ", [$libraryId]);
    
} catch (Exception $e) {
    setAlert('حدث خطأ في تحميل بيانات المادة: ' . $e->getMessage(), 'danger');
    header('Location: ' . url('admin/library'));
    exit;
}

// تحويل العلامات إلى مصفوفة
$tags = [];
if (!empty($libraryItem['tags'])) {
    $tagsArray = json_decode($libraryItem['tags'], true);
    if (is_array($tagsArray)) {
        $tags = $tagsArray;
    }
}

// تعيين متغيرات الصفحة
$pageTitle = 'عرض المادة: ' . $libraryItem['title'];
$currentPage = 'admin_library';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<!-- تضمين الشريط الجانبي -->
<?php include 'includes/sidebar.php'; ?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .detail-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .detail-section {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 2rem;
        margin-bottom: 2rem;
    }

    .detail-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
    }

    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #667eea;
        min-width: 150px;
    }

    .info-value {
        color: #495057;
        text-align: left;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
    }

    .file-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        background: #6c757d;
        color: white;
    }

    .featured-badge {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
    }

    .rating-stars {
        color: #ffc107;
        font-size: 1.2rem;
    }

    .stats-card {
        background: #f8f9ff;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        border: 2px solid #e8f0ff;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .stats-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .tag-item {
        display: inline-block;
        background: #667eea;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        margin: 0.25rem;
        font-size: 0.875rem;
    }

    .rating-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #667eea;
    }

    .download-chart {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>

<div class="admin-main-content">
    <div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-eye me-3"></i>
                            عرض تفاصيل المادة
                        </h1>
                        <p class="mb-0 opacity-75"><?= htmlspecialchars($libraryItem['title']) ?></p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= url('admin/library/edit/' . $libraryId) ?>" class="btn btn-light btn-lg me-2">
                            <i class="fas fa-edit me-2"></i>
                            تعديل
                        </a>
                        <a href="<?= url('admin/library') ?>" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon text-primary">
                    <i class="fas fa-download"></i>
                </div>
                <h4 class="mb-1"><?= number_format($libraryItem['download_count']) ?></h4>
                <small class="text-muted">التحميلات</small>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon text-info">
                    <i class="fas fa-eye"></i>
                </div>
                <h4 class="mb-1"><?= number_format($libraryItem['view_count']) ?></h4>
                <small class="text-muted">المشاهدات</small>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon text-warning">
                    <i class="fas fa-star"></i>
                </div>
                <h4 class="mb-1"><?= number_format($libraryItem['rating'], 1) ?>/5</h4>
                <small class="text-muted">التقييم (<?= $libraryItem['total_ratings'] ?> تقييم)</small>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon text-success">
                    <i class="fas fa-calendar"></i>
                </div>
                <h4 class="mb-1"><?= date('Y-m-d', strtotime($libraryItem['created_at'])) ?></h4>
                <small class="text-muted">تاريخ الإضافة</small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- تفاصيل المادة -->
        <div class="col-lg-8">
            <div class="detail-container p-4">
                
                <!-- المعلومات الأساسية -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h4>
                    
                    <div class="info-item">
                        <span class="info-label">العنوان:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['title']) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الوصف:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['description'] ?: 'لا يوجد وصف') ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">المؤلف:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['author'] ?: 'غير محدد') ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الناشر:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['publisher'] ?: 'غير محدد') ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">التصنيف:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['category'] ?: 'غير محدد') ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">اللغة:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['language']) ?></span>
                    </div>
                    
                    <?php if (!empty($tags)): ?>
                    <div class="info-item">
                        <span class="info-label">العلامات:</span>
                        <span class="info-value">
                            <?php foreach ($tags as $tag): ?>
                            <span class="tag-item"><?= htmlspecialchars($tag) ?></span>
                            <?php endforeach; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- معلومات الملف -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="fas fa-file me-2"></i>
                        معلومات الملف
                    </h4>
                    
                    <div class="info-item">
                        <span class="info-label">نوع الملف:</span>
                        <span class="file-type-badge"><?= strtoupper($libraryItem['file_type']) ?></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">رابط الملف:</span>
                        <span class="info-value">
                            <a href="<?= htmlspecialchars($libraryItem['file_url']) ?>" target="_blank" class="text-primary">
                                <i class="fas fa-external-link-alt me-1"></i>
                                فتح الملف
                            </a>
                        </span>
                    </div>
                    
                    <?php if ($libraryItem['file_size'] > 0): ?>
                    <div class="info-item">
                        <span class="info-label">حجم الملف:</span>
                        <span class="info-value"><?= number_format($libraryItem['file_size']) ?> KB</span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($libraryItem['isbn']): ?>
                    <div class="info-item">
                        <span class="info-label">رقم ISBN:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['isbn']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($libraryItem['publication_year']): ?>
                    <div class="info-item">
                        <span class="info-label">سنة النشر:</span>
                        <span class="info-value"><?= $libraryItem['publication_year'] ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- الإعدادات والحالة -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات والحالة
                    </h4>
                    
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="status-badge bg-<?= $libraryItem['status'] === 'active' ? 'success' : ($libraryItem['status'] === 'inactive' ? 'warning' : 'secondary') ?>">
                            <?= $libraryItem['status'] === 'active' ? 'نشط' : ($libraryItem['status'] === 'inactive' ? 'في الانتظار' : 'مؤرشف') ?>
                        </span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">مجاني:</span>
                        <span class="info-value">
                            <i class="fas fa-<?= $libraryItem['is_free'] ? 'check text-success' : 'times text-danger' ?>"></i>
                            <?= $libraryItem['is_free'] ? 'نعم' : 'لا' ?>
                        </span>
                    </div>
                    
                    <?php if (!$libraryItem['is_free']): ?>
                    <div class="info-item">
                        <span class="info-label">السعر:</span>
                        <span class="info-value"><?= number_format($libraryItem['price'], 2) ?> ر.س</span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="info-item">
                        <span class="info-label">مميز:</span>
                        <span class="info-value">
                            <?php if ($libraryItem['is_featured']): ?>
                            <span class="featured-badge">مميز</span>
                            <?php else: ?>
                            <i class="fas fa-times text-muted"></i>
                            <?php endif; ?>
                        </span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">تم الإنشاء بواسطة:</span>
                        <span class="info-value"><?= htmlspecialchars($libraryItem['creator_name'] ?: 'غير محدد') ?></span>
                    </div>
                    
                    <?php if ($libraryItem['approved_by']): ?>
                    <div class="info-item">
                        <span class="info-label">تمت الموافقة بواسطة:</span>
                        <span class="info-value">
                            <?= htmlspecialchars($libraryItem['approver_first_name'] . ' ' . $libraryItem['approver_last_name']) ?>
                            في <?= date('Y-m-d H:i', strtotime($libraryItem['approved_at'])) ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- التقييمات -->
                <?php if (!empty($ratings)): ?>
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="fas fa-star me-2"></i>
                        آخر التقييمات
                    </h4>
                    
                    <?php foreach ($ratings as $rating): ?>
                    <div class="rating-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <strong><?= htmlspecialchars($rating['first_name'] . ' ' . $rating['last_name']) ?></strong>
                                <small class="text-muted ms-2">@<?= htmlspecialchars($rating['username']) ?></small>
                            </div>
                            <div class="rating-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?= $i <= $rating['rating'] ? '' : '-o' ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <?php if ($rating['review']): ?>
                        <p class="mb-0 text-muted"><?= htmlspecialchars($rating['review']) ?></p>
                        <?php endif; ?>
                        <small class="text-muted"><?= date('Y-m-d H:i', strtotime($rating['created_at'])) ?></small>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- إحصائيات التحميلات -->
            <?php if (!empty($downloadStats)): ?>
            <div class="detail-container p-4 mb-4">
                <h5 class="section-title">
                    <i class="fas fa-chart-line me-2"></i>
                    إحصائيات التحميلات
                </h5>
                
                <div class="download-chart">
                    <canvas id="downloadChart" width="400" height="200"></canvas>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- الإجراءات السريعة -->
            <div class="detail-container p-4">
                <h5 class="section-title">
                    <i class="fas fa-tools me-2"></i>
                    الإجراءات السريعة
                </h5>
                
                <div class="d-grid gap-2">
                    <a href="<?= url('admin/library/edit/' . $libraryId) ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المادة
                    </a>
                    
                    <?php if ($libraryItem['status'] === 'inactive'): ?>
                    <a href="<?= url('admin/library?action=approve&id=' . $libraryId) ?>" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>
                        موافقة
                    </a>
                    <?php elseif ($libraryItem['status'] === 'active'): ?>
                    <a href="<?= url('admin/library?action=reject&id=' . $libraryId) ?>" class="btn btn-warning">
                        <i class="fas fa-ban me-2"></i>
                        رفض
                    </a>
                    <?php endif; ?>
                    
                    <?php if (!$libraryItem['is_featured']): ?>
                    <a href="<?= url('admin/library?action=feature&id=' . $libraryId) ?>" class="btn btn-info">
                        <i class="fas fa-star me-2"></i>
                        تمييز كمميز
                    </a>
                    <?php else: ?>
                    <a href="<?= url('admin/library?action=unfeature&id=' . $libraryId) ?>" class="btn btn-outline-info">
                        <i class="fas fa-star-o me-2"></i>
                        إلغاء التمييز
                    </a>
                    <?php endif; ?>
                    
                    <?php if ($libraryItem['status'] !== 'archived'): ?>
                    <a href="<?= url('admin/library?action=archive&id=' . $libraryId) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-archive me-2"></i>
                        أرشفة
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?= url('admin/library?action=delete&id=' . $libraryId) ?>" 
                       class="btn btn-danger"
                       onclick="return confirm('هل أنت متأكد من حذف هذه المادة؟ هذا الإجراء لا يمكن التراجع عنه.')">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </a>
                </div>
            </div>
        </div>
        </div>
    </div>
</div>

<?php if (!empty($downloadStats)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('downloadChart').getContext('2d');
    
    const data = {
        labels: <?= json_encode(array_column(array_reverse($downloadStats), 'download_date')) ?>,
        datasets: [{
            label: 'التحميلات',
            data: <?= json_encode(array_column(array_reverse($downloadStats), 'download_count')) ?>,
            backgroundColor: 'rgba(102, 126, 234, 0.2)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 2,
            tension: 0.4
        }]
    };
    
    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    };
    
    new Chart(ctx, config);
});
</script>
<?php endif; ?>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 