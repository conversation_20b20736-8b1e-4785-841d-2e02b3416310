-- تحديث البيانات في جدول users
-- Update users data

-- تحديث العملاء (المستخدمين الذين يبدأ username بـ mariam, ali, nada, mohammed, reem, ahmed_user, sara_user, omar_user, layla_user, khalid_user)
UPDATE users SET user_type = 'user' WHERE username IN ('mariam', 'ali', 'nada', 'mohammed', 'reem', 'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user');

-- تحديث المعالجين (المستخدمين الذين يبدأ username بـ dr.)
UPDATE users SET user_type = 'therapist' WHERE username LIKE 'dr.%';

-- عرض النتائج
SELECT 
    id,
    username,
    email,
    first_name,
    last_name,
    user_type
FROM users 
ORDER BY user_type, first_name, last_name; 