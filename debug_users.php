<?php
/**
 * ملف للتحقق من البيانات الموجودة في جدول users
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>التحقق من البيانات الموجودة في جدول users</h2>";
    
    // التحقق من وجود جدول users
    echo "<h3>التحقق من وجود جدول users:</h3>";
    $tableExists = $db->select("SHOW TABLES LIKE 'users'");
    if (empty($tableExists)) {
        echo "<p style='color: red;'>جدول users غير موجود!</p>";
        echo "<p>يجب إنشاء قاعدة البيانات أولاً.</p>";
        return;
    } else {
        echo "<p style='color: green;'>جدول users موجود ✓</p>";
    }
    
    // عرض هيكل الجدول
    echo "<h3>هيكل جدول users:</h3>";
    $columns = $db->select("DESCRIBE users");
    if ($columns === false) {
        echo "<p style='color: red;'>خطأ في عرض هيكل الجدول</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض جميع المستخدمين
    echo "<h3>جميع المستخدمين:</h3>";
    $allUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type, status, is_active FROM users ORDER BY id");
    
    if ($allUsers === false) {
        echo "<p style='color: red;'>خطأ في الاستعلام أو الاتصال بقاعدة البيانات</p>";
        echo "<p>تفاصيل الخطأ: " . $db->getLastError() . "</p>";
        return;
    }
    
    if (empty($allUsers)) {
        echo "<p style='color: orange;'>لا يوجد مستخدمين في قاعدة البيانات</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>User Type</th><th>Status</th><th>Is Active</th></tr>";
        
        foreach ($allUsers as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . ($user['username'] ?? 'NULL') . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['first_name'] . ' ' . $user['last_name'] . "</td>";
            echo "<td>" . ($user['user_type'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['status'] ?? 'NULL') . "</td>";
            echo "<td>" . ($user['is_active'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // التحقق من العملاء
    echo "<h3>العملاء (user_type = 'user' أو NULL):</h3>";
    $clients = $db->select("
        SELECT id, first_name, last_name, email, user_type, status 
        FROM users 
        WHERE (user_type = 'user' OR user_type IS NULL) AND status = 'active'
        ORDER BY first_name, last_name
    ");
    
    if ($clients === false) {
        echo "<p style='color: red;'>خطأ في استعلام العملاء</p>";
    } elseif (empty($clients)) {
        echo "<p style='color: red;'>لا يوجد عملاء</p>";
    } else {
        echo "<p style='color: green;'>عدد العملاء: " . count($clients) . "</p>";
        foreach ($clients as $client) {
            echo "- " . $client['first_name'] . ' ' . $client['last_name'] . " (" . $client['email'] . ")<br>";
        }
    }
    
    // التحقق من المعالجين
    echo "<h3>المعالجين (user_type = 'therapist'):</h3>";
    $therapists = $db->select("
        SELECT id, first_name, last_name, email, user_type, status, specialization
        FROM users 
        WHERE user_type = 'therapist' AND status = 'active'
        ORDER BY first_name, last_name
    ");
    
    if ($therapists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
    } elseif (empty($therapists)) {
        echo "<p style='color: red;'>لا يوجد معالجين</p>";
    } else {
        echo "<p style='color: green;'>عدد المعالجين: " . count($therapists) . "</p>";
        foreach ($therapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ") - " . ($therapist['specialization'] ?? 'بدون تخصص') . "<br>";
        }
    }
    
    // إحصائيات عامة
    echo "<h3>إحصائيات عامة:</h3>";
    $stats = $db->select("
        SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN user_type = 'user' OR user_type IS NULL THEN 1 ELSE 0 END) as clients,
            SUM(CASE WHEN user_type = 'therapist' THEN 1 ELSE 0 END) as therapists,
            SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admins,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users
        FROM users
    ");
    
    if ($stats === false) {
        echo "<p style='color: red;'>خطأ في استعلام الإحصائيات</p>";
    } else {
        $stat = $stats[0];
        echo "<p>إجمالي المستخدمين: " . $stat['total_users'] . "</p>";
        echo "<p>العملاء: " . $stat['clients'] . "</p>";
        echo "<p>المعالجين: " . $stat['therapists'] . "</p>";
        echo "<p>المديرين: " . $stat['admins'] . "</p>";
        echo "<p>المستخدمين النشطين: " . $stat['active_users'] . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 