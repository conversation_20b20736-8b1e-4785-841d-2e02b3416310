<?php
/**
 * Test script to check system_settings table
 */

// Include the app initialization
require_once 'app/init.php';

try {
    $db = db();
    
    // Check if table exists
    $tableExists = $db->tableExists('system_settings');
    echo "Table system_settings exists: " . ($tableExists ? 'YES' : 'NO') . "\n";
    
    if ($tableExists) {
        // Get all settings
        $settings = $db->select("SELECT setting_key, setting_value FROM system_settings");
        
        if ($settings && is_array($settings)) {
            echo "Found " . count($settings) . " settings:\n";
            foreach ($settings as $setting) {
                echo "- " . $setting['setting_key'] . ": " . $setting['setting_value'] . "\n";
            }
        } else {
            echo "No settings found or error occurred\n";
            echo "Settings variable type: " . gettype($settings) . "\n";
            if ($settings === false) {
                echo "Database error: " . $db->getLastError() . "\n";
            }
        }
    } else {
        echo "Table does not exist. Creating it...\n";
        
        // Create the table
        $createTableSQL = "
        CREATE TABLE system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $result = $db->execute($createTableSQL);
        echo "Table creation result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
        
        if ($result) {
            // Insert default settings
            $defaultSettings = [
                ['site_name', 'منصة نفسي', 'اسم الموقع'],
                ['site_description', 'منصة متخصصة في الاستشارات النفسية عبر الإنترنت', 'وصف الموقع'],
                ['maintenance_mode', 'false', 'وضع الصيانة'],
                ['registration_enabled', 'true', 'تفعيل التسجيل'],
                ['email_verification_required', 'true', 'التحقق من البريد الإلكتروني مطلوب'],
                ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
                ['session_timeout', '3600', 'مهلة الجلسة بالثواني'],
                ['default_currency', 'SAR', 'العملة الافتراضية'],
                ['min_session_duration', '30', 'الحد الأدنى لمدة الجلسة بالدقائق'],
                ['max_session_duration', '120', 'الحد الأقصى لمدة الجلسة بالدقائق']
            ];
            
            foreach ($defaultSettings as $setting) {
                $insertSQL = "INSERT INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
                $db->execute($insertSQL, $setting);
            }
            
            echo "Default settings inserted successfully\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
} 