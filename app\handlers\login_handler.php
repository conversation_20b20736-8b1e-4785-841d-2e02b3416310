<?php
/**
 * معالج تسجيل الدخول الجديد والمبسط
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// التحقق من نوع الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: /nafsi_platform/login');
    exit;
}

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // الحصول على البيانات المرسلة
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember']) && $_POST['remember'] === 'on';
    $csrfToken = $_POST['csrf_token'] ?? '';

    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrfToken)) {
        throw new Exception('رمز الأمان غير صحيح');
    }

    // التحقق من صحة البيانات الأساسية
    if (empty($email) || empty($password)) {
        throw new Exception('البريد الإلكتروني وكلمة المرور مطلوبان');
    }

    // التحقق من صحة البريد الإلكتروني
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('صيغة البريد الإلكتروني غير صحيحة');
    }

    // تهيئة كلاس المستخدم
    $user = new User();

    // البحث عن المستخدم
    $userData = $user->getByEmail($email);

    if (!$userData) {
        throw new Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }

    // التحقق من حالة المستخدم
    if ($userData['status'] != 1) {
        throw new Exception('الحساب غير مفعل. يرجى التواصل مع الإدارة');
    }

    // التحقق من كلمة المرور
    if (!$user->verifyPassword($userData, $password)) {
        throw new Exception('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }

    // تسجيل الدخول ناجح - إنشاء الجلسة
    $_SESSION['user_id'] = $userData['id'];
    $_SESSION['user_email'] = $userData['email'];
    $_SESSION['user_name'] = $userData['first_name'] . ' ' . $userData['last_name'];
    $_SESSION['user_type'] = $userData['user_type'];
    $_SESSION['logged_in'] = true;
    $_SESSION['login_time'] = time();

    // تحديث آخر تسجيل دخول
    $db = db();
    $db->update('users', ['last_login' => date('Y-m-d H:i:s')], ['id' => $userData['id']]);

    // إعداد "تذكرني" إذا تم اختياره
    if ($rememberMe) {
        $token = bin2hex(random_bytes(32));
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 يوم

        // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول للرموز لاحقاً)
        $_SESSION['remember_token'] = $token;
    }

    // تعيين رسالة نجاح
    setAlert('تم تسجيل الدخول بنجاح! مرحباً بك في منصة نفسي', 'success');

    // تحديد صفحة إعادة التوجيه حسب نوع المستخدم
    $redirectUrl = '/nafsi_platform/dashboard'; // افتراضي

    switch ($userData['user_type']) {
        case 'admin':
            $redirectUrl = '/nafsi_platform/admin/dashboard';
            break;
        case 'therapist':
            $redirectUrl = '/nafsi_platform/therapist/dashboard';
            break;
        case 'client':
            $redirectUrl = '/nafsi_platform/dashboard';
            break;
    }

    // إعادة التوجيه
    header('Location: ' . $redirectUrl);
    exit;

} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("Login Error: " . $e->getMessage() . " | Email: " . ($email ?? 'N/A'));

    // تعيين رسالة خطأ
    setAlert($e->getMessage(), 'danger');

    // إعادة التوجيه لصفحة تسجيل الدخول
    header('Location: /nafsi_platform/login');
    exit;
}
?>