<?php
/**
 * إدارة المستخدمين - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/users');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معالجة الطلبات
$action = $_GET['action'] ?? '';
$userId = $_GET['id'] ?? 0;

// معالجة إضافة مستخدم جديد
if (isset($_POST['action']) && $_POST['action'] === 'add_user') {
    try {
        $db = db();
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['first_name', 'last_name', 'email', 'password', 'user_type'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("حقل $field مطلوب");
            }
        }
        
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("البريد الإلكتروني غير صحيح");
        }
        
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        $existingUser = $db->select("SELECT id FROM users WHERE email = ?", [$_POST['email']]);
        if (!empty($existingUser)) {
            throw new Exception("البريد الإلكتروني مستخدم بالفعل");
        }
        
        // تشفير كلمة المرور
        $passwordHash = password_hash($_POST['password'], PASSWORD_DEFAULT);
        
        // إعداد البيانات للإدراج
        $userData = [
            'first_name' => trim($_POST['first_name']),
            'last_name' => trim($_POST['last_name']),
            'email' => trim($_POST['email']),
            'password_hash' => $passwordHash,
            'user_type' => $_POST['user_type'],
            'is_active' => 1, // تفعيل المستخدم تلقائياً
            'is_verified' => 1, // تأكيد البريد الإلكتروني تلقائياً
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // إضافة حقول اختيارية
        if (!empty($_POST['phone'])) {
            $userData['phone'] = trim($_POST['phone']);
        }
        
        if (!empty($_POST['date_of_birth'])) {
            $userData['date_of_birth'] = $_POST['date_of_birth'];
        }
        
        if (!empty($_POST['gender'])) {
            $userData['gender'] = $_POST['gender'];
        }
        
        // إدراج المستخدم الجديد
        $columns = implode(', ', array_keys($userData));
        $placeholders = ':' . implode(', :', array_keys($userData));
        $sql = "INSERT INTO users ($columns) VALUES ($placeholders)";
        
        $userId = $db->insert($sql, $userData);
        
        if ($userId) {
            setAlert('تم إضافة المستخدم الجديد بنجاح', 'success');
        } else {
            throw new Exception("فشل في إضافة المستخدم");
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

if ($action && $userId) {
    try {
        $db = db();
        
        switch ($action) {
            case 'activate':
                $db->update("UPDATE users SET is_active = 1 WHERE id = ?", [$userId]);
                setAlert('تم تفعيل المستخدم بنجاح', 'success');
                break;
                
            case 'deactivate':
                $db->update("UPDATE users SET is_active = 0 WHERE id = ?", [$userId]);
                setAlert('تم إيقاف المستخدم بنجاح', 'success');
                break;
                
            case 'delete':
                $db->delete("DELETE FROM users WHERE id = ?", [$userId]);
                setAlert('تم حذف المستخدم بنجاح', 'success');
                break;
                
            case 'make_admin':
                $db->update("UPDATE users SET user_type = 'admin' WHERE id = ?", [$userId]);
                setAlert('تم تعيين المستخدم كمدير بنجاح', 'success');
                break;
                
            case 'make_therapist':
                $db->update("UPDATE users SET user_type = 'therapist' WHERE id = ?", [$userId]);
                setAlert('تم تعيين المستخدم كمعالج بنجاح', 'success');
                break;
                
            case 'make_user':
                $db->update("UPDATE users SET user_type = 'user' WHERE id = ?", [$userId]);
                setAlert('تم تعيين المستخدم كمستخدم عادي بنجاح', 'success');
                break;
        }
        
        header('Location: ' . $_SERVER['REQUEST_URI']);
        exit;
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// معاملات البحث والتصفية
$search = $_GET['search'] ?? '';
$userType = $_GET['user_type'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 10;
$offset = ($page - 1) * $perPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

if ($userType) {
    $whereConditions[] = "user_type = ?";
    $params[] = $userType;
}

if ($status) {
    $whereConditions[] = "is_active = ?";
    $params[] = ($status === 'active') ? 1 : 0;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات عامة
    $stats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM users")[0]['count'] ?? 0,
        'active' => $db->select("SELECT COUNT(*) as count FROM users WHERE is_active = 1")[0]['count'] ?? 0,
        'therapists' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'")[0]['count'] ?? 0,
        'clients' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'")[0]['count'] ?? 0
    ];
    
    // إجمالي عدد المستخدمين للصفحات
    $totalUsers = $db->select("SELECT COUNT(*) as count FROM users $whereClause", $params)[0]['count'] ?? 0;
    $totalPages = ceil($totalUsers / $perPage);
    
    // قائمة المستخدمين
    $users = $db->select("
        SELECT id, first_name, last_name, email, user_type, is_active as status, created_at, last_login 
        FROM users 
        $whereClause 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ", array_merge($params, [$perPage, $offset]));
    
} catch (Exception $e) {
    $users = [];
    $totalUsers = 0;
    $totalPages = 0;
    $stats = ['total' => 0, 'active' => 0, 'therapists' => 0, 'clients' => 0];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إدارة المستخدمين';
$currentPage = 'admin_users';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }

    .table-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.375rem;
    }

    .search-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </h2>
                            <p class="mb-0">إدارة جميع مستخدمي المنصة وتحديث صلاحياتهم</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light me-2">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['total'] ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['active'] ?></h3>
                    <p class="mb-0">المستخدمين النشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['therapists'] ?></h3>
                    <p class="mb-0">المعالجين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $stats['clients'] ?></h3>
                    <p class="mb-0">العملاء</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات البحث والتصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-container">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" placeholder="البحث بالاسم أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-2">
                            <label for="user_type" class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="user_type" name="user_type">
                                <option value="">الكل</option>
                                <option value="admin" <?= $userType === 'admin' ? 'selected' : '' ?>>مدير</option>
                                <option value="therapist" <?= $userType === 'therapist' ? 'selected' : '' ?>>معالج</option>
                                <option value="user" <?= $userType === 'user' ? 'selected' : '' ?>>مستخدم</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">الكل</option>
                                <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>نشط</option>
                                <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <a href="<?= url('admin/users') ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-refresh"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المستخدمين -->
    <div class="row">
        <div class="col-12">
            <div class="card table-container">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة المستخدمين
                        <span class="badge bg-light text-primary ms-2"><?= $totalUsers ?> مستخدم</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($users)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد مستخدمين</h5>
                        <p class="text-muted">لم يتم العثور على مستخدمين يطابقون معايير البحث</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                <?= strtoupper(substr($user['first_name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></h6>
                                                <small class="text-muted">ID: <?= $user['id'] ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($user['email']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $user['user_type'] === 'admin' ? 'danger' : ($user['user_type'] === 'therapist' ? 'success' : 'primary') ?>">
                                            <?= $user['user_type'] === 'admin' ? 'مدير' : ($user['user_type'] === 'therapist' ? 'معالج' : 'مستخدم') ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $user['status'] == 1 ? 'success' : 'secondary' ?>">
                                            <?= $user['status'] == 1 ? 'نشط' : 'غير نشط' ?>
                                        </span>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($user['created_at'])) ?></td>
                                    <td>
                                        <?= $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول' ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($user['status'] == 1): ?>
                                            <a href="?action=deactivate&id=<?= $user['id'] ?>" 
                                               class="btn btn-sm btn-warning btn-action" 
                                               onclick="return confirm('هل أنت متأكد من إيقاف هذا المستخدم؟')">
                                                <i class="fas fa-pause"></i>
                                            </a>
                                            <?php else: ?>
                                            <a href="?action=activate&id=<?= $user['id'] ?>" 
                                               class="btn btn-sm btn-success btn-action">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary btn-action dropdown-toggle" 
                                                        data-bs-toggle="dropdown">
                                                    <i class="fas fa-user-edit"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="?action=make_admin&id=<?= $user['id'] ?>">
                                                        <i class="fas fa-crown text-danger"></i> تعيين كمدير
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="?action=make_therapist&id=<?= $user['id'] ?>">
                                                        <i class="fas fa-user-md text-success"></i> تعيين كمعالج
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="?action=make_user&id=<?= $user['id'] ?>">
                                                        <i class="fas fa-user text-primary"></i> تعيين كمستخدم
                                                    </a></li>
                                                </ul>
                                            </div>
                                            
                                            <a href="?action=delete&id=<?= $user['id'] ?>" 
                                               class="btn btn-sm btn-danger btn-action" 
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="ترقيم الصفحات">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&user_type=<?= urlencode($userType) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&user_type=<?= urlencode($userType) ?>&status=<?= urlencode($status) ?>">
                            <?= $i ?>
                        </a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&user_type=<?= urlencode($userType) ?>&status=<?= urlencode($status) ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" id="addUserForm">
                <input type="hidden" name="action" value="add_user">
                <div class="modal-body">
                    <div class="row">
                        <!-- الاسم الأول -->
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">
                                <i class="fas fa-user text-primary"></i>
                                الاسم الأول <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   required placeholder="أدخل الاسم الأول">
                        </div>
                        
                        <!-- الاسم الأخير -->
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">
                                <i class="fas fa-user text-primary"></i>
                                الاسم الأخير <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   required placeholder="أدخل الاسم الأخير">
                        </div>
                        
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope text-primary"></i>
                                البريد الإلكتروني <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   required placeholder="<EMAIL>">
                        </div>
                        
                        <!-- كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock text-primary"></i>
                                كلمة المرور <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       required placeholder="أدخل كلمة المرور" minlength="6">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone text-primary"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="+966 50 123 4567">
                        </div>
                        
                        <!-- تاريخ الميلاد -->
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">
                                <i class="fas fa-calendar text-primary"></i>
                                تاريخ الميلاد
                            </label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                        </div>
                        
                        <!-- الجنس -->
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">
                                <i class="fas fa-venus-mars text-primary"></i>
                                الجنس
                            </label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">اختر الجنس</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                                <option value="other">آخر</option>
                            </select>
                        </div>
                        
                        <!-- نوع المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="user_type" class="form-label">
                                <i class="fas fa-user-tag text-primary"></i>
                                نوع المستخدم <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="user_type" name="user_type" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="user">مستخدم عادي</option>
                                <option value="therapist">معالج</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript للتفاعل -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');
    
    if (togglePassword && password) {
        togglePassword.addEventListener('click', function() {
            const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
            password.setAttribute('type', type);
            this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
        });
    }
    
    // التحقق من صحة النموذج
    const addUserForm = document.getElementById('addUserForm');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            const password = document.getElementById('password');
            const email = document.getElementById('email');
            
            // التحقق من طول كلمة المرور
            if (password.value.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                password.focus();
                return false;
            }
            
            // التحقق من صحة البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email.value)) {
                e.preventDefault();
                alert('يرجى إدخال بريد إلكتروني صحيح');
                email.focus();
                return false;
            }
        });
    }
    
    // إعادة تعيين النموذج عند إغلاق Modal
    const addUserModal = document.getElementById('addUserModal');
    if (addUserModal) {
        addUserModal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('addUserForm').reset();
            const passwordField = document.getElementById('password');
            if (passwordField) {
                passwordField.setAttribute('type', 'password');
            }
            const toggleBtn = document.getElementById('togglePassword');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    }
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?>