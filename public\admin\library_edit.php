<?php
/**
 * تعديل مادة في المكتبة - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/library/edit');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على معرف المادة
$libraryId = intval($_GET['id'] ?? 0);
if (!$libraryId) {
    setAlert('معرف المادة غير صحيح', 'danger');
    header('Location: ' . url('admin/library'));
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على بيانات المادة
try {
    $db = db();
    $libraryItem = $db->select("
        SELECT l.*, u.first_name, u.last_name, u.username as creator_name
        FROM library l
        LEFT JOIN users u ON l.created_by = u.id
        WHERE l.id = ?
    ", [$libraryId]);
    
    if (empty($libraryItem)) {
        setAlert('المادة غير موجودة', 'danger');
        header('Location: ' . url('admin/library'));
        exit;
    }
    
    $libraryItem = $libraryItem[0];
    
} catch (Exception $e) {
    setAlert('حدث خطأ في تحميل بيانات المادة: ' . $e->getMessage(), 'danger');
    header('Location: ' . url('admin/library'));
    exit;
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من البيانات
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $author = trim($_POST['author'] ?? '');
        $publisher = trim($_POST['publisher'] ?? '');
        $isbn = trim($_POST['isbn'] ?? '');
        $publicationYear = intval($_POST['publication_year'] ?? 0);
        $language = trim($_POST['language'] ?? 'Arabic');
        $category = trim($_POST['category'] ?? '');
        $fileType = $_POST['file_type'] ?? '';
        $fileUrl = trim($_POST['file_url'] ?? '');
        $isFree = isset($_POST['is_free']) ? 1 : 0;
        $price = floatval($_POST['price'] ?? 0);
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        $status = $_POST['status'] ?? 'active';
        
        // التحقق من الحقول المطلوبة
        if (empty($title)) {
            throw new Exception('عنوان المادة مطلوب');
        }
        
        if (empty($fileType)) {
            throw new Exception('نوع الملف مطلوب');
        }
        
        if (empty($fileUrl)) {
            throw new Exception('رابط الملف مطلوب');
        }
        
        // معالجة التصنيفات
        $tags = [];
        if (!empty($_POST['tags'])) {
            $tagsArray = explode(',', $_POST['tags']);
            $tags = array_map('trim', $tagsArray);
            $tags = array_filter($tags);
        }
        
        // تحديث المادة في قاعدة البيانات
        $libraryData = [
            'title' => $title,
            'description' => $description,
            'author' => $author,
            'publisher' => $publisher,
            'isbn' => $isbn,
            'publication_year' => $publicationYear > 0 ? $publicationYear : null,
            'language' => $language,
            'category' => $category,
            'tags' => json_encode($tags),
            'file_type' => $fileType,
            'file_url' => $fileUrl,
            'is_free' => $isFree,
            'price' => $price,
            'is_featured' => $isFeatured,
            'status' => $status
        ];
        
        $updated = $db->update("library", $libraryData, "id = ?", [$libraryId]);
        
        if ($updated) {
            setAlert('تم تحديث المادة بنجاح', 'success');
            header('Location: ' . url('admin/library'));
            exit;
        } else {
            throw new Exception('فشل في تحديث المادة');
        }
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// تحويل العلامات إلى نص
$tags = [];
if (!empty($libraryItem['tags'])) {
    $tagsArray = json_decode($libraryItem['tags'], true);
    if (is_array($tagsArray)) {
        $tags = $tagsArray;
    }
}
$tagsText = implode(', ', $tags);

// تعيين متغيرات الصفحة
$pageTitle = 'تعديل المادة: ' . $libraryItem['title'];
$currentPage = 'admin_library';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<!-- تضمين الشريط الجانبي -->
<?php include 'includes/sidebar.php'; ?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .form-section {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 2rem;
        margin-bottom: 2rem;
    }

    .form-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
    }

    .section-title {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
    }

    .file-type-preview {
        padding: 1rem;
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        text-align: center;
        margin-top: 0.5rem;
        transition: all 0.3s ease;
    }

    .file-type-preview:hover {
        border-color: #667eea;
        background-color: #f8f9ff;
    }

    .library-info {
        background: #f8f9ff;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .info-label {
        font-weight: 600;
        color: #667eea;
    }
</style>

<div class="admin-main-content">
    <div class="container-fluid py-4">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="mb-2">
                            <i class="fas fa-edit me-3"></i>
                            تعديل المادة
                        </h1>
                        <p class="mb-0 opacity-75">تعديل مادة: <?= htmlspecialchars($libraryItem['title']) ?></p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= url('admin/library') ?>" class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للمكتبة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المادة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="library-info">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المادة
                </h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">المؤلف:</span>
                            <span><?= htmlspecialchars($libraryItem['author'] ?? 'غير محدد') ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">التصنيف:</span>
                            <span><?= htmlspecialchars($libraryItem['category'] ?? 'غير محدد') ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">نوع الملف:</span>
                            <span><?= strtoupper($libraryItem['file_type']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">الحالة:</span>
                            <span class="badge bg-<?= $libraryItem['status'] === 'active' ? 'success' : ($libraryItem['status'] === 'inactive' ? 'warning' : 'secondary') ?>">
                                <?= $libraryItem['status'] === 'active' ? 'نشط' : ($libraryItem['status'] === 'inactive' ? 'في الانتظار' : 'مؤرشف') ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">التحميلات:</span>
                            <span><?= number_format($libraryItem['download_count']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">المشاهدات:</span>
                            <span><?= number_format($libraryItem['view_count']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">التقييم:</span>
                            <span><?= number_format($libraryItem['rating'], 1) ?>/5 (<?= $libraryItem['total_ratings'] ?> تقييم)</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-item">
                            <span class="info-label">تاريخ الإضافة:</span>
                            <span><?= date('Y-m-d', strtotime($libraryItem['created_at'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج التعديل -->
    <div class="row">
        <div class="col-12">
            <div class="form-container p-4">
                <form method="POST" enctype="multipart/form-data">
                    
                    <!-- معلومات أساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>
                            المعلومات الأساسية
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">عنوان المادة *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?= htmlspecialchars($_POST['title'] ?? $libraryItem['title']) ?>" 
                                           required placeholder="أدخل عنوان المادة">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category" class="form-label">التصنيف</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">اختر التصنيف</option>
                                        <option value="علم النفس" <?= ($_POST['category'] ?? $libraryItem['category']) === 'علم النفس' ? 'selected' : '' ?>>علم النفس</option>
                                        <option value="العلاج النفسي" <?= ($_POST['category'] ?? $libraryItem['category']) === 'العلاج النفسي' ? 'selected' : '' ?>>العلاج النفسي</option>
                                        <option value="التنمية البشرية" <?= ($_POST['category'] ?? $libraryItem['category']) === 'التنمية البشرية' ? 'selected' : '' ?>>التنمية البشرية</option>
                                        <option value="العلاقات" <?= ($_POST['category'] ?? $libraryItem['category']) === 'العلاقات' ? 'selected' : '' ?>>العلاقات</option>
                                        <option value="الأسرة" <?= ($_POST['category'] ?? $libraryItem['category']) === 'الأسرة' ? 'selected' : '' ?>>الأسرة</option>
                                        <option value="الأطفال" <?= ($_POST['category'] ?? $libraryItem['category']) === 'الأطفال' ? 'selected' : '' ?>>الأطفال</option>
                                        <option value="المراهقة" <?= ($_POST['category'] ?? $libraryItem['category']) === 'المراهقة' ? 'selected' : '' ?>>المراهقة</option>
                                        <option value="القلق والاكتئاب" <?= ($_POST['category'] ?? $libraryItem['category']) === 'القلق والاكتئاب' ? 'selected' : '' ?>>القلق والاكتئاب</option>
                                        <option value="الإدمان" <?= ($_POST['category'] ?? $libraryItem['category']) === 'الإدمان' ? 'selected' : '' ?>>الإدمان</option>
                                        <option value="أخرى" <?= ($_POST['category'] ?? $libraryItem['category']) === 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المادة</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="أدخل وصفاً مفصلاً للمادة"><?= htmlspecialchars($_POST['description'] ?? $libraryItem['description']) ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="author" class="form-label">المؤلف</label>
                                    <input type="text" class="form-control" id="author" name="author" 
                                           value="<?= htmlspecialchars($_POST['author'] ?? $libraryItem['author']) ?>" 
                                           placeholder="اسم المؤلف">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="publisher" class="form-label">الناشر</label>
                                    <input type="text" class="form-control" id="publisher" name="publisher" 
                                           value="<?= htmlspecialchars($_POST['publisher'] ?? $libraryItem['publisher']) ?>" 
                                           placeholder="اسم الناشر">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="isbn" class="form-label">رقم ISBN</label>
                                    <input type="text" class="form-control" id="isbn" name="isbn" 
                                           value="<?= htmlspecialchars($_POST['isbn'] ?? $libraryItem['isbn']) ?>" 
                                           placeholder="رقم ISBN">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="publication_year" class="form-label">سنة النشر</label>
                                    <input type="number" class="form-control" id="publication_year" name="publication_year" 
                                           value="<?= htmlspecialchars($_POST['publication_year'] ?? $libraryItem['publication_year']) ?>" 
                                           min="1900" max="<?= date('Y') + 1 ?>" placeholder="سنة النشر">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="Arabic" <?= ($_POST['language'] ?? $libraryItem['language']) === 'Arabic' ? 'selected' : '' ?>>العربية</option>
                                        <option value="English" <?= ($_POST['language'] ?? $libraryItem['language']) === 'English' ? 'selected' : '' ?>>الإنجليزية</option>
                                        <option value="French" <?= ($_POST['language'] ?? $libraryItem['language']) === 'French' ? 'selected' : '' ?>>الفرنسية</option>
                                        <option value="German" <?= ($_POST['language'] ?? $libraryItem['language']) === 'German' ? 'selected' : '' ?>>الألمانية</option>
                                        <option value="Spanish" <?= ($_POST['language'] ?? $libraryItem['language']) === 'Spanish' ? 'selected' : '' ?>>الإسبانية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الملف -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-file me-2"></i>
                            معلومات الملف
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="file_type" class="form-label">نوع الملف *</label>
                                    <select class="form-select" id="file_type" name="file_type" required>
                                        <option value="">اختر نوع الملف</option>
                                        <option value="pdf" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'pdf' ? 'selected' : '' ?>>PDF</option>
                                        <option value="doc" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'doc' ? 'selected' : '' ?>>DOC</option>
                                        <option value="docx" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'docx' ? 'selected' : '' ?>>DOCX</option>
                                        <option value="epub" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'epub' ? 'selected' : '' ?>>EPUB</option>
                                        <option value="video" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'video' ? 'selected' : '' ?>>فيديو</option>
                                        <option value="audio" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'audio' ? 'selected' : '' ?>>صوتي</option>
                                        <option value="link" <?= ($_POST['file_type'] ?? $libraryItem['file_type']) === 'link' ? 'selected' : '' ?>>رابط</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="file_url" class="form-label">رابط الملف *</label>
                                    <input type="url" class="form-control" id="file_url" name="file_url" 
                                           value="<?= htmlspecialchars($_POST['file_url'] ?? $libraryItem['file_url']) ?>" 
                                           required placeholder="رابط الملف أو الفيديو">
                                </div>
                            </div>
                        </div>
                        
                        <div id="file_preview" class="file-type-preview">
                            <i class="fas fa-file fa-2x text-muted mb-2"></i>
                            <p class="mb-0 text-muted">معاينة نوع الملف</p>
                        </div>
                    </div>
                    
                    <!-- الإعدادات -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_free" name="is_free" 
                                               <?= isset($_POST['is_free']) ? ($_POST['is_free'] ? 'checked' : '') : ($libraryItem['is_free'] ? 'checked' : '') ?>>
                                        <label class="form-check-label" for="is_free">
                                            مجاني
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                               <?= isset($_POST['is_featured']) ? ($_POST['is_featured'] ? 'checked' : '') : ($libraryItem['is_featured'] ? 'checked' : '') ?>>
                                        <label class="form-check-label" for="is_featured">
                                            مميز
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?= ($_POST['status'] ?? $libraryItem['status']) === 'active' ? 'selected' : '' ?>>نشط</option>
                                        <option value="inactive" <?= ($_POST['status'] ?? $libraryItem['status']) === 'inactive' ? 'selected' : '' ?>>في الانتظار</option>
                                        <option value="archived" <?= ($_POST['status'] ?? $libraryItem['status']) === 'archived' ? 'selected' : '' ?>>مؤرشف</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">السعر (إذا لم تكن مجانية)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="price" name="price" 
                                               value="<?= htmlspecialchars($_POST['price'] ?? $libraryItem['price']) ?>" 
                                               min="0" step="0.01" placeholder="0.00">
                                        <span class="input-group-text">ر.س</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tags" class="form-label">العلامات (اختيارية)</label>
                                    <input type="text" class="form-control" id="tags" name="tags" 
                                           value="<?= htmlspecialchars($_POST['tags'] ?? $tagsText) ?>" 
                                           placeholder="أدخل العلامات مفصولة بفواصل">
                                    <small class="form-text text-muted">مثال: علم النفس، علاج، تنمية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= url('admin/library') ?>" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileTypeSelect = document.getElementById('file_type');
    const filePreview = document.getElementById('file_preview');
    const isFreeCheckbox = document.getElementById('is_free');
    const priceInput = document.getElementById('price');
    
    // معاينة نوع الملف
    function updateFilePreview() {
        const fileType = fileTypeSelect.value;
        const preview = filePreview;
        
        if (fileType) {
            preview.style.display = 'block';
            const icon = preview.querySelector('i');
            const text = preview.querySelector('p');
            
            switch(fileType) {
                case 'pdf':
                    icon.className = 'fas fa-file-pdf fa-2x text-danger mb-2';
                    text.textContent = 'ملف PDF';
                    break;
                case 'doc':
                case 'docx':
                    icon.className = 'fas fa-file-word fa-2x text-primary mb-2';
                    text.textContent = 'ملف Word';
                    break;
                case 'epub':
                    icon.className = 'fas fa-book fa-2x text-success mb-2';
                    text.textContent = 'ملف EPUB';
                    break;
                case 'video':
                    icon.className = 'fas fa-video fa-2x text-warning mb-2';
                    text.textContent = 'ملف فيديو';
                    break;
                case 'audio':
                    icon.className = 'fas fa-music fa-2x text-info mb-2';
                    text.textContent = 'ملف صوتي';
                    break;
                case 'link':
                    icon.className = 'fas fa-link fa-2x text-secondary mb-2';
                    text.textContent = 'رابط خارجي';
                    break;
            }
        } else {
            preview.style.display = 'none';
        }
    }
    
    fileTypeSelect.addEventListener('change', updateFilePreview);
    
    // تحديث المعاينة عند تحميل الصفحة
    updateFilePreview();
    
    // إدارة السعر عند تغيير حالة المجانية
    isFreeCheckbox.addEventListener('change', function() {
        if (this.checked) {
            priceInput.value = '0';
            priceInput.disabled = true;
        } else {
            priceInput.disabled = false;
        }
    });
    
    // تعطيل السعر إذا كانت المادة مجانية
    if (isFreeCheckbox.checked) {
        priceInput.disabled = true;
    }
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 