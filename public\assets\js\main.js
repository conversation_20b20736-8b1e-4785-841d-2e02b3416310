/**
 * ملف JavaScript الرئيسي لمنصة نفسي
 * Main JavaScript file for Nafsi Platform
 */

// انتظار تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    
    // تهيئة جميع الوظائف
    initBackToTop();
    initSmoothScroll();
    initFormValidation();
    initTooltips();
    initAlerts();
    initLoadingStates();
    initResponsiveMenu();
    
    console.log('منصة نفسي - تم تحميل JavaScript بنجاح');
});

/**
 * زر العودة للأعلى
 */
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    if (!backToTopBtn) return;
    
    // إظهار/إخفاء الزر عند التمرير
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.remove('d-none');
        } else {
            backToTopBtn.classList.add('d-none');
        }
    });
    
    // التمرير للأعلى عند النقر
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * التمرير السلس
 */
function initSmoothScroll() {
    // جميع الروابط الداخلية
    const internalLinks = document.querySelectorAll('a[href^="#"]');
    
    internalLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * التحقق من صحة النماذج
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
            }
        });
    });
}

/**
 * التحقق من صحة نموذج معين
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            markFieldAsInvalid(input, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            markFieldAsValid(input);
        }
        
        // التحقق من البريد الإلكتروني
        if (input.type === 'email' && input.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(input.value)) {
                markFieldAsInvalid(input, 'يرجى إدخال بريد إلكتروني صحيح');
                isValid = false;
            }
        }
        
        // التحقق من كلمة المرور
        if (input.type === 'password' && input.value) {
            if (input.value.length < 6) {
                markFieldAsInvalid(input, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                isValid = false;
            }
        }
    });
    
    return isValid;
}

/**
 * تمييز الحقل كخطأ
 */
function markFieldAsInvalid(field, message) {
    field.classList.add('is-invalid');
    field.classList.remove('is-valid');
    
    // إزالة رسالة الخطأ السابقة
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // إضافة رسالة الخطأ الجديدة
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * تمييز الحقل كصحيح
 */
function markFieldAsValid(field) {
    field.classList.add('is-valid');
    field.classList.remove('is-invalid');
    
    // إزالة رسالة الخطأ
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * تهيئة التلميحات
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة التنبيهات
 */
function initAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    });
}

/**
 * إظهار تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
    alertContainer.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة التنبيه في بداية الصفحة
    const main = document.querySelector('main');
    if (main) {
        main.insertBefore(alertContainer, main.firstChild);
    }
    
    // إخفاء التنبيه تلقائياً
    setTimeout(() => {
        if (alertContainer) {
            const bsAlert = new bootstrap.Alert(alertContainer);
            bsAlert.close();
        }
    }, 5000);
}

/**
 * الحصول على أيقونة التنبيه
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * تهيئة حالات التحميل
 */
function initLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الإرسال...';
            }
        });
    });
}

/**
 * تهيئة القائمة المتجاوبة
 */
function initResponsiveMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        // إغلاق القائمة عند النقر على رابط
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 992) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }
            });
        });
    }
}

/**
 * تحميل المزيد من المحتوى
 */
function loadMoreContent(url, container, page = 1) {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'text-center py-3';
    loadingDiv.innerHTML = '<div class="spinner-border text-primary"></div>';
    container.appendChild(loadingDiv);
    
    fetch(`${url}?page=${page}`)
        .then(response => response.json())
        .then(data => {
            loadingDiv.remove();
            
            if (data.content) {
                container.insertAdjacentHTML('beforeend', data.content);
                
                // إضافة زر "تحميل المزيد" إذا كان هناك المزيد
                if (data.hasMore) {
                    const loadMoreBtn = document.createElement('button');
                    loadMoreBtn.className = 'btn btn-outline-primary mt-3';
                    loadMoreBtn.textContent = 'تحميل المزيد';
                    loadMoreBtn.addEventListener('click', () => {
                        loadMoreContent(url, container, page + 1);
                        loadMoreBtn.remove();
                    });
                    container.appendChild(loadMoreBtn);
                }
            }
        })
        .catch(error => {
            loadingDiv.remove();
            showAlert('حدث خطأ في تحميل المحتوى', 'danger');
            console.error('Error loading content:', error);
        });
}

/**
 * البحث المباشر
 */
function initLiveSearch(searchInput, resultsContainer, searchUrl) {
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        
        const query = this.value.trim();
        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            performSearch(query, resultsContainer, searchUrl);
        }, 300);
    });
}

/**
 * تنفيذ البحث
 */
function performSearch(query, container, url) {
    container.innerHTML = '<div class="text-center"><div class="spinner-border text-primary"></div></div>';
    
    fetch(`${url}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.results && data.results.length > 0) {
                container.innerHTML = data.results;
            } else {
                container.innerHTML = '<div class="text-center text-muted">لا توجد نتائج</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="text-center text-danger">حدث خطأ في البحث</div>';
            console.error('Search error:', error);
        });
}

/**
 * تحميل الصور بشكل تدريجي
 */
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

/**
 * تحسين الأداء
 */
function initPerformanceOptimizations() {
    // تأجيل تحميل الصور غير المرئية
    if ('IntersectionObserver' in window) {
        initLazyLoading();
    }
    
    // تحسين التمرير
    let ticking = false;
    function updateOnScroll() {
        // تحديث العناصر المرئية فقط
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    });
}

/**
 * إعدادات إضافية للصفحة
 */
function initPageSpecific() {
    // تهيئة خاصة بصفحة تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            togglePassword.addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const icon = this.querySelector('i');
                
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }
    }
    
    // تهيئة خاصة بصفحة التسجيل
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        
        if (passwordField && confirmPasswordField) {
            confirmPasswordField.addEventListener('input', function() {
                if (this.value !== passwordField.value) {
                    markFieldAsInvalid(this, 'كلمات المرور غير متطابقة');
                } else {
                    markFieldAsValid(this);
                }
            });
        }
    }
}

// تشغيل الإعدادات الإضافية
document.addEventListener('DOMContentLoaded', function() {
    initPageSpecific();
    initPerformanceOptimizations();
});

// تصدير الدوال للاستخدام الخارجي
window.NafsiPlatform = {
    showAlert,
    loadMoreContent,
    initLiveSearch,
    validateForm
};
