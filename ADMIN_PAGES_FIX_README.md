# إصلاح مشكلة تضمين الملفات في صفحات المشرف

## المشكلة
كانت هناك مشكلة في صفحات لوحة تحكم المشرف حيث تظهر رسالة خطأ:
```
include_once(../includes/header.php): Failed to open stream: No such file or directory
```

## سبب المشكلة
المشكلة كانت في استخدام المسارات النسبية (`../includes/header.php`) في ملفات المشرف، بينما يتم تحميل هذه الملفات من خلال Front Controller مما يجعل المسارات النسبية غير صحيحة.

## التصحيحات المطبقة

### 1. إصلاح جميع ملفات admin
تم تحديث المسارات في الملفات التالية:
- `public/admin/dashboard.php`
- `public/admin/users.php`
- `public/admin/specialists.php`
- `public/admin/simple_dashboard.php`
- `public/admin/settings.php`
- `public/admin/reports.php`
- `public/admin/content.php`
- `public/admin/appointments.php`

### 2. إصلاح ملفات therapist
تم تحديث المسارات في:
- `public/therapist/dashboard.php`

### 3. التغييرات المطبقة
تم تغيير جميع المسارات من:
```php
include_once '../includes/header.php';
include_once '../includes/footer.php';
```

إلى:
```php
include_once PUBLIC_ROOT . '/includes/header.php';
include_once PUBLIC_ROOT . '/includes/footer.php';
```

## النتيجة
الآن يجب أن تعمل جميع صفحات لوحة تحكم المشرف بشكل صحيح دون أخطاء في تضمين الملفات.

## ملاحظات مهمة
- تأكد من أن ثابت `PUBLIC_ROOT` معرف بشكل صحيح في `app/init.php`
- في حالة ظهور أخطاء أخرى، تحقق من سجلات الأخطاء في `logs/`
- تأكد من أن جميع الملفات المطلوبة موجودة في المسارات الصحيحة 