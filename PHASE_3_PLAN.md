# خطة المرحلة الثالثة: تطوير الواجهات العامة

## نظرة عامة على المرحلة الثالثة

المرحلة الثالثة تركز على تطوير الواجهات العامة للمنصة، بما في ذلك الصفحات العامة، المكتبة التعليمية، والواجهة الأمامية. هذه المرحلة تجعل المنصة متاحة للمستخدمين النهائيين.

## الأهداف الرئيسية للمرحلة الثالثة

### 3.1 تطوير الصفحات العامة

#### 3.1.1 الصفحة الرئيسية
**الملف**: `public/index.php` (محسن)

**الميزات المطلوبة**:
- قسم ترحيبي جذاب (Hero Section)
- عرض الميزات الرئيسية
- إحصائيات المنصة
- قسم "كيف يعمل النظام"
- آراء المستخدمين
- دعوة للعمل (Call to Action)
- روابط سريعة

**التصميم**:
```html
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <h1>منصة نفسي - الاستشارات النفسية عبر الإنترنت</h1>
        <p>تواصل مع أخصائيين نفسيين معتمدين من أي مكان وفي أي وقت</p>
        <div class="hero-buttons">
            <a href="register.php" class="btn btn-primary">ابدأ الآن</a>
            <a href="specialists.php" class="btn btn-outline">تصفح الأخصائيين</a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <!-- 3-4 ميزات رئيسية -->
</section>

<!-- Statistics Section -->
<section class="stats-section">
    <!-- إحصائيات المنصة -->
</section>
```

#### 3.1.2 صفحة تسجيل الدخول
**الملف**: `public/login.php` (محسن)

**الميزات المطلوبة**:
- نموذج تسجيل دخول آمن
- خيار "تذكرني"
- إظهار/إخفاء كلمة المرور
- روابط لاستعادة كلمة المرور
- رابط لإنشاء حساب جديد
- رسائل خطأ واضحة
- حماية من محاولات تسجيل الدخول المتكررة

**التصميم**:
```html
<div class="login-container">
    <div class="login-form">
        <h2>تسجيل الدخول</h2>
        <form method="POST" action="handlers/login_handler.php">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="password-input">
                    <input type="password" id="password" name="password" required>
                    <button type="button" class="toggle-password">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="remember" value="1">
                    <span>تذكرني</span>
                </label>
            </div>
            <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
        </form>
        <div class="login-links">
            <a href="forgot-password.php">نسيت كلمة المرور؟</a>
            <a href="register.php">ليس لديك حساب؟ سجل الآن</a>
        </div>
    </div>
</div>
```

#### 3.1.3 صفحة إنشاء حساب
**الملف**: `public/register.php` (محسن)

**الميزات المطلوبة**:
- نموذج تسجيل شامل
- التحقق من صحة البيانات في الوقت الفعلي
- مؤشر قوة كلمة المرور
- التحقق من البريد الإلكتروني
- شروط الاستخدام وسياسة الخصوصية
- خيار الاشتراك في النشرة الإخبارية
- رسائل تأكيد واضحة

**الحقول المطلوبة**:
- الاسم الأول والأخير
- اسم المستخدم
- البريد الإلكتروني
- رقم الهاتف
- تاريخ الميلاد
- الجنس
- كلمة المرور وتأكيدها
- الموافقة على الشروط

#### 3.1.4 صفحة "عن المنصة"
**الملف**: `public/about.php` (محسن)

**المحتوى المطلوب**:
- رؤية ورسالة المنصة
- القيم الأساسية
- فريق العمل
- إحصائيات المنصة
- الشهادات والاعتمادات
- الجوائز والإنجازات
- الشراكات والتعاونات

#### 3.1.5 صفحة التواصل
**الملف**: `public/contact.php` (محسن)

**الميزات المطلوبة**:
- نموذج تواصل شامل
- معلومات الاتصال
- خريطة الموقع
- ساعات العمل
- الأسئلة الشائعة (FAQ)
- روابط وسائل التواصل الاجتماعي

#### 3.1.6 صفحة سياسة الخصوصية
**الملف**: `public/privacy_policy.php` (جديد)

**المحتوى المطلوب**:
- جمع البيانات الشخصية
- استخدام البيانات
- مشاركة البيانات
- حقوق المستخدم
- حماية البيانات
- ملفات تعريف الارتباط
- التحديثات والتغييرات

#### 3.1.7 صفحة شروط الاستخدام
**الملف**: `public/terms_of_service.php` (جديد)

**المحتوى المطلوب**:
- شروط الاستخدام
- حقوق وواجبات المستخدمين
- حقوق وواجبات الأخصائيين
- إلغاء الحساب
- الملكية الفكرية
- المسؤولية القانونية

### 3.2 تطوير صفحات المكتبة التعليمية

#### 3.2.1 صفحة المكتبة الرئيسية
**الملف**: `public/library.php` (محسن)

**الميزات المطلوبة**:
- عرض المقالات والفيديوهات
- نظام البحث والتصفية
- التصنيفات والعلامات
- المحتوى المميز
- نظام التقييم
- نظام المفضلة
- الاشتراك في النشرة الإخبارية

**التصميم**:
```html
<div class="library-container">
    <!-- شريط البحث والتصفية -->
    <div class="search-filter-bar">
        <div class="search-box">
            <input type="text" placeholder="ابحث في المكتبة...">
            <button type="button"><i class="fas fa-search"></i></button>
        </div>
        <div class="filter-options">
            <select name="category">
                <option value="">جميع التصنيفات</option>
                <!-- التصنيفات -->
            </select>
            <select name="type">
                <option value="">جميع الأنواع</option>
                <option value="article">مقالات</option>
                <option value="video">فيديوهات</option>
            </select>
        </div>
    </div>

    <!-- المحتوى المميز -->
    <section class="featured-content">
        <h3>المحتوى المميز</h3>
        <div class="content-grid">
            <!-- المحتوى المميز -->
        </div>
    </section>

    <!-- جميع المحتويات -->
    <section class="all-content">
        <div class="content-grid">
            <!-- جميع المحتويات -->
        </div>
    </section>

    <!-- ترقيم الصفحات -->
    <div class="pagination">
        <!-- ترقيم الصفحات -->
    </div>
</div>
```

#### 3.2.2 صفحة عرض المقال
**الملف**: `public/article.php` (جديد)

**الميزات المطلوبة**:
- عرض المقال كاملاً
- معلومات الكاتب
- تاريخ النشر
- التصنيفات والعلامات
- نظام التعليقات
- مقالات ذات صلة
- مشاركة المقال
- طباعة المقال
- إضافة للمفضلة

#### 3.2.3 صفحة عرض الفيديو
**الملف**: `public/video.php` (جديد)

**الميزات المطلوبة**:
- مشغل فيديو متقدم
- وصف الفيديو
- معلومات المقدم
- التعليقات والتقييمات
- فيديوهات ذات صلة
- تحميل الفيديو (إذا كان مسموحاً)
- مشاركة الفيديو

### 3.3 تطوير صفحات الأخصائيين

#### 3.3.1 صفحة قائمة الأخصائيين
**الملف**: `public/specialists.php` (محسن)

**الميزات المطلوبة**:
- عرض قائمة الأخصائيين
- نظام البحث والتصفية
- التخصصات والجنس
- التقييمات والسعر
- حالة التوفر
- عرض مختصر للملف الشخصي
- روابط للملف الشخصي الكامل

**التصميم**:
```html
<div class="specialists-container">
    <!-- شريط البحث والتصفية -->
    <div class="search-filter-bar">
        <div class="search-box">
            <input type="text" placeholder="ابحث عن أخصائي...">
            <button type="button"><i class="fas fa-search"></i></button>
        </div>
        <div class="filter-options">
            <select name="specialization">
                <option value="">جميع التخصصات</option>
                <!-- التخصصات -->
            </select>
            <select name="gender">
                <option value="">جميع الجنسيات</option>
                <option value="male">ذكر</option>
                <option value="female">أنثى</option>
            </select>
            <select name="rating">
                <option value="">جميع التقييمات</option>
                <option value="4">4 نجوم وأكثر</option>
                <option value="3">3 نجوم وأكثر</option>
            </select>
        </div>
    </div>

    <!-- قائمة الأخصائيين -->
    <div class="specialists-grid">
        <!-- بطاقات الأخصائيين -->
    </div>

    <!-- ترقيم الصفحات -->
    <div class="pagination">
        <!-- ترقيم الصفحات -->
    </div>
</div>
```

#### 3.3.2 صفحة ملف الأخصائي
**الملف**: `public/specialist_profile.php` (جديد)

**الميزات المطلوبة**:
- معلومات الأخصائي الكاملة
- التخصصات والخبرات
- التعليم والشهادات
- التقييمات والمراجعات
- الجدول الزمني
- أسعار الجلسات
- حجز جلسة
- التواصل مع الأخصائي

### 3.4 تطوير الواجهة الأمامية

#### 3.4.1 الأنماط الرئيسية
**الملف**: `assets/css/style.css` (محسن)

**التحسينات المطلوبة**:
- تحسين التصميم العام
- إضافة متغيرات CSS للألوان
- تحسين التجاوب
- إضافة تأثيرات بصرية
- تحسين إمكانية الوصول
- تحسين الأداء

**المتغيرات الجديدة**:
```css
:root {
    /* الألوان الأساسية */
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    
    /* الألوان المحايدة */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* الخطوط */
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}
```

#### 3.4.2 التفاعلات العامة
**الملف**: `assets/js/main.js` (محسن)

**التحسينات المطلوبة**:
- تحسين التفاعلات
- إضافة تأثيرات بصرية
- تحسين تجربة المستخدم
- إضافة رسائل تأكيد
- تحسين الأداء
- إضافة دعم للوضع المظلم

**الميزات الجديدة**:
```javascript
// دعم الوضع المظلم
const darkModeToggle = {
    init() {
        this.toggle = document.getElementById('dark-mode-toggle');
        this.body = document.body;
        this.initEventListeners();
        this.loadSavedPreference();
    },
    
    initEventListeners() {
        this.toggle.addEventListener('click', () => {
            this.toggleDarkMode();
        });
    },
    
    toggleDarkMode() {
        this.body.classList.toggle('dark-mode');
        const isDark = this.body.classList.contains('dark-mode');
        localStorage.setItem('darkMode', isDark);
    },
    
    loadSavedPreference() {
        const saved = localStorage.getItem('darkMode');
        if (saved === 'true') {
            this.body.classList.add('dark-mode');
        }
    }
};

// تحسين التفاعلات
const interactions = {
    init() {
        this.initTooltips();
        this.initModals();
        this.initFormValidation();
        this.initLazyLoading();
    },
    
    initTooltips() {
        // تهيئة التلميحات
    },
    
    initModals() {
        // تهيئة النوافذ المنبثقة
    },
    
    initFormValidation() {
        // التحقق من صحة النماذج
    },
    
    initLazyLoading() {
        // تحميل الصور عند الحاجة
    }
};
```

#### 3.4.3 تحسين تجربة المستخدم (UX)

**التحسينات المطلوبة**:
- تحسين سرعة التحميل
- تحسين إمكانية الوصول
- تحسين التجاوب
- تحسين سهولة الاستخدام
- إضافة رسائل تأكيد
- تحسين التنقل

### 3.5 تطوير المكونات المشتركة

#### 3.5.1 الهيدر المحسن
**الملف**: `public/includes/header.php` (محسن)

**التحسينات المطلوبة**:
- قائمة تنقل محسنة
- شريط البحث
- إشعارات المستخدم
- قائمة المستخدم
- دعم الوضع المظلم
- تحسين التجاوب

#### 3.5.2 الفوتر المحسن
**الملف**: `public/includes/footer.php` (محسن)

**التحسينات المطلوبة**:
- روابط سريعة محسنة
- معلومات الاتصال
- وسائل التواصل الاجتماعي
- النشرة الإخبارية
- شروط الاستخدام
- تحسين التجاوب

#### 3.5.3 مكونات إضافية
**الملفات الجديدة**:
- `public/includes/search_modal.php`
- `public/includes/notification_modal.php`
- `public/includes/user_menu.php`
- `public/includes/breadcrumb.php`

### 3.6 تطوير نظام البحث

#### 3.6.1 البحث العام
**الملف**: `public/search.php` (جديد)

**الميزات المطلوبة**:
- البحث في جميع المحتويات
- البحث في الأخصائيين
- البحث في المقالات
- البحث في الفيديوهات
- تصفية النتائج
- ترتيب النتائج

#### 3.6.2 البحث المتقدم
**الملف**: `public/advanced_search.php` (جديد)

**الميزات المطلوبة**:
- خيارات بحث متقدمة
- تصفية حسب التاريخ
- تصفية حسب التصنيف
- تصفية حسب التقييم
- حفظ عمليات البحث

### 3.7 تطوير نظام التقييمات والمراجعات

#### 3.7.1 نظام تقييم الأخصائيين
**الملف**: `public/rate_specialist.php` (جديد)

**الميزات المطلوبة**:
- نموذج تقييم شامل
- تقييم من 1 إلى 5 نجوم
- تعليق مكتوب
- تقييم جوانب محددة
- إمكانية تعديل التقييم

#### 3.7.2 عرض التقييمات
**الملف**: `public/specialist_reviews.php` (جديد)

**الميزات المطلوبة**:
- عرض جميع التقييمات
- تصفية التقييمات
- ترتيب التقييمات
- إحصائيات التقييمات
- رد الأخصائي على التقييمات

## الجدول الزمني للمرحلة الثالثة

### الأسبوع الأول (أيام 1-7)
- تطوير الصفحات العامة الأساسية
- تحسين الصفحة الرئيسية
- تطوير صفحات تسجيل الدخول وإنشاء الحساب
- تطوير صفحة "عن المنصة" والتواصل

### الأسبوع الثاني (أيام 8-14)
- تطوير المكتبة التعليمية
- تطوير صفحات الأخصائيين
- تطوير نظام البحث
- تطوير نظام التقييمات

### الأسبوع الثالث (أيام 15-21)
- تحسين الواجهة الأمامية
- تطوير المكونات المشتركة
- تحسين تجربة المستخدم
- اختبار وتطوير النظام

## الملفات المطلوبة إنشاؤها

### الصفحات العامة
```
public/
├── index.php (محسن)
├── login.php (محسن)
├── register.php (محسن)
├── about.php (محسن)
├── contact.php (محسن)
├── privacy_policy.php (جديد)
├── terms_of_service.php (جديد)
├── search.php (جديد)
└── advanced_search.php (جديد)
```

### صفحات المكتبة
```
public/
├── library.php (محسن)
├── article.php (جديد)
├── video.php (جديد)
└── category.php (جديد)
```

### صفحات الأخصائيين
```
public/
├── specialists.php (محسن)
├── specialist_profile.php (جديد)
├── rate_specialist.php (جديد)
└── specialist_reviews.php (جديد)
```

### المكونات المشتركة
```
public/includes/
├── header.php (محسن)
├── footer.php (محسن)
├── search_modal.php (جديد)
├── notification_modal.php (جديد)
├── user_menu.php (جديد)
└── breadcrumb.php (جديد)
```

### ملفات الواجهة الأمامية
```
assets/
├── css/style.css (محسن)
├── css/components.css (جديد)
├── css/responsive.css (جديد)
├── js/main.js (محسن)
├── js/search.js (جديد)
├── js/rating.js (جديد)
└── js/dark-mode.js (جديد)
```

## معايير الجودة

### معايير التصميم
- تصميم متجاوب بالكامل
- إمكانية وصول عالية
- سرعة تحميل عالية
- سهولة الاستخدام
- جاذبية بصرية

### معايير الكود
- كود نظيف ومنظم
- تعليقات واضحة
- اتباع معايير الترميز
- تحسين الأداء
- قابلية الصيانة

### معايير المحتوى
- محتوى مفيد وجذاب
- دقة المعلومات
- تحديث المحتوى بانتظام
- تنوع المحتوى
- جودة عالية

## الاختبارات المطلوبة

### اختبارات الواجهة
- اختبار التجاوب
- اختبار إمكانية الوصول
- اختبار سهولة الاستخدام
- اختبار سرعة التحميل
- اختبار التوافق مع المتصفحات

### اختبارات الوظائف
- اختبار النماذج
- اختبار البحث
- اختبار التقييمات
- اختبار التنقل
- اختبار التفاعلات

### اختبارات المحتوى
- اختبار دقة المحتوى
- اختبار جودة المحتوى
- اختبار تحديث المحتوى
- اختبار تنوع المحتوى

## النتائج المتوقعة

### في نهاية المرحلة الثالثة
- ✅ واجهات عامة متطورة وجذابة
- ✅ مكتبة تعليمية شاملة
- ✅ صفحات أخصائيين متكاملة
- ✅ نظام بحث متقدم
- ✅ نظام تقييمات شامل
- ✅ واجهة مستخدم محسنة
- ✅ تجربة مستخدم ممتازة
- ✅ تصميم متجاوب بالكامل

### الاستعداد للمرحلة الرابعة
- الواجهات العامة جاهزة
- النظام جاهز للمستخدمين
- يمكن البدء في لوحات التحكم
- النظام جاهز للتوسع

## المخاطر والتحديات

### المخاطر التقنية
- تحديات التصميم المتجاوب
- مشاكل الأداء
- تحديات إمكانية الوصول
- مشاكل التوافق

### المخاطر التشغيلية
- تأخير في الجدول الزمني
- تغيير في المتطلبات
- مشاكل في المحتوى
- مشاكل في التصميم

## خطة الطوارئ

### في حالة التأخير
- إعطاء الأولوية للصفحات الأساسية
- تأجيل الميزات الثانوية
- استخدام قوالب جاهزة
- تبسيط التصميم

### في حالة المشاكل التقنية
- البحث عن حلول بديلة
- استخدام مكتبات جاهزة
- تبسيط الميزات
- الاستعانة بمصممين

## الخلاصة

المرحلة الثالثة هي مرحلة حاسمة في تطوير منصة نفسي. تركز على جعل المنصة متاحة ومفيدة للمستخدمين النهائيين من خلال تطوير واجهات جذابة ومتطورة. النجاح في هذه المرحلة سيضمن نجاح المشروع بالكامل.

---

**تم إنشاء هذا الملف بواسطة فريق منصة نفسي**  
**التاريخ**: يوليو 2024  
**الإصدار**: 1.0.0 