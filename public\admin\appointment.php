<?php
/**
 * عرض تفاصيل موعد محدد - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/appointment/' . ($appointmentId ?? ''));
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على معرف الموعد من URL
$appointmentId = isset($params['id']) ? (int)$params['id'] : 0;

if (!$appointmentId) {
    setAlert('معرف الموعد غير صحيح', 'danger');
    header('Location: /nafsi_platform/admin/appointments');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// معالجة تحديث حالة الموعد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = db();
        
        switch ($_POST['action']) {
            case 'update_status':
                $newStatus = $_POST['status'];
                $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
                
                if (!in_array($newStatus, $validStatuses)) {
                    throw new Exception('حالة الموعد غير صحيحة');
                }
                
                $result = $db->update("
                    UPDATE appointments 
                    SET status = ?, updated_at = ? 
                    WHERE id = ?
                ", [$newStatus, date('Y-m-d H:i:s'), $appointmentId]);
                
                if ($result) {
                    setAlert('تم تحديث حالة الموعد بنجاح', 'success');
                } else {
                    throw new Exception('فشل في تحديث حالة الموعد');
                }
                break;
                
            case 'update_notes':
                $notes = trim($_POST['notes'] ?? '');
                
                $result = $db->update("
                    UPDATE appointments 
                    SET notes = ?, updated_at = ? 
                    WHERE id = ?
                ", [$notes, date('Y-m-d H:i:s'), $appointmentId]);
                
                if ($result) {
                    setAlert('تم تحديث ملاحظات الموعد بنجاح', 'success');
                } else {
                    throw new Exception('فشل في تحديث ملاحظات الموعد');
                }
                break;
                
            case 'delete':
                $result = $db->delete("DELETE FROM appointments WHERE id = ?", [$appointmentId]);
                
                if ($result) {
                    setAlert('تم حذف الموعد بنجاح', 'success');
                    header('Location: /nafsi_platform/admin/appointments');
                    exit;
                } else {
                    throw new Exception('فشل في حذف الموعد');
                }
                break;
                
            default:
                throw new Exception('إجراء غير معروف');
        }
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// الحصول على تفاصيل الموعد
try {
    $db = db();
    $appointment = $db->selectOne("
        SELECT a.*, 
               c.first_name as client_first_name, c.last_name as client_last_name, 
               c.email as client_email, c.phone as client_phone,
               t.first_name as therapist_first_name, t.last_name as therapist_last_name,
               t.email as therapist_email, t.phone as therapist_phone
        FROM appointments a
        JOIN users c ON a.client_id = c.id
        JOIN users t ON a.therapist_id = t.id
        WHERE a.id = ?
    ", [$appointmentId]);
    
    if (!$appointment) {
        setAlert('الموعد غير موجود', 'danger');
        header('Location: /nafsi_platform/admin/appointments');
        exit;
    }
    
} catch (Exception $e) {
    setAlert('حدث خطأ في تحميل تفاصيل الموعد: ' . $e->getMessage(), 'danger');
    header('Location: /nafsi_platform/admin/appointments');
    exit;
}

// تعيين متغيرات الصفحة
$pageTitle = 'تفاصيل الموعد #' . $appointmentId;
$currentPage = 'admin_appointment';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    .info-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    .status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
    }
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-confirmed { background-color: #d1ecf1; color: #0c5460; }
    .status-completed { background-color: #d4edda; color: #155724; }
    .status-cancelled { background-color: #f8d7da; color: #721c24; }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-calendar-check me-2"></i>
                                تفاصيل الموعد #<?= $appointmentId ?>
                            </h2>
                            <p class="mb-0">عرض وتعديل تفاصيل الموعد</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/appointments') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة لقائمة المواعيد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الموعد -->
    <div class="row">
        <!-- معلومات الموعد -->
        <div class="col-lg-8">
            <div class="card info-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        معلومات الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">التاريخ:</label>
                            <p class="form-control-plaintext"><?= date('Y/m/d', strtotime($appointment['appointment_date'])) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الوقت:</label>
                            <p class="form-control-plaintext"><?= $appointment['appointment_time'] ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الحالة:</label>
                            <span class="status-badge status-<?= $appointment['status'] ?>">
                                <?php
                                $statusLabels = [
                                    'pending' => 'في الانتظار',
                                    'confirmed' => 'مؤكد',
                                    'completed' => 'مكتمل',
                                    'cancelled' => 'ملغي'
                                ];
                                echo $statusLabels[$appointment['status']] ?? $appointment['status'];
                                ?>
                            </span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                            <p class="form-control-plaintext"><?= date('Y/m/d H:i', strtotime($appointment['created_at'])) ?></p>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">الملاحظات:</label>
                            <p class="form-control-plaintext"><?= $appointment['notes'] ?: 'لا توجد ملاحظات' ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات العميل والمعالج -->
        <div class="col-lg-4">
            <!-- معلومات العميل -->
            <div class="card info-card mb-4">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h6>
                </div>
                <div class="card-body">
                    <h6><?= htmlspecialchars($appointment['client_first_name'] . ' ' . $appointment['client_last_name']) ?></h6>
                    <p class="text-muted mb-1">
                        <i class="fas fa-envelope me-1"></i>
                        <?= htmlspecialchars($appointment['client_email']) ?>
                    </p>
                    <?php if ($appointment['client_phone']): ?>
                    <p class="text-muted mb-0">
                        <i class="fas fa-phone me-1"></i>
                        <?= htmlspecialchars($appointment['client_phone']) ?>
                    </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- معلومات المعالج -->
            <div class="card info-card mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-user-md me-2"></i>
                        معلومات المعالج
                    </h6>
                </div>
                <div class="card-body">
                    <h6><?= htmlspecialchars($appointment['therapist_first_name'] . ' ' . $appointment['therapist_last_name']) ?></h6>
                    <p class="text-muted mb-1">
                        <i class="fas fa-envelope me-1"></i>
                        <?= htmlspecialchars($appointment['therapist_email']) ?>
                    </p>
                    <?php if ($appointment['therapist_phone']): ?>
                    <p class="text-muted mb-0">
                        <i class="fas fa-phone me-1"></i>
                        <?= htmlspecialchars($appointment['therapist_phone']) ?>
                    </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="row">
        <div class="col-12">
            <div class="card info-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        إجراءات الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- تحديث الحالة -->
                        <div class="col-md-6 mb-3">
                            <h6>تحديث حالة الموعد</h6>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="update_status">
                                <div class="input-group">
                                    <select name="status" class="form-select">
                                        <option value="pending" <?= $appointment['status'] === 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                        <option value="confirmed" <?= $appointment['status'] === 'confirmed' ? 'selected' : '' ?>>مؤكد</option>
                                        <option value="completed" <?= $appointment['status'] === 'completed' ? 'selected' : '' ?>>مكتمل</option>
                                        <option value="cancelled" <?= $appointment['status'] === 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> تحديث
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- تحديث الملاحظات -->
                        <div class="col-md-6 mb-3">
                            <h6>تحديث الملاحظات</h6>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="update_notes">
                                <div class="input-group">
                                    <textarea name="notes" class="form-control" rows="2" placeholder="أضف ملاحظات جديدة..."><?= htmlspecialchars($appointment['notes']) ?></textarea>
                                    <button type="submit" class="btn btn-info">
                                        <i class="fas fa-edit me-1"></i> تحديث
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <hr>

                    <!-- أزرار إضافية -->
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="<?= url('admin/appointments') ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                        </a>
                        
                        <a href="<?= url('admin/add_appointment') ?>" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i> إضافة موعد جديد
                        </a>
                        
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-1"></i> حذف الموعد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا الموعد؟</p>
                <p class="text-muted">
                    <strong>العميل:</strong> <?= htmlspecialchars($appointment['client_first_name'] . ' ' . $appointment['client_last_name']) ?><br>
                    <strong>التاريخ:</strong> <?= date('Y/m/d', strtotime($appointment['appointment_date'])) ?> في <?= $appointment['appointment_time'] ?>
                </p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء!</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="delete">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> تأكيد الحذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث التاريخ والوقت في الصفحة
    const appointmentDate = new Date('<?= $appointment['appointment_date'] ?>');
    const appointmentTime = '<?= $appointment['appointment_time'] ?>';
    
    // إضافة تأثيرات بصرية للحقول
    const selects = document.querySelectorAll('.form-select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
    // تأكيد الحذف
    const deleteForm = document.querySelector('form[action="delete"]');
    if (deleteForm) {
        deleteForm.addEventListener('submit', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
                e.preventDefault();
            }
        });
    }
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 