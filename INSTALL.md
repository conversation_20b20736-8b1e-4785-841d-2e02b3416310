# دليل التثبيت - منصة نفسي

## نظرة عامة

هذا الدليل يوضح كيفية تثبيت وإعداد منصة نفسي على نظامك المحلي أو على الخادم.

## المتطلبات الأساسية

### متطلبات الخادم
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache**: 2.4+ مع mod_rewrite مفعل
- **مساحة التخزين**: 1GB على الأقل
- **الذاكرة**: 512MB على الأقل

### متطلبات PHP
- `ext-mysqli` أو `ext-pdo_mysql`
- `ext-json`
- `ext-mbstring`
- `ext-openssl`
- `ext-curl` (اختياري للبريد الإلكتروني)

## خطوات التثبيت

### الخطوة 1: تحضير البيئة

#### باستخدام XAMPP (Windows)
1. قم بتحميل وتثبيت [XAMPP](https://www.apachefriends.org/)
2. ابدأ Apache و MySQL من لوحة التحكم
3. تأكد من تفعيل mod_rewrite في Apache

#### باستخدام WAMP (Windows)
1. قم بتحميل وتثبيت [WAMP](https://www.wampserver.com/)
2. ابدأ الخدمات
3. تأكد من تفعيل mod_rewrite

#### باستخدام MAMP (macOS)
1. قم بتحميل وتثبيت [MAMP](https://www.mamp.info/)
2. ابدأ الخدمات
3. تأكد من تفعيل mod_rewrite

### الخطوة 2: تحميل المشروع

```bash
# باستخدام Git
git clone https://github.com/your-username/nafsi_platform.git
cd nafsi_platform

# أو قم بتحميل الملفات مباشرة
# واستخرجها في مجلد htdocs
```

### الخطوة 3: إعداد قاعدة البيانات

#### إنشاء قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE nafsi_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم (اختياري)
CREATE USER 'nafsi_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON nafsi_platform.* TO 'nafsi_user'@'localhost';
FLUSH PRIVILEGES;
```

#### استيراد المخطط الأساسي
```bash
# باستخدام سطر الأوامر
mysql -u root -p nafsi_platform < database/create_database.sql

# أو باستخدام phpMyAdmin
# 1. افتح phpMyAdmin
# 2. اختر قاعدة البيانات nafsi_platform
# 3. اذهب إلى تبويب Import
# 4. اختر ملف database/create_database.sql
# 5. اضغط Go
```

### الخطوة 4: تكوين التطبيق

#### تعديل ملف الإعدادات
افتح ملف `app/config.php` وعدّل الإعدادات التالية:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'nafsi_platform');
define('DB_USER', 'root'); // أو 'nafsi_user' إذا أنشأت مستخدم
define('DB_PASS', ''); // كلمة المرور إذا وجدت

// رابط التطبيق
define('APP_URL', 'http://localhost/nafsi_platform');

// إعدادات البريد الإلكتروني (اختياري)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'your-app-password');
```

#### إنشاء المجلدات المطلوبة
```bash
# إنشاء مجلدات الملفات المرفوعة والسجلات
mkdir uploads
mkdir logs
chmod 755 uploads logs
```

### الخطوة 5: إعداد Apache

#### تفعيل mod_rewrite
تأكد من تفعيل mod_rewrite في ملف `httpd.conf`:

```apache
# في ملف httpd.conf
LoadModule rewrite_module modules/mod_rewrite.so
```

#### إعدادات المجلد
```apache
<Directory "C:/xampp/htdocs">
    AllowOverride All
    Require all granted
</Directory>
```

### الخطوة 6: اختبار التثبيت

#### اختبار النظام
افتح المتصفح واذهب إلى:
```
http://localhost/nafsi_platform/test.php
```

يجب أن ترى صفحة اختبار تظهر:
- ✓ الاتصال بقاعدة البيانات ناجح
- ✓ جميع الملفات موجودة
- ✓ الروابط تعمل بشكل صحيح

#### اختبار الروابط الرئيسية
- الصفحة الرئيسية: `http://localhost/nafsi_platform/`
- تسجيل الدخول: `http://localhost/nafsi_platform/login`
- إنشاء حساب: `http://localhost/nafsi_platform/register`

## إعدادات إضافية

### إعدادات الأمان

#### تشفير كلمات المرور
```php
// في app/config.php
define('PASSWORD_COST', 12); // تكلفة تشفير كلمة المرور
```

#### إعدادات الجلسة
```php
// في app/config.php
define('SESSION_LIFETIME', 3600); // مدة الجلسة بالساعات
```

### إعدادات البريد الإلكتروني

#### Gmail SMTP
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'your-app-password');
```

#### Outlook SMTP
```php
define('SMTP_HOST', 'smtp-mail.outlook.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'your-password');
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ "404 Not Found"
**السبب**: mod_rewrite غير مفعل أو ملف .htaccess غير موجود
**الحل**:
```apache
# تأكد من تفعيل mod_rewrite
LoadModule rewrite_module modules/mod_rewrite.so

# تأكد من AllowOverride All
<Directory "C:/xampp/htdocs">
    AllowOverride All
</Directory>
```

#### 2. خطأ في الاتصال بقاعدة البيانات
**السبب**: إعدادات قاعدة البيانات غير صحيحة
**الحل**:
- تحقق من اسم قاعدة البيانات
- تحقق من اسم المستخدم وكلمة المرور
- تأكد من تشغيل MySQL

#### 3. الروابط لا تعمل
**السبب**: مشكلة في ملف .htaccess
**الحل**:
- تحقق من وجود ملف .htaccess في المجلد الجذر
- تأكد من صحة قواعد إعادة الكتابة

#### 4. الملفات الثابتة لا تتحمل
**السبب**: مشكلة في إعدادات Apache
**الحل**:
```apache
# في ملف .htaccess
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>
```

### سجلات الأخطاء

#### Apache Error Log
```
# Windows (XAMPP)
C:\xampp\apache\logs\error.log

# Linux
/var/log/apache2/error.log
```

#### PHP Error Log
```php
// في app/config.php
define('LOG_ERRORS', true);
define('LOG_PATH', __DIR__ . '/../logs/');
```

## التحديث

### تحديث النظام
```bash
# باستخدام Git
git pull origin main

# أو قم بتحميل الملفات الجديدة يدوياً
```

### تحديث قاعدة البيانات
```sql
-- قم بتشغيل ملفات التحديث في مجلد database/migrations/
```

## الدعم

إذا واجهت أي مشاكل:

1. **راجع هذا الدليل** مرة أخرى
2. **تحقق من سجلات الأخطاء**
3. **راجع ملف README.md**
4. **أنشئ Issue في GitHub**

## معلومات إضافية

- **التوثيق الكامل**: راجع ملف `README.md`
- **مخطط قاعدة البيانات**: راجع ملف `DATABASE_SCHEMA.md`
- **خطة التطوير**: راجع ملف `PLAN.md`
- **سجل التحديثات**: راجع ملف `CHANGELOG.md`

---

**تم إنشاء هذا الدليل بواسطة فريق منصة نفسي**  
**آخر تحديث**: يوليو 2024  
**الإصدار**: 1.0.0 