<?php
/**
 * Web-accessible script to test and create system_settings table
 */

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'nafsi_platform';

echo "<h2>System Settings Table Test</h2>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>✅ Connected to database successfully</p>";
    
    // Check if system_settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: blue;'>✅ system_settings table already exists</p>";
        
        // Check if it has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_settings");
        $count = $stmt->fetch()['count'];
        echo "<p>Table has $count settings</p>";
        
        if ($count == 0) {
            echo "<p style='color: orange;'>⚠️ Table is empty, inserting default settings...</p>";
            
            // Insert default settings
            $defaultSettings = [
                ['site_name', 'منصة نفسي', 'اسم الموقع'],
                ['site_description', 'منصة متخصصة في الاستشارات النفسية عبر الإنترنت', 'وصف الموقع'],
                ['maintenance_mode', 'false', 'وضع الصيانة'],
                ['registration_enabled', 'true', 'تفعيل التسجيل'],
                ['email_verification_required', 'true', 'التحقق من البريد الإلكتروني مطلوب'],
                ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
                ['session_timeout', '3600', 'مهلة الجلسة بالثواني'],
                ['default_currency', 'SAR', 'العملة الافتراضية'],
                ['min_session_duration', '30', 'الحد الأدنى لمدة الجلسة بالدقائق'],
                ['max_session_duration', '120', 'الحد الأقصى لمدة الجلسة بالدقائق']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            
            echo "<p style='color: green;'>✅ Default settings inserted successfully</p>";
        } else {
            // Show existing settings
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
            $settings = $stmt->fetchAll();
            
            echo "<h3>Existing Settings:</h3>";
            echo "<ul>";
            foreach ($settings as $setting) {
                echo "<li><strong>{$setting['setting_key']}:</strong> {$setting['setting_value']}</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Creating system_settings table...</p>";
        
        // Create the table
        $createTableSQL = "
        CREATE TABLE system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($createTableSQL);
        echo "<p style='color: green;'>✅ Table created successfully</p>";
        
        // Insert default settings
        echo "<p style='color: orange;'>⚠️ Inserting default settings...</p>";
        
        $defaultSettings = [
            ['site_name', 'منصة نفسي', 'اسم الموقع'],
            ['site_description', 'منصة متخصصة في الاستشارات النفسية عبر الإنترنت', 'وصف الموقع'],
            ['maintenance_mode', 'false', 'وضع الصيانة'],
            ['registration_enabled', 'true', 'تفعيل التسجيل'],
            ['email_verification_required', 'true', 'التحقق من البريد الإلكتروني مطلوب'],
            ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
            ['session_timeout', '3600', 'مهلة الجلسة بالثواني'],
            ['default_currency', 'SAR', 'العملة الافتراضية'],
            ['min_session_duration', '30', 'الحد الأدنى لمدة الجلسة بالدقائق'],
            ['max_session_duration', '120', 'الحد الأقصى لمدة الجلسة بالدقائق']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
        echo "<p style='color: green;'>✅ Default settings inserted successfully</p>";
    }
    
    echo "<p style='color: green;'>✅ Done!</p>";
    echo "<p><a href='/nafsi_platform/admin/settings'>Go to Admin Settings</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
} 