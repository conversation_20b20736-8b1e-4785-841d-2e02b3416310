<?php
/**
 * إضافة بيانات تجريبية للعملاء والمعالجين
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/../app/init.php';

try {
    $db = db();
    
    echo "بدء إضافة البيانات التجريبية...\n";
    
    // التحقق من وجود معالجين
    $therapists = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'");
    $therapistCount = $therapists[0]['count'] ?? 0;
    
    if ($therapistCount == 0) {
        echo "إضافة المعالجين...\n";
        
        // إضافة 10 معالجين
        $therapistData = [
            ['dr.ahmed', '<EMAIL>', 'أحمد', 'محمد', '+966501234567', '1985-03-15', 'male'],
            ['dr.sara', '<EMAIL>', 'سارة', 'أحمد', '+966502345678', '1988-07-22', 'female'],
            ['dr.khalid', '<EMAIL>', 'خالد', 'علي', '+966503456789', '1982-11-08', 'male'],
            ['dr.fatima', '<EMAIL>', 'فاطمة', 'حسن', '+966504567890', '1990-05-12', 'female'],
            ['dr.omar', '<EMAIL>', 'عمر', 'عبدالله', '+966505678901', '1987-09-30', 'male'],
            ['dr.layla', '<EMAIL>', 'ليلى', 'محمد', '+966506789012', '1986-12-18', 'female'],
            ['dr.youssef', '<EMAIL>', 'يوسف', 'أحمد', '+966507890123', '1984-02-25', 'male'],
            ['dr.nour', '<EMAIL>', 'نور', 'علي', '+966508901234', '1989-08-14', 'female'],
            ['dr.hassan', '<EMAIL>', 'حسن', 'محمد', '+966509012345', '1983-06-20', 'male'],
            ['dr.zeina', '<EMAIL>', 'زينة', 'أحمد', '+966500123456', '1991-01-10', 'female']
        ];
        
        foreach ($therapistData as $therapist) {
            $data = [
                'username' => $therapist[0],
                'email' => $therapist[1],
                'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
                'first_name' => $therapist[2],
                'last_name' => $therapist[3],
                'phone' => $therapist[4],
                'date_of_birth' => $therapist[5],
                'gender' => $therapist[6],
                'user_type' => 'therapist',
                'is_active' => 1,
                'is_verified' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->insert("users", $data);
            echo "تم إضافة المعالج: {$therapist[2]} {$therapist[3]}\n";
        }
    } else {
        echo "المعالجين موجودون بالفعل ($therapistCount معالج)\n";
    }
    
    // التحقق من وجود عملاء
    $clients = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'user'");
    $clientCount = $clients[0]['count'] ?? 0;
    
    if ($clientCount == 0) {
        echo "إضافة العملاء...\n";
        
        // إضافة 10 عملاء
        $clientData = [
            ['mariam', '<EMAIL>', 'مريم', 'علي', '+966501111111', '1995-04-15', 'female'],
            ['ali', '<EMAIL>', 'علي', 'محمد', '+966502222222', '1992-08-20', 'male'],
            ['nada', '<EMAIL>', 'ندى', 'أحمد', '+966503333333', '1998-12-10', 'female'],
            ['mohammed', '<EMAIL>', 'محمد', 'عبدالله', '+966504444444', '1990-03-25', 'male'],
            ['reem', '<EMAIL>', 'ريم', 'حسن', '+966505555555', '1996-07-18', 'female'],
            ['ahmed_user', '<EMAIL>', 'أحمد', 'علي', '+966506666666', '1993-11-05', 'male'],
            ['sara_user', '<EMAIL>', 'سارة', 'محمد', '+966507777777', '1997-09-30', 'female'],
            ['omar_user', '<EMAIL>', 'عمر', 'أحمد', '+966508888888', '1991-02-14', 'male'],
            ['layla_user', '<EMAIL>', 'ليلى', 'علي', '+966509999999', '1994-06-22', 'female'],
            ['khalid_user', '<EMAIL>', 'خالد', 'محمد', '+966500000000', '1989-12-08', 'male']
        ];
        
        foreach ($clientData as $client) {
            $data = [
                'username' => $client[0],
                'email' => $client[1],
                'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
                'first_name' => $client[2],
                'last_name' => $client[3],
                'phone' => $client[4],
                'date_of_birth' => $client[5],
                'gender' => $client[6],
                'user_type' => 'user',
                'is_active' => 1,
                'is_verified' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $db->insert("users", $data);
            echo "تم إضافة العميل: {$client[2]} {$client[3]}\n";
        }
    } else {
        echo "العملاء موجودون بالفعل ($clientCount عميل)\n";
    }
    
    echo "\nتم إضافة البيانات التجريبية بنجاح!\n";
    echo "يمكنك الآن الوصول إلى صفحة إضافة المواعيد وستجد العملاء والمعالجين متاحين.\n";
    
} catch (Exception $e) {
    echo "حدث خطأ: " . $e->getMessage() . "\n";
}
?> 