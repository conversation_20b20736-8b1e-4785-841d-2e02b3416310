<?php
/**
 * اختبار النظام بعد إزالة الملفات المتداخلة
 */

// تعريف الثوابت بشكل آمن
if (!defined('NAFSI_APP')) {
    define('NAFSI_APP', true);
}
if (!defined('APP_ROOT')) {
    define('APP_ROOT', __DIR__ . '/app');
}
if (!defined('PUBLIC_ROOT')) {
    define('PUBLIC_ROOT', __DIR__ . '/public');
}

// تضمين ملف التهيئة
require_once APP_ROOT . '/init.php';

// تعيين متغيرات الصفحة
$pageTitle = 'اختبار النظام';
$currentPage = 'test_system';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">
                <i class="fas fa-cogs text-primary me-2"></i>
                اختبار النظام
            </h1>
            
            <div class="row">
                <!-- اختبار الثوابت -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-check-circle me-2"></i>الثوابت</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $constants = [
                                'NAFSI_APP' => 'ثابت التطبيق',
                                'APP_ROOT' => 'مجلد التطبيق',
                                'PUBLIC_ROOT' => 'المجلد العام',
                                'DB_HOST' => 'خادم قاعدة البيانات',
                                'DB_NAME' => 'اسم قاعدة البيانات'
                            ];
                            
                            foreach ($constants as $const => $desc) {
                                if (defined($const)) {
                                    echo "<div class='text-success'><i class='fas fa-check'></i> $desc: " . constant($const) . "</div>";
                                } else {
                                    echo "<div class='text-danger'><i class='fas fa-times'></i> $desc: غير معرف</div>";
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار الدوال -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-code me-2"></i>الدوال</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $functions = [
                                'url' => 'دالة الروابط',
                                'loadPage' => 'دالة تحميل الصفحات',
                                'sanitizeInput' => 'دالة تنظيف المدخلات',
                                'generateCSRFToken' => 'دالة رمز الأمان',
                                'verifyCSRFToken' => 'دالة التحقق من الأمان',
                                'setAlert' => 'دالة الرسائل',
                                'getAlert' => 'دالة قراءة الرسائل'
                            ];
                            
                            foreach ($functions as $func => $desc) {
                                if (function_exists($func)) {
                                    echo "<div class='text-success'><i class='fas fa-check'></i> $desc</div>";
                                } else {
                                    echo "<div class='text-danger'><i class='fas fa-times'></i> $desc</div>";
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار قاعدة البيانات -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-database me-2"></i>قاعدة البيانات</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = db();
                                echo "<div class='text-success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات ناجح</div>";
                                
                                // اختبار الجداول
                                $tables = ['users', 'appointments', 'content', 'settings'];
                                foreach ($tables as $table) {
                                    try {
                                        $result = $db->select("SHOW TABLES LIKE ?", [$table]);
                                        if (!empty($result)) {
                                            echo "<div class='text-success'><i class='fas fa-check'></i> جدول $table موجود</div>";
                                        } else {
                                            echo "<div class='text-warning'><i class='fas fa-exclamation-triangle'></i> جدول $table غير موجود</div>";
                                        }
                                    } catch (Exception $e) {
                                        echo "<div class='text-danger'><i class='fas fa-times'></i> خطأ في جدول $table: " . $e->getMessage() . "</div>";
                                    }
                                }
                            } catch (Exception $e) {
                                echo "<div class='text-danger'><i class='fas fa-times'></i> خطأ في الاتصال: " . $e->getMessage() . "</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار الملفات -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h5><i class="fas fa-file me-2"></i>الملفات</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $files = [
                                'app/init.php' => 'ملف التهيئة',
                                'app/config/config.php' => 'ملف الإعدادات',
                                'app/core/Auth.php' => 'كلاس المصادقة',
                                'app/core/User.php' => 'كلاس المستخدم',
                                'public/includes/header.php' => 'رأس الصفحة',
                                'public/includes/footer.php' => 'تذييل الصفحة'
                            ];
                            
                            foreach ($files as $file => $desc) {
                                if (file_exists($file)) {
                                    echo "<div class='text-success'><i class='fas fa-check'></i> $desc</div>";
                                } else {
                                    echo "<div class='text-danger'><i class='fas fa-times'></i> $desc</div>";
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- اختبار الصلاحيات -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5><i class="fas fa-shield-alt me-2"></i>الصلاحيات</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $directories = [
                                'uploads' => 'مجلد الرفع',
                                'logs' => 'مجلد السجلات',
                                'public/assets' => 'مجلد الأصول'
                            ];
                            
                            foreach ($directories as $dir => $desc) {
                                if (is_dir($dir) && is_writable($dir)) {
                                    echo "<div class='text-success'><i class='fas fa-check'></i> $desc قابل للكتابة</div>";
                                } elseif (is_dir($dir)) {
                                    echo "<div class='text-warning'><i class='fas fa-exclamation-triangle'></i> $desc غير قابل للكتابة</div>";
                                } else {
                                    echo "<div class='text-danger'><i class='fas fa-times'></i> $desc غير موجود</div>";
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                        </div>
                        <div class="card-body">
                            <div><strong>إصدار PHP:</strong> <?= PHP_VERSION ?></div>
                            <div><strong>إصدار MySQL:</strong> <?= function_exists('mysqli_get_server_info') ? mysqli_get_server_info(db()->getConnection()) : 'غير متوفر' ?></div>
                            <div><strong>الذاكرة المستخدمة:</strong> <?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB</div>
                            <div><strong>الحد الأقصى للذاكرة:</strong> <?= ini_get('memory_limit') ?></div>
                            <div><strong>وقت الخادم:</strong> <?= date('Y-m-d H:i:s') ?></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- زر إعادة الاختبار -->
            <div class="text-center mt-4">
                <a href="test_system.php" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>
                    إعادة الاختبار
                </a>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?>
