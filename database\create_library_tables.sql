-- إنشاء جداول المكتبة
-- تشغيل هذا الملف في phpMyAdmin أو أي أداة إدارة قاعدة البيانات

-- جدول المكتبة
CREATE TABLE IF NOT EXISTS library (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    author <PERSON><PERSON><PERSON><PERSON>(100),
    publisher VARCHAR(100),
    isbn VARCHAR(20),
    publication_year INT,
    language VARCHAR(50) DEFAULT 'Arabic',
    category VARCHAR(100),
    tags JSON,
    file_type ENUM('pdf', 'doc', 'docx', 'epub', 'video', 'audio', 'link') NOT NULL,
    file_url VARCHAR(500),
    file_size INT,
    thumbnail VARCHAR(255),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT TRUE,
    price DECIMAL(10,2) DEFAULT 0.00,
    created_by INT,
    approved_by INT,
    approved_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول تقييمات المكتبة
CREATE TABLE IF NOT EXISTS library_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_library_rating (user_id, library_id)
);

-- جدول تحميلات المكتبة
CREATE TABLE IF NOT EXISTS library_downloads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT NOT NULL,
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول مشاهدات المكتبة
CREATE TABLE IF NOT EXISTS library_views (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء فهارس للمكتبة
CREATE INDEX idx_library_status ON library(status);
CREATE INDEX idx_library_category ON library(category);
CREATE INDEX idx_library_created_at ON library(created_at);
CREATE INDEX idx_library_is_featured ON library(is_featured);
CREATE FULLTEXT INDEX idx_library_search ON library(title, description, author, publisher);

-- إضافة بيانات تجريبية
INSERT INTO library (title, description, author, publisher, category, file_type, file_url, is_free, status, is_featured, tags, created_by, approved_by, approved_at) VALUES
('أساسيات علم النفس', 'كتاب شامل يغطي أساسيات علم النفس الحديث وأهم النظريات والتطبيقات العملية', 'د. أحمد محمد', 'دار النشر العلمية', 'علم النفس', 'pdf', 'https://example.com/psychology-basics.pdf', 1, 'active', 1, '["علم النفس", "أساسيات", "نظريات"]', 1, 1, NOW()),
('العلاج المعرفي السلوكي', 'دليل عملي للعلاج المعرفي السلوكي مع أمثلة وتطبيقات عملية', 'د. فاطمة علي', 'مركز الدراسات النفسية', 'العلاج النفسي', 'pdf', 'https://example.com/cbt-guide.pdf', 0, 'active', 1, '["علاج", "معرفي", "سلوكي", "CBT"]', 1, 1, NOW()),
('تنمية الذات والثقة', 'برنامج متكامل لتنمية الذات وبناء الثقة بالنفس', 'د. خالد عبدالله', 'مؤسسة التنمية البشرية', 'التنمية البشرية', 'video', 'https://example.com/self-development.mp4', 1, 'active', 0, '["تنمية", "ذات", "ثقة", "برنامج"]', 1, 1, NOW()),
('العلاقات الزوجية الصحية', 'دليل شامل لبناء علاقات زوجية صحية ومستقرة', 'د. سارة أحمد', 'مركز الاستشارات الأسرية', 'العلاقات', 'audio', 'https://example.com/marriage-relationships.mp3', 0, 'active', 0, '["علاقات", "زوجية", "أسرة", "صحة"]', 1, 1, NOW()),
('تربية الأطفال في العصر الحديث', 'أساليب حديثة في تربية الأطفال مع التركيز على التطور النفسي والاجتماعي', 'د. محمد حسن', 'دار التربية الحديثة', 'الأطفال', 'pdf', 'https://example.com/modern-parenting.pdf', 1, 'active', 1, '["تربية", "أطفال", "حديث", "تطور"]', 1, 1, NOW());

-- تحديث الأسعار للمواد المدفوعة
UPDATE library SET price = 50.00 WHERE title = 'العلاج المعرفي السلوكي';
UPDATE library SET price = 30.00 WHERE title = 'العلاقات الزوجية الصحية'; 