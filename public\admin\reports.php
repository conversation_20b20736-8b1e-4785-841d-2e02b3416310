<?php
/**
 * التقارير - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/reports');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';

// الحصول على التنبيهات
$alert = getAlert();

// معاملات التصفية
$period = $_GET['period'] ?? 'month';
$startDate = $_GET['start_date'] ?? '';
$endDate = $_GET['end_date'] ?? '';

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات المستخدمين
    $userStats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM users")[0]['count'] ?? 0,
        'active' => $db->select("SELECT COUNT(*) as count FROM users WHERE status = 'active'")[0]['count'] ?? 0,
        'therapists' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'")[0]['count'] ?? 0,
        'clients' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")[0]['count'] ?? 0
    ];
    
    // إحصائيات المواعيد
    $appointmentStats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM appointments")[0]['count'] ?? 0,
        'confirmed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'confirmed'")[0]['count'] ?? 0,
        'completed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'completed'")[0]['count'] ?? 0,
        'cancelled' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'cancelled'")[0]['count'] ?? 0
    ];
    
    // إحصائيات الإيرادات (إذا كانت متوفرة)
    $revenueStats = [
        'total' => 0,
        'this_month' => 0,
        'this_year' => 0
    ];
    
    // بيانات الرسوم البيانية
    $chartData = [
        'users' => [],
        'appointments' => [],
        'revenue' => []
    ];
    
} catch (Exception $e) {
    $userStats = ['total' => 0, 'active' => 0, 'therapists' => 0, 'clients' => 0];
    $appointmentStats = ['total' => 0, 'confirmed' => 0, 'completed' => 0, 'cancelled' => 0];
    $revenueStats = ['total' => 0, 'this_month' => 0, 'this_year' => 0];
    $chartData = ['users' => [], 'appointments' => [], 'revenue' => []];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'التقارير والإحصائيات';
$currentPage = 'admin_reports';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 20px;
    }

    .filter-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير والإحصائيات
                            </h2>
                            <p class="mb-0">عرض وتحليل بيانات المنصة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التصفية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card filter-container">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="period" class="form-label">الفترة الزمنية</label>
                            <select class="form-select" id="period" name="period">
                                <option value="week" <?= $period === 'week' ? 'selected' : '' ?>>أسبوع</option>
                                <option value="month" <?= $period === 'month' ? 'selected' : '' ?>>شهر</option>
                                <option value="quarter" <?= $period === 'quarter' ? 'selected' : '' ?>>ربع سنة</option>
                                <option value="year" <?= $period === 'year' ? 'selected' : '' ?>>سنة</option>
                                <option value="custom" <?= $period === 'custom' ? 'selected' : '' ?>>مخصص</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?= htmlspecialchars($startDate) ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="<?= htmlspecialchars($endDate) ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter"></i> تطبيق الفلتر
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المستخدمين -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['total'] ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['active'] ?></h3>
                    <p class="mb-0">المستخدمين النشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['therapists'] ?></h3>
                    <p class="mb-0">المعالجين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['clients'] ?></h3>
                    <p class="mb-0">العملاء</p>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات المواعيد -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-secondary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $appointmentStats['total'] ?></h3>
                    <p class="mb-0">إجمالي المواعيد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $appointmentStats['confirmed'] ?></h3>
                    <p class="mb-0">المواعيد المؤكدة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $appointmentStats['completed'] ?></h3>
                    <p class="mb-0">المواعيد المكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-danger text-white">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $appointmentStats['cancelled'] ?></h3>
                    <p class="mb-0">المواعيد الملغاة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    نمو المستخدمين
                </h5>
                <canvas id="usersChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المواعيد
                </h5>
                <canvas id="appointmentsChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإيرادات -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= number_format($revenueStats['total'], 2) ?> ر.س</h3>
                    <p class="mb-0">إجمالي الإيرادات</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= number_format($revenueStats['this_month'], 2) ?> ر.س</h3>
                    <p class="mb-0">إيرادات هذا الشهر</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= number_format($revenueStats['this_year'], 2) ?> ر.س</h3>
                    <p class="mb-0">إيرادات هذا العام</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تقارير مفصلة -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card chart-container">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        أفضل المعالجين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المعالج</th>
                                    <th>عدد المواعيد</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>د. أحمد محمد</td>
                                    <td>45</td>
                                    <td>
                                        <span class="text-warning">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>د. فاطمة علي</td>
                                    <td>38</td>
                                    <td>
                                        <span class="text-warning">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card chart-container">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        نشاط المنصة
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// رسم بياني للمستخدمين
const usersCtx = document.getElementById('usersChart').getContext('2d');
const usersChart = new Chart(usersCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'المستخدمين الجدد',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// رسم بياني للمواعيد
const appointmentsCtx = document.getElementById('appointmentsChart').getContext('2d');
const appointmentsChart = new Chart(appointmentsCtx, {
    type: 'doughnut',
    data: {
        labels: ['مؤكدة', 'مكتملة', 'ملغاة', 'في الانتظار'],
        datasets: [{
            data: [<?= $appointmentStats['confirmed'] ?>, <?= $appointmentStats['completed'] ?>, <?= $appointmentStats['cancelled'] ?>, <?= $appointmentStats['total'] - $appointmentStats['confirmed'] - $appointmentStats['completed'] - $appointmentStats['cancelled'] ?>],
            backgroundColor: [
                'rgb(54, 162, 235)',
                'rgb(75, 192, 192)',
                'rgb(255, 99, 132)',
                'rgb(255, 205, 86)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// رسم بياني للنشاط
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'bar',
    data: {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'المواعيد',
            data: [12, 19, 3, 5, 2, 3, 7],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgb(54, 162, 235)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 