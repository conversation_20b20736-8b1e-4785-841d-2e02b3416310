-- إنشاء جدول المستخدمين لمنصة نفسي
-- Create users table for Nafsi Platform

CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `user_type` enum('user','therapist','admin') NOT NULL DEFAULT 'user',
  `status` enum('active','inactive','suspended','pending') NOT NULL DEFAULT 'pending',
  `email_verified` tinyint(1) NOT NULL DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) NOT NULL DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `specialization` varchar(255) DEFAULT NULL COMMENT 'For therapists',
  `license_number` varchar(100) DEFAULT NULL COMMENT 'For therapists',
  `experience_years` int(11) DEFAULT NULL COMMENT 'For therapists',
  `hourly_rate` decimal(10,2) DEFAULT NULL COMMENT 'For therapists',
  `available_times` json DEFAULT NULL COMMENT 'For therapists',
  `languages` json DEFAULT NULL,
  `timezone` varchar(50) DEFAULT 'Asia/Riyadh',
  `notification_preferences` json DEFAULT NULL,
  `privacy_settings` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مستخدم إداري افتراضي
-- Insert default admin user
INSERT IGNORE INTO `users` (
    `email`, 
    `password`, 
    `first_name`, 
    `last_name`, 
    `user_type`, 
    `status`, 
    `email_verified`,
    `created_at`
) VALUES (
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'مدير',
    'النظام',
    'admin',
    'active',
    1,
    NOW()
);

-- إدراج مستخدم تجريبي
-- Insert test user
INSERT IGNORE INTO `users` (
    `email`, 
    `password`, 
    `first_name`, 
    `last_name`, 
    `user_type`, 
    `status`, 
    `email_verified`,
    `created_at`
) VALUES (
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'مستخدم',
    'تجريبي',
    'user',
    'active',
    1,
    NOW()
);

-- إدراج معالج نفسي تجريبي
-- Insert test therapist
INSERT IGNORE INTO `users` (
    `email`, 
    `password`, 
    `first_name`, 
    `last_name`, 
    `user_type`, 
    `status`, 
    `email_verified`,
    `specialization`,
    `license_number`,
    `experience_years`,
    `hourly_rate`,
    `bio`,
    `created_at`
) VALUES (
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'د. أحمد',
    'المعالج',
    'therapist',
    'active',
    1,
    'علم النفس السريري',
    'PSY-2023-001',
    5,
    150.00,
    'معالج نفسي متخصص في العلاج المعرفي السلوكي مع خبرة 5 سنوات في مجال الصحة النفسية.',
    NOW()
);

-- إنشاء جدول الجلسات
-- Create sessions table
CREATE TABLE IF NOT EXISTS `sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل تسجيل الدخول
-- Create login log table
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `status` enum('success','failed','blocked') NOT NULL,
  `failure_reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
