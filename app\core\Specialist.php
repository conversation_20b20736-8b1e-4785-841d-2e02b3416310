<?php
/**
 * كلاس الأخصائي
 * Specialist Class
 * 
 * هذا الكلاس يتعامل مع جميع عمليات الأخصائيين
 * بما في ذلك إدارة التخصصات، الجداول الزمنية،
 * التقييمات، والحجوزات
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Specialist {
    private $db;
    private $id;
    private $userId;
    private $specializations;
    private $experience;
    private $education;
    private $certifications;
    private $hourlyRate;
    private $availability;
    private $rating;
    private $status;
    private $bio;
    private $profileImage;
    private $createdAt;
    private $updatedAt;
    
    /**
     * حالات الأخصائي
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_PENDING = 'pending';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_VERIFIED = 'verified';
    
    /**
     * مستويات الخبرة
     */
    const EXPERIENCE_JUNIOR = 'junior';
    const EXPERIENCE_MID = 'mid';
    const EXPERIENCE_SENIOR = 'senior';
    const EXPERIENCE_EXPERT = 'expert';
    
    public function __construct() {
        $this->db = db();
    }
    
    /**
     * إنشاء ملف أخصائي جديد
     * @param array $data بيانات الأخصائي
     * @return bool|int معرف الأخصائي أو false في حالة الفشل
     */
    public function create($data) {
        try {
            // التحقق من صحة البيانات
            if (!$this->validateSpecialistData($data)) {
                return false;
            }
            
            // التحقق من وجود المستخدم
            $user = new User();
            if (!$user->exists($data['user_id'])) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // التحقق من عدم وجود ملف أخصائي مسبقاً
            if ($this->existsByUserId($data['user_id'])) {
                throw new Exception('يوجد ملف أخصائي مسبقاً لهذا المستخدم');
            }
            
            // إعداد البيانات الافتراضية
            $data['status'] = $data['status'] ?? self::STATUS_PENDING;
            $data['rating'] = $data['rating'] ?? 0;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "INSERT INTO specialists (user_id, specializations, experience, 
                    education, certifications, hourly_rate, bio, profile_image, 
                    status, rating, created_at, updated_at) 
                    VALUES (:user_id, :specializations, :experience, :education, 
                    :certifications, :hourly_rate, :bio, :profile_image, :status, 
                    :rating, :created_at, :updated_at)";
            
            $params = [
                ':user_id' => $data['user_id'],
                ':specializations' => json_encode($data['specializations']),
                ':experience' => $data['experience'],
                ':education' => $data['education'],
                ':certifications' => json_encode($data['certifications'] ?? []),
                ':hourly_rate' => $data['hourly_rate'],
                ':bio' => $data['bio'] ?? null,
                ':profile_image' => $data['profile_image'] ?? null,
                ':status' => $data['status'],
                ':rating' => $data['rating'],
                ':created_at' => $data['created_at'],
                ':updated_at' => $data['updated_at']
            ];
            
            $result = $this->db->insert($sql, $params);
            
            if ($result) {
                $specialistId = $this->db->getConnection()->lastInsertId();
                
                // إنشاء الجدول الزمني الافتراضي
                $this->createDefaultAvailability($specialistId);
                
                return $specialistId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Specialist Create Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث ملف الأخصائي
     * @param int $specialistId معرف الأخصائي
     * @param array $data البيانات المحدثة
     * @return bool
     */
    public function update($specialistId, $data) {
        try {
            // التحقق من وجود الأخصائي
            if (!$this->exists($specialistId)) {
                throw new Exception('الأخصائي غير موجود');
            }
            
            // التحقق من صحة البيانات
            if (!$this->validateUpdateData($data)) {
                return false;
            }
            
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE specialists SET ";
            $params = [];
            $updates = [];
            
            $allowedFields = ['specializations', 'experience', 'education', 
                            'certifications', 'hourly_rate', 'bio', 'profile_image', 
                            'status', 'updated_at'];
            
            foreach ($data as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    if (in_array($field, ['specializations', 'certifications'])) {
                        $value = json_encode($value);
                    }
                    $updates[] = "$field = :$field";
                    $params[":$field"] = $value;
                }
            }
            
            if (empty($updates)) {
                return false;
            }
            
            $sql .= implode(', ', $updates);
            $sql .= " WHERE id = :specialist_id";
            $params[':specialist_id'] = $specialistId;
            
            return $this->db->update($sql, $params);
            
        } catch (Exception $e) {
            error_log("Specialist Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على بيانات الأخصائي بالمعرف
     * @param int $specialistId معرف الأخصائي
     * @return array|false
     */
    public function getById($specialistId) {
        $sql = "SELECT s.*, u.first_name, u.last_name, u.email, u.phone 
                FROM specialists s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.id = :specialist_id";
        
        $result = $this->db->selectOne($sql, [':specialist_id' => $specialistId]);
        
        if ($result) {
            $result['specializations'] = json_decode($result['specializations'], true);
            $result['certifications'] = json_decode($result['certifications'], true);
        }
        
        return $result;
    }
    
    /**
     * الحصول على بيانات الأخصائي بمعرف المستخدم
     * @param int $userId معرف المستخدم
     * @return array|false
     */
    public function getByUserId($userId) {
        $sql = "SELECT s.*, u.first_name, u.last_name, u.email, u.phone 
                FROM specialists s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.user_id = :user_id";
        
        $result = $this->db->selectOne($sql, [':user_id' => $userId]);
        
        if ($result) {
            $result['specializations'] = json_decode($result['specializations'], true);
            $result['certifications'] = json_decode($result['certifications'], true);
        }
        
        return $result;
    }
    
    /**
     * البحث عن الأخصائيين
     * @param array $filters معايير البحث
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function search($filters = [], $limit = 20, $offset = 0) {
        $sql = "SELECT s.*, u.first_name, u.last_name, u.email, u.phone 
                FROM specialists s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.status = :active_status";
        
        $params = [':active_status' => self::STATUS_ACTIVE];
        
        if (isset($filters['specialization'])) {
            $sql .= " AND JSON_CONTAINS(s.specializations, :specialization)";
            $params[':specialization'] = json_encode($filters['specialization']);
        }
        
        if (isset($filters['experience'])) {
            $sql .= " AND s.experience = :experience";
            $params[':experience'] = $filters['experience'];
        }
        
        if (isset($filters['max_rate'])) {
            $sql .= " AND s.hourly_rate <= :max_rate";
            $params[':max_rate'] = $filters['max_rate'];
        }
        
        if (isset($filters['min_rating'])) {
            $sql .= " AND s.rating >= :min_rating";
            $params[':min_rating'] = $filters['min_rating'];
        }
        
        if (isset($filters['search'])) {
            $sql .= " AND (u.first_name LIKE :search OR u.last_name LIKE :search OR s.bio LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        $sql .= " ORDER BY s.rating DESC, s.created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $results = $this->db->select($sql, $params);
        
        // تحويل البيانات JSON
        foreach ($results as &$result) {
            $result['specializations'] = json_decode($result['specializations'], true);
            $result['certifications'] = json_decode($result['certifications'], true);
        }
        
        return $results;
    }
    
    /**
     * تحديث حالة الأخصائي
     * @param int $specialistId معرف الأخصائي
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateStatus($specialistId, $status) {
        $validStatuses = [self::STATUS_ACTIVE, self::STATUS_INACTIVE, 
                         self::STATUS_PENDING, self::STATUS_SUSPENDED, 
                         self::STATUS_VERIFIED];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }
        
        $sql = "UPDATE specialists SET status = :status, updated_at = :updated_at 
                WHERE id = :specialist_id";
        
        $params = [
            ':status' => $status,
            ':updated_at' => date('Y-m-d H:i:s'),
            ':specialist_id' => $specialistId
        ];
        
        return $this->db->update($sql, $params);
    }
    
    /**
     * تحديث التقييم
     * @param int $specialistId معرف الأخصائي
     * @param float $rating التقييم الجديد
     * @return bool
     */
    public function updateRating($specialistId, $rating) {
        if ($rating < 0 || $rating > 5) {
            return false;
        }
        
        // حساب التقييم المتوسط
        $currentSpecialist = $this->getById($specialistId);
        if (!$currentSpecialist) {
            return false;
        }
        
        // الحصول على جميع التقييمات
        $sql = "SELECT AVG(rating) as avg_rating FROM specialist_reviews 
                WHERE specialist_id = :specialist_id";
        $result = $this->db->selectOne($sql, [':specialist_id' => $specialistId]);
        
        $newRating = $result['avg_rating'] ?: $rating;
        
        $sql = "UPDATE specialists SET rating = :rating, updated_at = :updated_at 
                WHERE id = :specialist_id";
        
        $params = [
            ':rating' => $newRating,
            ':updated_at' => date('Y-m-d H:i:s'),
            ':specialist_id' => $specialistId
        ];
        
        return $this->db->update($sql, $params);
    }
    
    /**
     * إدارة الجدول الزمني
     * @param int $specialistId معرف الأخصائي
     * @param array $availability الجدول الزمني
     * @return bool
     */
    public function updateAvailability($specialistId, $availability) {
        try {
            // حذف الجدول الزمني الحالي
            $sql = "DELETE FROM specialist_availability WHERE specialist_id = :specialist_id";
            $this->db->execute($sql, [':specialist_id' => $specialistId]);
            
            // إضافة الجدول الزمني الجديد
            foreach ($availability as $day => $times) {
                foreach ($times as $time) {
                    $sql = "INSERT INTO specialist_availability (specialist_id, day_of_week, 
                            start_time, end_time) VALUES (:specialist_id, :day, :start_time, :end_time)";
                    
                    $params = [
                        ':specialist_id' => $specialistId,
                        ':day' => $day,
                        ':start_time' => $time['start'],
                        ':end_time' => $time['end']
                    ];
                    
                    $this->db->insert($sql, $params);
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Availability Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على الجدول الزمني
     * @param int $specialistId معرف الأخصائي
     * @return array
     */
    public function getAvailability($specialistId) {
        $sql = "SELECT * FROM specialist_availability 
                WHERE specialist_id = :specialist_id 
                ORDER BY day_of_week, start_time";
        
        $results = $this->db->select($sql, [':specialist_id' => $specialistId]);
        
        $availability = [];
        foreach ($results as $row) {
            $day = $row['day_of_week'];
            if (!isset($availability[$day])) {
                $availability[$day] = [];
            }
            $availability[$day][] = [
                'start' => $row['start_time'],
                'end' => $row['end_time']
            ];
        }
        
        return $availability;
    }
    
    /**
     * التحقق من توفر الموعد
     * @param int $specialistId معرف الأخصائي
     * @param string $date التاريخ
     * @param string $time الوقت
     * @return bool
     */
    public function isAvailable($specialistId, $date, $time) {
        // التحقق من الجدول الزمني
        $dayOfWeek = date('N', strtotime($date)); // 1 = Monday, 7 = Sunday
        $availability = $this->getAvailability($specialistId);
        
        if (!isset($availability[$dayOfWeek])) {
            return false;
        }
        
        $timeAvailable = false;
        foreach ($availability[$dayOfWeek] as $slot) {
            if ($time >= $slot['start'] && $time <= $slot['end']) {
                $timeAvailable = true;
                break;
            }
        }
        
        if (!$timeAvailable) {
            return false;
        }
        
        // التحقق من عدم وجود حجز في هذا الموعد
        $sql = "SELECT COUNT(*) as count FROM bookings 
                WHERE specialist_id = :specialist_id 
                AND booking_date = :date 
                AND booking_time = :time 
                AND status IN ('confirmed', 'pending')";
        
        $params = [
            ':specialist_id' => $specialistId,
            ':date' => $date,
            ':time' => $time
        ];
        
        $result = $this->db->selectOne($sql, $params);
        return $result['count'] == 0;
    }
    
    /**
     * التحقق من وجود الأخصائي
     * @param int $specialistId معرف الأخصائي
     * @return bool
     */
    public function exists($specialistId) {
        $sql = "SELECT COUNT(*) as count FROM specialists WHERE id = :specialist_id";
        $result = $this->db->selectOne($sql, [':specialist_id' => $specialistId]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود أخصائي بمعرف المستخدم
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function existsByUserId($userId) {
        $sql = "SELECT COUNT(*) as count FROM specialists WHERE user_id = :user_id";
        $result = $this->db->selectOne($sql, [':user_id' => $userId]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات الأخصائيين
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // إجمالي الأخصائيين
        $sql = "SELECT COUNT(*) as total FROM specialists";
        $result = $this->db->selectOne($sql);
        $stats['total_specialists'] = $result['total'];
        
        // الأخصائيين النشطين
        $sql = "SELECT COUNT(*) as active FROM specialists WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_ACTIVE]);
        $stats['active_specialists'] = $result['active'];
        
        // الأخصائيين المصدق عليهم
        $sql = "SELECT COUNT(*) as verified FROM specialists WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_VERIFIED]);
        $stats['verified_specialists'] = $result['verified'];
        
        // متوسط التقييم
        $sql = "SELECT AVG(rating) as avg_rating FROM specialists WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_ACTIVE]);
        $stats['average_rating'] = round($result['avg_rating'], 2);
        
        // توزيع التخصصات
        $sql = "SELECT specializations FROM specialists WHERE status = :status";
        $results = $this->db->select($sql, [':status' => self::STATUS_ACTIVE]);
        
        $specializations = [];
        foreach ($results as $row) {
            $specs = json_decode($row['specializations'], true);
            foreach ($specs as $spec) {
                if (!isset($specializations[$spec])) {
                    $specializations[$spec] = 0;
                }
                $specializations[$spec]++;
            }
        }
        $stats['specializations'] = $specializations;
        
        return $stats;
    }
    
    /**
     * التحقق من صحة بيانات الأخصائي
     * @param array $data البيانات
     * @return bool
     */
    private function validateSpecialistData($data) {
        // التحقق من الحقول المطلوبة
        $required = ['user_id', 'specializations', 'experience', 'education', 'hourly_rate'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return false;
            }
        }
        
        // التحقق من صحة التخصصات
        if (!is_array($data['specializations']) || empty($data['specializations'])) {
            return false;
        }
        
        // التحقق من صحة مستوى الخبرة
        $validExperiences = [self::EXPERIENCE_JUNIOR, self::EXPERIENCE_MID, 
                           self::EXPERIENCE_SENIOR, self::EXPERIENCE_EXPERT];
        if (!in_array($data['experience'], $validExperiences)) {
            return false;
        }
        
        // التحقق من صحة السعر بالساعة
        if (!is_numeric($data['hourly_rate']) || $data['hourly_rate'] < 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة بيانات التحديث
     * @param array $data البيانات
     * @return bool
     */
    private function validateUpdateData($data) {
        // التحقق من صحة التخصصات إذا كانت موجودة
        if (isset($data['specializations']) && (!is_array($data['specializations']) || empty($data['specializations']))) {
            return false;
        }
        
        // التحقق من صحة مستوى الخبرة إذا كان موجوداً
        if (isset($data['experience'])) {
            $validExperiences = [self::EXPERIENCE_JUNIOR, self::EXPERIENCE_MID, 
                               self::EXPERIENCE_SENIOR, self::EXPERIENCE_EXPERT];
            if (!in_array($data['experience'], $validExperiences)) {
                return false;
            }
        }
        
        // التحقق من صحة السعر بالساعة إذا كان موجوداً
        if (isset($data['hourly_rate']) && (!is_numeric($data['hourly_rate']) || $data['hourly_rate'] < 0)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * إنشاء الجدول الزمني الافتراضي
     * @param int $specialistId معرف الأخصائي
     */
    private function createDefaultAvailability($specialistId) {
        $defaultSchedule = [
            1 => [['start' => '09:00', 'end' => '17:00']], // Monday
            2 => [['start' => '09:00', 'end' => '17:00']], // Tuesday
            3 => [['start' => '09:00', 'end' => '17:00']], // Wednesday
            4 => [['start' => '09:00', 'end' => '17:00']], // Thursday
            5 => [['start' => '09:00', 'end' => '17:00']], // Friday
        ];
        
        $this->updateAvailability($specialistId, $defaultSchedule);
    }
}
?> 