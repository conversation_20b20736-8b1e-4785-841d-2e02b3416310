<?php
/**
 * سكريبت تثبيت جدول رسائل التواصل
 * Contact Messages Table Installation Script
 */

// تضمين ملف التهيئة
require_once 'app/init.php';

try {
    $db = Database::getInstance();
    
    echo "جاري تثبيت جدول رسائل التواصل...\n";
    
    // التحقق من وجود الجدول
    $tableExists = $db->select("SHOW TABLES LIKE 'contact_messages'");
    
    if (!empty($tableExists)) {
        echo "✅ جدول contact_messages موجود بالفعل\n";
    } else {
        // إنشاء الجدول
        $sql = "
        CREATE TABLE contact_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            subject ENUM('general', 'technical', 'billing', 'support', 'partnership', 'other') NOT NULL,
            message TEXT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
            admin_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $result = $db->query($sql);
        
        if ($result) {
            echo "✅ تم إنشاء جدول contact_messages بنجاح\n";
            
            // إنشاء الفهارس
            $indexes = [
                "CREATE INDEX idx_contact_messages_status ON contact_messages(status)",
                "CREATE INDEX idx_contact_messages_created_at ON contact_messages(created_at)",
                "CREATE INDEX idx_contact_messages_email ON contact_messages(email)"
            ];
            
            foreach ($indexes as $index) {
                $db->query($index);
            }
            
            echo "✅ تم إنشاء الفهارس بنجاح\n";
        } else {
            echo "❌ فشل في إنشاء جدول contact_messages\n";
        }
    }
    
    echo "\n✅ تم الانتهاء من تثبيت جدول رسائل التواصل\n";
    echo "يمكنك الآن الوصول إلى صفحة إدارة الرسائل على:\n";
    echo "http://localhost/nafsi_platform/admin/contact_messages\n";
    
} catch (Exception $e) {
    echo "❌ حدث خطأ: " . $e->getMessage() . "\n";
}
?> 