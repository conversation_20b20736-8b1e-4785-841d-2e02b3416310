# مخطط قاعدة البيانات - منصة نفسي

## نظرة عامة
هذا المخطط يوضح جميع جداول قاعدة البيانات والعلاقات بينها لمنصة نفسي.

## الجداول الرئيسية

### 1. جدول المستخدمين العاديين (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    profile_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. جدول الأخصائيين (specialists)
```sql
CREATE TABLE specialists (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNIQUE,
    license_number VARCHAR(100) UNIQUE,
    specialization TEXT,
    experience_years INT,
    education TEXT,
    certifications TEXT,
    bio TEXT,
    hourly_rate DECIMAL(10,2),
    is_approved BOOLEAN DEFAULT FALSE,
    approval_date DATETIME,
    rejection_reason TEXT,
    total_sessions INT DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 3. جدول المشرفين (admins)
```sql
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNIQUE,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 4. جدول الحجوزات (bookings)
```sql
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    specialist_id INT NOT NULL,
    session_date DATE NOT NULL,
    session_time TIME NOT NULL,
    duration INT DEFAULT 60, -- بالدقائق
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    session_type ENUM('video', 'audio', 'chat') DEFAULT 'video',
    notes TEXT,
    price DECIMAL(10,2),
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES specialists(id) ON DELETE CASCADE
);
```

### 5. جدول الجلسات (sessions)
```sql
CREATE TABLE sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT UNIQUE,
    start_time DATETIME,
    end_time DATETIME,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    session_url VARCHAR(500),
    session_key VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);
```

### 6. جدول الرسائل (messages)
```sql
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    sender_id INT NOT NULL,
    sender_type ENUM('user', 'specialist') NOT NULL,
    message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
    content TEXT NOT NULL,
    file_url VARCHAR(500),
    file_name VARCHAR(255),
    file_size INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
);
```

### 7. جدول التقييمات (ratings)
```sql
CREATE TABLE ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT UNIQUE,
    user_id INT NOT NULL,
    specialist_id INT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (specialist_id) REFERENCES specialists(id) ON DELETE CASCADE
);
```

### 8. جدول المحتوى التعليمي (content)
```sql
CREATE TABLE content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content_type ENUM('article', 'video', 'audio', 'pdf') NOT NULL,
    content TEXT,
    excerpt TEXT,
    featured_image VARCHAR(255),
    video_url VARCHAR(500),
    audio_url VARCHAR(500),
    pdf_url VARCHAR(500),
    author_id INT,
    category_id INT,
    tags JSON,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    views_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    published_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 9. جدول التصنيفات (categories)
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id INT,
    image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);
```

### 10. جدول الجداول الزمنية للأخصائيين (specialist_schedules)
```sql
CREATE TABLE specialist_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    specialist_id INT NOT NULL,
    day_of_week ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (specialist_id) REFERENCES specialists(id) ON DELETE CASCADE
);
```

### 11. جدول الإشعارات (notifications)
```sql
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 12. جدول المفضلة (favorites)
```sql
CREATE TABLE favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    content_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, content_id)
);
```

### 13. جدول التعليقات (comments)
```sql
CREATE TABLE comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_id INT NOT NULL,
    user_id INT NOT NULL,
    parent_id INT,
    comment TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
);
```

### 14. جدول سجل النشاطات (activity_logs)
```sql
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

## الفهارس المهمة (Indexes)

```sql
-- فهارس لتحسين الأداء
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_bookings_user_id ON bookings(user_id);
CREATE INDEX idx_bookings_specialist_id ON bookings(specialist_id);
CREATE INDEX idx_bookings_session_date ON bookings(session_date);
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_content_status ON content(status);
CREATE INDEX idx_content_published_at ON content(published_at);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
```

## العلاقات بين الجداول

### العلاقات الرئيسية:
1. **users** ↔ **specialists** (1:1)
2. **users** ↔ **admins** (1:1)
3. **users** ↔ **bookings** (1:N)
4. **specialists** ↔ **bookings** (1:N)
5. **bookings** ↔ **sessions** (1:1)
6. **sessions** ↔ **messages** (1:N)
7. **sessions** ↔ **ratings** (1:1)
8. **content** ↔ **categories** (N:1)
9. **users** ↔ **favorites** (1:N)
10. **content** ↔ **favorites** (1:N)

## إعدادات إضافية

### إعدادات الأمان:
```sql
-- تشفير كلمات المرور
ALTER TABLE users MODIFY password_hash VARCHAR(255) NOT NULL;

-- إضافة فهارس للبحث
CREATE FULLTEXT INDEX idx_content_search ON content(title, content, excerpt);
CREATE FULLTEXT INDEX idx_specialists_search ON specialists(specialization, bio);
```

### إعدادات النسخ الاحتياطي:
```sql
-- إضافة حقول للنسخ الاحتياطي
ALTER TABLE users ADD COLUMN backup_created_at TIMESTAMP NULL;
ALTER TABLE specialists ADD COLUMN backup_created_at TIMESTAMP NULL;
```

## ملاحظات مهمة

1. **الأمان**: جميع كلمات المرور يجب تشفيرها باستخدام `password_hash()`
2. **الخصوصية**: البيانات الحساسة يجب تشفيرها
3. **الأداء**: استخدام الفهارس المناسبة لتحسين الأداء
4. **النسخ الاحتياطي**: إعداد نظام نسخ احتياطي منتظم
5. **التوافق**: التأكد من توافق قاعدة البيانات مع PHP 8.0+

## حجم قاعدة البيانات المتوقع

- **المستخدمين**: 10,000+ مستخدم
- **الأخصائيين**: 500+ أخصائي
- **الجلسات**: 50,000+ جلسة شهرياً
- **المحتوى**: 1,000+ مقال وفيديو
- **الرسائل**: 1,000,000+ رسالة شهرياً

## استراتيجية النسخ الاحتياطي

1. **نسخ احتياطي يومي** لجميع الجداول
2. **نسخ احتياطي فوري** للجداول المهمة (users, sessions, messages)
3. **تشفير النسخ الاحتياطي** لحماية البيانات
4. **اختبار استعادة النسخ الاحتياطي** بشكل دوري 