-- جدول المكتبة
CREATE TABLE library (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    author VARCHAR(100),
    publisher VARCHAR(100),
    isbn VARCHAR(20),
    publication_year INT,
    language VARCHAR(50) DEFAULT 'Arabic',
    category VARCHAR(100),
    tags JSON,
    file_type ENUM('pdf', 'doc', 'docx', 'epub', 'video', 'audio', 'link') NOT NULL,
    file_url VARCHAR(500),
    file_size INT, -- بالكيلوبايت
    thumbnail VARCHAR(255),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT TRUE,
    price DECIMAL(10,2) DEFAULT 0.00,
    created_by INT,
    approved_by INT,
    approved_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء فهارس للمكتبة
CREATE INDEX idx_library_status ON library(status);
CREATE INDEX idx_library_category ON library(category);
CREATE INDEX idx_library_created_at ON library(created_at);
CREATE INDEX idx_library_is_featured ON library(is_featured);
CREATE FULLTEXT INDEX idx_library_search ON library(title, description, author, publisher);

-- جدول تقييمات المكتبة
CREATE TABLE library_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_library_rating (user_id, library_id)
);

-- جدول تحميلات المكتبة
CREATE TABLE library_downloads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT NOT NULL,
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول مشاهدات المكتبة
CREATE TABLE library_views (
    id INT PRIMARY KEY AUTO_INCREMENT,
    library_id INT NOT NULL,
    user_id INT,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
); 