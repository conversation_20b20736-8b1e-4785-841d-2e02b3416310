<?php
/**
 * ملف اختبار استعلام المعالجين
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>اختبار استعلام المعالجين</h2>";
    
    // اختبار 1: فحص جميع المستخدمين
    echo "<h3>1. جميع المستخدمين:</h3>";
    $allUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users");
    if ($allUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام جميع المستخدمين</p>";
    } else {
        echo "<p>إجمالي المستخدمين: " . count($allUsers) . "</p>";
        foreach ($allUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // اختبار 2: فحص المعالجين
    echo "<h3>2. المعالجين (user_type = 'specialist'):</h3>";
    $specialists = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE user_type = 'specialist'");
    if ($specialists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
        // محاولة معرفة سبب الخطأ
        echo "<p>محاولة معرفة سبب الخطأ:</p>";
        $error = $db->getLastError();
        if ($error) {
            echo "<p style='color: red;'>خطأ SQL: " . $error . "</p>";
        }
    } elseif (empty($specialists)) {
        echo "<p style='color: orange;'>لا يوجد معالجين (user_type = 'specialist')</p>";
    } else {
        echo "<p>عدد المعالجين: " . count($specialists) . "</p>";
        foreach ($specialists as $specialist) {
            echo "- " . $specialist['first_name'] . ' ' . $specialist['last_name'] . " (" . $specialist['email'] . ")<br>";
        }
    }
    
    // اختبار 3: فحص المستخدمين الذين يبدأ username بـ 'dr.'
    echo "<h3>3. المستخدمين الذين يبدأ username بـ 'dr.':</h3>";
    $drUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE username LIKE 'dr.%'");
    if ($drUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام المستخدمين dr.</p>";
    } elseif (empty($drUsers)) {
        echo "<p style='color: orange;'>لا يوجد مستخدمين يبدأ username بـ 'dr.'</p>";
    } else {
        echo "<p>عدد المستخدمين dr.: " . count($drUsers) . "</p>";
        foreach ($drUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // اختبار 4: فحص المستخدمين بدون user_type
    echo "<h3>4. المستخدمين بدون user_type:</h3>";
    $nullUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE user_type IS NULL OR user_type = ''");
    if ($nullUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام المستخدمين بدون user_type</p>";
    } elseif (empty($nullUsers)) {
        echo "<p style='color: green;'>لا يوجد مستخدمين بدون user_type</p>";
    } else {
        echo "<p>عدد المستخدمين بدون user_type: " . count($nullUsers) . "</p>";
        foreach ($nullUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // اختبار 5: فحص المستخدمين الموجودين
    echo "<h3>5. فحص المستخدمين المحددين:</h3>";
    $specificUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE username IN ('dr.ahmed', 'dr.fatima', 'dr.omar', 'dr.layla', 'dr.youssef')");
    if ($specificUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام المستخدمين المحددين</p>";
    } elseif (empty($specificUsers)) {
        echo "<p style='color: orange;'>لا يوجد المستخدمين المحددين</p>";
    } else {
        echo "<p>عدد المستخدمين المحددين: " . count($specificUsers) . "</p>";
        foreach ($specificUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 