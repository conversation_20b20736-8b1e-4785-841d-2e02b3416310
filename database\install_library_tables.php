<?php
/**
 * إنشاء جداول المكتبة في قاعدة البيانات
 */

// تضمين ملف التكوين
require_once __DIR__ . '/../app/config.php';
require_once __DIR__ . '/../app/database.php';

try {
    $db = db();
    
    echo "بدء إنشاء جداول المكتبة...\n";
    
    // إنشاء جدول المكتبة
    $db->query("
        CREATE TABLE IF NOT EXISTS library (
            id INT PRIMARY KEY AUTO_INCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            author VA<PERSON><PERSON><PERSON>(100),
            publisher VARCHAR(100),
            isbn VARCHAR(20),
            publication_year INT,
            language VARCHAR(50) DEFAULT 'Arabic',
            category VARCHAR(100),
            tags JSON,
            file_type ENUM('pdf', 'doc', 'docx', 'epub', 'video', 'audio', 'link') NOT NULL,
            file_url VARCHAR(500),
            file_size INT,
            thumbnail VARCHAR(255),
            download_count INT DEFAULT 0,
            view_count INT DEFAULT 0,
            rating DECIMAL(3,2) DEFAULT 0.00,
            total_ratings INT DEFAULT 0,
            status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
            is_featured BOOLEAN DEFAULT FALSE,
            is_free BOOLEAN DEFAULT TRUE,
            price DECIMAL(10,2) DEFAULT 0.00,
            created_by INT,
            approved_by INT,
            approved_at DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "✓ تم إنشاء جدول library\n";
    
    // إنشاء جدول تقييمات المكتبة
    $db->query("
        CREATE TABLE IF NOT EXISTS library_ratings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            library_id INT NOT NULL,
            user_id INT NOT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_library_rating (user_id, library_id)
        )
    ");
    echo "✓ تم إنشاء جدول library_ratings\n";
    
    // إنشاء جدول تحميلات المكتبة
    $db->query("
        CREATE TABLE IF NOT EXISTS library_downloads (
            id INT PRIMARY KEY AUTO_INCREMENT,
            library_id INT NOT NULL,
            user_id INT NOT NULL,
            downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "✓ تم إنشاء جدول library_downloads\n";
    
    // إنشاء جدول مشاهدات المكتبة
    $db->query("
        CREATE TABLE IF NOT EXISTS library_views (
            id INT PRIMARY KEY AUTO_INCREMENT,
            library_id INT NOT NULL,
            user_id INT,
            viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (library_id) REFERENCES library(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
    ");
    echo "✓ تم إنشاء جدول library_views\n";
    
    // إنشاء الفهارس
    echo "إنشاء الفهارس...\n";
    
    $db->query("CREATE INDEX IF NOT EXISTS idx_library_status ON library(status)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_library_category ON library(category)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_library_created_at ON library(created_at)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_library_is_featured ON library(is_featured)");
    $db->query("CREATE FULLTEXT INDEX IF NOT EXISTS idx_library_search ON library(title, description, author, publisher)");
    
    echo "✓ تم إنشاء جميع الفهارس\n";
    
    // إضافة بيانات تجريبية
    echo "إضافة بيانات تجريبية...\n";
    
    $sampleData = [
        [
            'title' => 'أساسيات علم النفس',
            'description' => 'كتاب شامل يغطي أساسيات علم النفس الحديث وأهم النظريات والتطبيقات العملية',
            'author' => 'د. أحمد محمد',
            'publisher' => 'دار النشر العلمية',
            'category' => 'علم النفس',
            'file_type' => 'pdf',
            'file_url' => 'https://example.com/psychology-basics.pdf',
            'is_free' => 1,
            'status' => 'active',
            'is_featured' => 1,
            'tags' => json_encode(['علم النفس', 'أساسيات', 'نظريات'])
        ],
        [
            'title' => 'العلاج المعرفي السلوكي',
            'description' => 'دليل عملي للعلاج المعرفي السلوكي مع أمثلة وتطبيقات عملية',
            'author' => 'د. فاطمة علي',
            'publisher' => 'مركز الدراسات النفسية',
            'category' => 'العلاج النفسي',
            'file_type' => 'pdf',
            'file_url' => 'https://example.com/cbt-guide.pdf',
            'is_free' => 0,
            'price' => 50.00,
            'status' => 'active',
            'is_featured' => 1,
            'tags' => json_encode(['علاج', 'معرفي', 'سلوكي', 'CBT'])
        ],
        [
            'title' => 'تنمية الذات والثقة',
            'description' => 'برنامج متكامل لتنمية الذات وبناء الثقة بالنفس',
            'author' => 'د. خالد عبدالله',
            'publisher' => 'مؤسسة التنمية البشرية',
            'category' => 'التنمية البشرية',
            'file_type' => 'video',
            'file_url' => 'https://example.com/self-development.mp4',
            'is_free' => 1,
            'status' => 'active',
            'is_featured' => 0,
            'tags' => json_encode(['تنمية', 'ذات', 'ثقة', 'برنامج'])
        ],
        [
            'title' => 'العلاقات الزوجية الصحية',
            'description' => 'دليل شامل لبناء علاقات زوجية صحية ومستقرة',
            'author' => 'د. سارة أحمد',
            'publisher' => 'مركز الاستشارات الأسرية',
            'category' => 'العلاقات',
            'file_type' => 'audio',
            'file_url' => 'https://example.com/marriage-relationships.mp3',
            'is_free' => 0,
            'price' => 30.00,
            'status' => 'active',
            'is_featured' => 0,
            'tags' => json_encode(['علاقات', 'زوجية', 'أسرة', 'صحة'])
        ],
        [
            'title' => 'تربية الأطفال في العصر الحديث',
            'description' => 'أساليب حديثة في تربية الأطفال مع التركيز على التطور النفسي والاجتماعي',
            'author' => 'د. محمد حسن',
            'publisher' => 'دار التربية الحديثة',
            'category' => 'الأطفال',
            'file_type' => 'pdf',
            'file_url' => 'https://example.com/modern-parenting.pdf',
            'is_free' => 1,
            'status' => 'active',
            'is_featured' => 1,
            'tags' => json_encode(['تربية', 'أطفال', 'حديث', 'تطور'])
        ]
    ];
    
    foreach ($sampleData as $data) {
        $data['created_by'] = 1; // افتراض أن المدير الأول هو منشئ المحتوى
        $data['approved_by'] = 1;
        $data['approved_at'] = date('Y-m-d H:i:s');
        
        $db->insert("library", $data);
    }
    
    echo "✓ تم إضافة البيانات التجريبية\n";
    
    echo "\n🎉 تم إنشاء جميع جداول المكتبة بنجاح!\n";
    echo "يمكنك الآن الوصول إلى صفحة إدارة المكتبة من لوحة الإدارة.\n";
    
} catch (Exception $e) {
    echo "❌ حدث خطأ: " . $e->getMessage() . "\n";
}
?> 