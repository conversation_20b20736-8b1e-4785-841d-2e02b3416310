<?php
/**
 * صفحة تسجيل الخروج
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/logout');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل الخروج
$auth = new Auth();
$logoutSuccess = $auth->logout();

// تدمير الجلسة
session_destroy();

// تعيين رسالة تسجيل الخروج
if ($logoutSuccess) {
    // بدء جلسة جديدة لعرض الرسالة
    session_start();
    setAlert('تم تسجيل الخروج بنجاح', 'success');
} else {
    session_start();
    setAlert('حدث خطأ أثناء تسجيل الخروج', 'error');
}

// إعادة التوجيه لصفحة تسجيل الدخول
header('Location: /nafsi_platform/login');
exit;
?>