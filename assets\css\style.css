/* منصة نفسي - الأنماط المخصصة */

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* الألوان المخصصة */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* تحسينات عامة */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* تحسينات النافبار */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.nav-link.active {
    color: var(--primary-color) !important;
    font-weight: 600;
}

/* تحسينات البطاقات */
.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    padding: 0.375rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* تحسينات النماذج */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: 0.35rem;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* تحسينات الجداول */
.table {
    border-radius: 0.35rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

/* تحسينات البادج */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
}

/* تحسينات الصور */
.img-fluid {
    border-radius: 0.35rem;
}

/* تحسينات القوائم */
.list-group-item {
    border: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.list-group-item:hover {
    background-color: #f8f9fc;
}

/* تحسينات الفوتر */
footer {
    margin-top: auto;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
}

/* زر العودة للأعلى */
#backToTop {
    z-index: 1000;
    transition: all 0.3s ease;
}

#backToTop:hover {
    transform: translateY(-2px);
}

/* تحسينات الصفحة الرئيسية */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #224abe 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.25rem;
    opacity: 0.9;
}

/* تحسينات البطاقات المميزة */
.feature-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* تحسينات النماذج */
.form-container {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* تحسينات الأخصائيين */
.specialist-card {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.3s ease;
}

.specialist-card:hover {
    transform: translateY(-3px);
}

.specialist-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid white;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.rating-stars {
    color: #f6c23e;
}

/* تحسينات المكتبة */
.content-card {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-3px);
}

.content-image {
    height: 200px;
    object-fit: cover;
}

/* تحسينات لوحة التحكم */
.dashboard-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* تحسينات المحادثة */
.chat-container {
    height: 400px;
    border: 1px solid #e3e6f0;
    border-radius: 0.5rem;
    background: white;
}

.chat-messages {
    height: 300px;
    overflow-y: auto;
    padding: 1rem;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    max-width: 70%;
}

.message.sent {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
}

.message.received {
    background-color: #f8f9fc;
    color: #333;
}

/* تحسينات التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .feature-card {
        margin-bottom: 2rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .footer,
    .btn,
    #backToTop {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
} 