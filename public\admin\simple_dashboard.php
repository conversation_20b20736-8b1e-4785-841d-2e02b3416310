<?php
/**
 * لوحة تحكم المدير المبسطة
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/simple_dashboard');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على البيانات
try {
    $db = db();
    
    // إحصائيات المستخدمين
    $userStats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM users")[0]['count'] ?? 0,
        'active' => $db->select("SELECT COUNT(*) as count FROM users WHERE status = 'active'")[0]['count'] ?? 0,
        'therapists' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'")[0]['count'] ?? 0,
        'clients' => $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")[0]['count'] ?? 0
    ];
    
    // إحصائيات المواعيد
    $appointmentStats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM appointments")[0]['count'] ?? 0,
        'confirmed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'confirmed'")[0]['count'] ?? 0,
        'completed' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'completed'")[0]['count'] ?? 0,
        'cancelled' => $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'cancelled'")[0]['count'] ?? 0
    ];
    
    // إحصائيات المحتوى
    $contentStats = [
        'total' => $db->select("SELECT COUNT(*) as count FROM content")[0]['count'] ?? 0,
        'published' => $db->select("SELECT COUNT(*) as count FROM content WHERE status = 'published'")[0]['count'] ?? 0,
        'draft' => $db->select("SELECT COUNT(*) as count FROM content WHERE status = 'draft'")[0]['count'] ?? 0
    ];
    
    // النشاطات الأخيرة
    $recentActivities = $db->select("
        SELECT 'user' as type, first_name, last_name, created_at, 'مستخدم جديد' as action
        FROM users 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
} catch (Exception $e) {
    $userStats = ['total' => 0, 'active' => 0, 'therapists' => 0, 'clients' => 0];
    $appointmentStats = ['total' => 0, 'confirmed' => 0, 'completed' => 0, 'cancelled' => 0];
    $contentStats = ['total' => 0, 'published' => 0, 'draft' => 0];
    $recentActivities = [];
    setAlert('حدث خطأ في تحميل البيانات: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'لوحة التحكم المبسطة';
$currentPage = 'admin_simple_dashboard';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .action-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .activity-item {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background: #e8f0ff;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم المبسطة
                            </h2>
                            <p class="mb-0">مرحباً <?= htmlspecialchars($currentUser['first_name']) ?>، إليك نظرة عامة على المنصة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/dashboard') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> لوحة التحكم الكاملة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['total'] ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $appointmentStats['total'] ?></h3>
                    <p class="mb-0">إجمالي المواعيد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $userStats['therapists'] ?></h3>
                    <p class="mb-0">المعالجين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h3 class="mb-1"><?= $contentStats['total'] ?></h3>
                    <p class="mb-0">المحتويات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-bolt me-2"></i>
                الإجراءات السريعة
            </h4>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card action-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                    <h5>إدارة المستخدمين</h5>
                    <p class="text-muted">عرض وإدارة جميع المستخدمين</p>
                    <a href="<?= url('admin/users') ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> عرض
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card action-card">
                <div class="card-body text-center">
                    <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                    <h5>إدارة المعالجين</h5>
                    <p class="text-muted">عرض وإدارة المعالجين النفسيين</p>
                    <a href="<?= url('admin/specialists') ?>" class="btn btn-success">
                        <i class="fas fa-arrow-left"></i> عرض
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card action-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                    <h5>إدارة المواعيد</h5>
                    <p class="text-muted">عرض وإدارة جميع المواعيد</p>
                    <a href="<?= url('admin/appointments') ?>" class="btn btn-info">
                        <i class="fas fa-arrow-left"></i> عرض
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card action-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-3x text-warning mb-3"></i>
                    <h5>إدارة المحتوى</h5>
                    <p class="text-muted">عرض وإدارة المحتويات</p>
                    <a href="<?= url('admin/content') ?>" class="btn btn-warning">
                        <i class="fas fa-arrow-left"></i> عرض
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات مفصلة -->
    <div class="row mb-4">
        <div class="col-md-6 mb-4">
            <div class="card action-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        إحصائيات المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h4 class="text-success"><?= $userStats['active'] ?></h4>
                                <p class="text-muted">المستخدمين النشطين</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-user fa-2x text-info mb-2"></i>
                                <h4 class="text-info"><?= $userStats['clients'] ?></h4>
                                <p class="text-muted">العملاء</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card action-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        إحصائيات المواعيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-success"><?= $appointmentStats['confirmed'] ?></h4>
                                <p class="text-muted">المواعيد المؤكدة</p>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-clipboard-check fa-2x text-info mb-2"></i>
                                <h4 class="text-info"><?= $appointmentStats['completed'] ?></h4>
                                <p class="text-muted">المواعيد المكتملة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- النشاطات الأخيرة -->
    <div class="row">
        <div class="col-12">
            <div class="card action-card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        النشاطات الأخيرة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recentActivities)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نشاطات حديثة</h5>
                        <p class="text-muted">ستظهر هنا النشاطات الجديدة في المنصة</p>
                    </div>
                    <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentActivities as $activity): ?>
                        <div class="list-group-item activity-item">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-user-circle fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']) ?></h6>
                                    <p class="mb-1 text-muted"><?= $activity['action'] ?></p>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($activity['created_at'])) ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 