<?php
/**
 * لوحة تحكم المدير الجديدة
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/dashboard');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على إحصائيات النظام
try {
    $db = db();
    
    // إحصائيات المستخدمين
    $totalUsers = $db->select("SELECT COUNT(*) as count FROM users")[0]['count'] ?? 0;
    $activeUsers = $db->select("SELECT COUNT(*) as count FROM users WHERE status = 'active'")[0]['count'] ?? 0;
    $therapists = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist'")[0]['count'] ?? 0;
    $clients = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'client'")[0]['count'] ?? 0;
    
    // إحصائيات الجلسات
    $totalAppointments = $db->select("SELECT COUNT(*) as count FROM appointments")[0]['count'] ?? 0;
    $confirmedAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'confirmed'")[0]['count'] ?? 0;
    $completedAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE status = 'completed'")[0]['count'] ?? 0;
    
    // المستخدمين الجدد (آخر 7 أيام)
    $newUsers = $db->select("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")[0]['count'] ?? 0;
    
    // أحدث المستخدمين
    $recentUsers = $db->select("SELECT id, first_name, last_name, email, user_type, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    
    // أحدث المواعيد
    $recentAppointments = $db->select("
        SELECT a.*, 
               c.first_name as client_name, c.last_name as client_lastname,
               t.first_name as therapist_name, t.last_name as therapist_lastname
        FROM appointments a 
        LEFT JOIN users c ON a.client_id = c.id 
        LEFT JOIN users t ON a.therapist_id = t.id 
        ORDER BY a.created_at DESC 
        LIMIT 5
    ");
    
} catch (Exception $e) {
    $totalUsers = $activeUsers = $therapists = $clients = 0;
    $totalAppointments = $confirmedAppointments = $completedAppointments = 0;
    $newUsers = 0;
    $recentUsers = $recentAppointments = [];
}

// تعيين متغيرات الصفحة
$pageTitle = 'لوحة تحكم المدير';
$currentPage = 'admin_dashboard';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<!-- تضمين الشريط الجانبي الجديد -->
<?php include 'includes/sidebar.php'; ?>

<!-- تضمين Font Awesome -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<!-- تضمين ملف CSS للوحة التحكم -->
<link href="<?= asset('css/admin-dashboard.css') ?>" rel="stylesheet">

<style>
    /* تحسينات إضافية للوحة التحكم */
    .admin-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .admin-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.7s;
    }

    .admin-header:hover::before {
        left: 100%;
    }

    .stats-card {
        border-radius: 20px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: none;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        overflow: hidden;
        position: relative;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stats-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .stats-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;
    }

    .stats-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        border-radius: 50%;
    }

    .recent-item {
        border-left: 4px solid #667eea;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        transition: all 0.3s ease;
        border-radius: 12px;
        position: relative;
        overflow: hidden;
    }

    .recent-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        transition: left 0.5s;
    }

    .recent-item:hover {
        background: linear-gradient(135deg, #e8f0ff 0%, #f8f9ff 100%);
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
    }

    .recent-item:hover::before {
        left: 100%;
    }

    .user-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    /* تحسين الأزرار */
    .btn {
        border-radius: 12px;
        transition: all 0.3s ease;
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
</style>

<div class="admin-main-content" id="adminMainContent">
    <div class="container-fluid">
        <!-- عرض التنبيهات -->
        <?php if ($alert): ?>
        <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
            <?= htmlspecialchars($alert['message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

    <!-- بطاقة الترحيب بالمدير -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card admin-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <div class="user-avatar">
                                <i class="fas fa-user-shield"></i>
                            </div>
                        </div>
                        <div class="col-md-10">
                            <h2 class="mb-2">مرحباً، <?= htmlspecialchars($currentUser['first_name'] ?? 'المدير') ?>!</h2>
                            <p class="mb-1">
                                <i class="fas fa-user-shield"></i> 
                                مدير النظام
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-envelope"></i> 
                                <?= htmlspecialchars($currentUser['email'] ?? '<EMAIL>') ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-calendar"></i> 
                                آخر تسجيل دخول: <?= isset($currentUser['last_login']) && $currentUser['last_login'] ? date('Y-m-d H:i', strtotime($currentUser['last_login'])) : 'لم يتم تسجيل الدخول من قبل' ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النظام -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-primary mx-auto">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="mb-1"><?= $totalUsers ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-success mx-auto">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="mb-1"><?= $therapists ?></h3>
                    <p class="mb-0">المعالجين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-info mx-auto">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3 class="mb-1"><?= $totalAppointments ?></h3>
                    <p class="mb-0">إجمالي المواعيد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-warning mx-auto">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3 class="mb-1"><?= $newUsers ?></h3>
                    <p class="mb-0">مستخدمين جدد</p>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-secondary text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-secondary mx-auto">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3 class="mb-1"><?= $activeUsers ?></h3>
                    <p class="mb-0">المستخدمين النشطين</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-success mx-auto">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1"><?= $confirmedAppointments ?></h3>
                    <p class="mb-0">المواعيد المؤكدة</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <div class="stats-icon bg-white text-info mx-auto">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3 class="mb-1"><?= $completedAppointments ?></h3>
                    <p class="mb-0">المواعيد المكتملة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الأقسام الرئيسية -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-users"></i> أحدث المستخدمين</h5>
                    <a href="<?= url('admin/users') ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-eye"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentUsers)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا يوجد مستخدمين مسجلين حالياً</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($recentUsers as $user): ?>
                    <div class="recent-item p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user text-primary"></i>
                                    <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-envelope"></i>
                                    <?= htmlspecialchars($user['email']) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-calendar"></i>
                                    <?= date('Y-m-d', strtotime($user['created_at'])) ?>
                                </p>
                            </div>
                            <span class="badge bg-<?= $user['user_type'] === 'admin' ? 'danger' : ($user['user_type'] === 'therapist' ? 'success' : 'primary') ?>">
                                <?= $user['user_type'] === 'admin' ? 'مدير' : ($user['user_type'] === 'therapist' ? 'معالج' : 'مستخدم') ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> أحدث المواعيد</h5>
                    <a href="<?= url('admin/appointments') ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-eye"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentAppointments)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>لا توجد مواعيد مسجلة حالياً</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($recentAppointments as $appointment): ?>
                    <div class="recent-item p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-calendar text-success"></i>
                                    <?= htmlspecialchars($appointment['client_name'] . ' ' . $appointment['client_lastname']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-user-md"></i>
                                    مع: <?= htmlspecialchars($appointment['therapist_name'] . ' ' . $appointment['therapist_lastname']) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-clock"></i>
                                    <?= date('Y-m-d H:i', strtotime($appointment['appointment_date'] . ' ' . $appointment['appointment_time'])) ?>
                                </p>
                            </div>
                            <span class="badge bg-<?= $appointment['status'] === 'confirmed' ? 'success' : ($appointment['status'] === 'completed' ? 'info' : 'warning') ?>">
                                <?= $appointment['status'] === 'confirmed' ? 'مؤكدة' : ($appointment['status'] === 'completed' ? 'مكتملة' : 'في الانتظار') ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة للمدير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-cogs"></i> إدارة النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('admin/users') ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-users"></i><br>
                                إدارة المستخدمين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('admin/appointments') ?>" class="btn btn-outline-success w-100">
                                <i class="fas fa-calendar"></i><br>
                                إدارة المواعيد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('admin/reports') ?>" class="btn btn-outline-info w-100">
                                <i class="fas fa-chart-bar"></i><br>
                                التقارير
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('admin/settings') ?>" class="btn btn-outline-warning w-100">
                                <i class="fas fa-cog"></i><br>
                                الإعدادات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?>

<script>
    // تحديث المحتوى الرئيسي عند إخفاء/إظهار الشريط الجانبي
    function updateMainContent() {
        const sidebar = document.getElementById('adminSidebar');
        const mainContent = document.getElementById('adminMainContent');
        
        if (sidebar && mainContent) {
            if (sidebar.classList.contains('collapsed')) {
                mainContent.classList.add('sidebar-collapsed');
            } else {
                mainContent.classList.remove('sidebar-collapsed');
            }
        }
    }
    
    // تشغيل عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateMainContent();
    });
    
    // مراقبة تغييرات الشريط الجانبي
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                updateMainContent();
            }
        });
    });
    
    const sidebar = document.getElementById('adminSidebar');
    if (sidebar) {
        observer.observe(sidebar, { attributes: true });
    }
</script>
