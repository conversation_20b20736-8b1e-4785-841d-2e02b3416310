<?php
/**
 * كلاس المستخدم
 * User Class
 * 
 * هذا الكلاس يتعامل مع جميع عمليات المستخدمين
 * بما في ذلك إنشاء الحسابات، تحديث الملف الشخصي،
 * إدارة الحالة، والصلاحيات
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class User {
    private $db;
    private $id;
    private $firstName;
    private $lastName;
    private $username;
    private $email;
    private $phone;
    private $dateOfBirth;
    private $gender;
    private $status;
    private $userType;
    private $createdAt;
    private $updatedAt;
    
    /**
     * أنواع المستخدمين
     */
    const USER_TYPE_REGULAR = 'regular';
    const USER_TYPE_SPECIALIST = 'specialist';
    const USER_TYPE_ADMIN = 'admin';
    
    /**
     * حالات المستخدم
     */
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_SUSPENDED = 0;
    const STATUS_PENDING = 0;
    
    /**
     * الجنس
     */
    const GENDER_MALE = 'male';
    const GENDER_FEMALE = 'female';
    const GENDER_OTHER = 'other';
    
    public function __construct() {
        $this->db = db();
    }
    
    /**
     * إنشاء حساب جديد
     * @param array $data بيانات المستخدم
     * @return bool|int معرف المستخدم أو false في حالة الفشل
     */
    public function create($data) {
        try {
            // التحقق من صحة البيانات
            if (!$this->validateUserData($data)) {
                return false;
            }
            
            // التحقق من عدم وجود البريد الإلكتروني مسبقاً
            if ($this->emailExists($data['email'])) {
                throw new Exception('البريد الإلكتروني مستخدم مسبقاً');
            }
            
            // التحقق من عدم وجود اسم المستخدم مسبقاً
            if (isset($data['username']) && $this->usernameExists($data['username'])) {
                throw new Exception('اسم المستخدم مستخدم مسبقاً');
            }
            
            // تشفير كلمة المرور
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // إعداد البيانات الافتراضية
            $data['status'] = $data['status'] ?? self::STATUS_ACTIVE;
            $data['user_type'] = $data['user_type'] ?? self::USER_TYPE_REGULAR;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "INSERT INTO users (first_name, last_name, username, email, phone, 
                    date_of_birth, gender, password_hash, is_active, user_type, created_at, updated_at) 
                    VALUES (:first_name, :last_name, :username, :email, :phone, 
                    :date_of_birth, :gender, :password_hash, :is_active, :user_type, :created_at, :updated_at)";
            
            $params = [
                ':first_name' => $data['first_name'],
                ':last_name' => $data['last_name'],
                ':username' => $data['username'] ?? null,
                ':email' => $data['email'],
                ':phone' => $data['phone'] ?? null,
                ':date_of_birth' => $data['date_of_birth'] ?? null,
                ':gender' => $data['gender'] ?? null,
                ':password_hash' => $data['password'],
                ':is_active' => $data['status'] === self::STATUS_ACTIVE ? 1 : 0,
                ':user_type' => $data['user_type'],
                ':created_at' => $data['created_at'],
                ':updated_at' => $data['updated_at']
            ];
            
            $result = $this->db->insert($sql, $params);
            
            if ($result) {
                $userId = $this->db->getConnection()->lastInsertId();
                
                // إرسال رسالة ترحيب
                $this->sendWelcomeEmail($data['email'], $data['first_name']);
                
                return $userId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("User Create Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث الملف الشخصي
     * @param int $userId معرف المستخدم
     * @param array $data البيانات المحدثة
     * @return bool
     */
    public function updateProfile($userId, $data) {
        try {
            // التحقق من وجود المستخدم
            if (!$this->exists($userId)) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // التحقق من صحة البيانات
            if (!$this->validateUpdateData($data)) {
                return false;
            }
            
            // التحقق من عدم استخدام البريد الإلكتروني من قبل مستخدم آخر
            if (isset($data['email']) && $this->emailExists($data['email'], $userId)) {
                throw new Exception('البريد الإلكتروني مستخدم من قبل مستخدم آخر');
            }
            
            // التحقق من عدم استخدام اسم المستخدم من قبل مستخدم آخر
            if (isset($data['username']) && $this->usernameExists($data['username'], $userId)) {
                throw new Exception('اسم المستخدم مستخدم من قبل مستخدم آخر');
            }
            
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE users SET ";
            $params = [];
            $updates = [];
            
            $allowedFields = ['first_name', 'last_name', 'username', 'email', 'phone', 
                            'date_of_birth', 'gender', 'updated_at'];
            
            foreach ($data as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $updates[] = "$field = :$field";
                    $params[":$field"] = $value;
                }
            }
            
            if (empty($updates)) {
                return false;
            }
            
            $sql .= implode(', ', $updates);
            $sql .= " WHERE id = :user_id";
            $params[':user_id'] = $userId;
            
            return $this->db->update($sql, $params);
            
        } catch (Exception $e) {
            error_log("User Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تغيير كلمة المرور
     * @param int $userId معرف المستخدم
     * @param string $currentPassword كلمة المرور الحالية
     * @param string $newPassword كلمة المرور الجديدة
     * @return bool
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // التحقق من كلمة المرور الحالية
            $user = $this->getById($userId);
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                throw new Exception('كلمة المرور الحالية غير صحيحة');
            }
            
            // التحقق من صحة كلمة المرور الجديدة
            if (!$this->validatePassword($newPassword)) {
                return false;
            }
            
            // تشفير كلمة المرور الجديدة
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            $sql = "UPDATE users SET password = :password, updated_at = :updated_at 
                    WHERE id = :user_id";
            
            $params = [
                ':password' => $hashedPassword,
                ':updated_at' => date('Y-m-d H:i:s'),
                ':user_id' => $userId
            ];
            
            return $this->db->update($sql, $params);
            
        } catch (Exception $e) {
            error_log("Password Change Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على بيانات المستخدم بالمعرف
     * @param int $userId معرف المستخدم
     * @return array|false
     */
    public function getById($userId) {
        $sql = "SELECT id, username, email, password_hash as password, first_name, last_name, 
                phone, date_of_birth, gender, profile_image, user_type, is_active as status, 
                is_verified, verification_token, reset_token, reset_token_expires, last_activity,
                email_verified, email_verification_token, password_reset_token,
                password_reset_expires, last_login, login_attempts, locked_until,
                created_at, updated_at FROM users WHERE id = :user_id";
        return $this->db->selectOne($sql, [':user_id' => $userId]);
    }

    /**
     * الحصول على بيانات المستخدم بالبريد الإلكتروني
     * @param string $email البريد الإلكتروني
     * @return array|false
     */
    public function getByEmail($email) {
        $sql = "SELECT id, username, email, password_hash as password, first_name, last_name, 
                phone, date_of_birth, gender, profile_image, user_type, is_active, is_active as status, 
                is_verified, verification_token, reset_token, reset_token_expires, last_activity,
                email_verified, email_verification_token, password_reset_token,
                password_reset_expires, last_login, login_attempts, locked_until,
                created_at, updated_at FROM users WHERE email = :email";
        return $this->db->selectOne($sql, [':email' => $email]);
    }
    
    /**
     * الحصول على بيانات المستخدم باسم المستخدم
     * @param string $username اسم المستخدم
     * @return array|false
     */
    public function getByUsername($username) {
        $sql = "SELECT id, username, email, password_hash as password, first_name, last_name, 
                phone, date_of_birth, gender, profile_image, user_type, is_active, is_active as status, 
                is_verified, verification_token, reset_token, reset_token_expires, last_activity, 
                created_at, updated_at FROM users WHERE username = :username";
        return $this->db->selectOne($sql, [':username' => $username]);
    }
    
    /**
     * البحث عن المستخدمين
     * @param array $filters معايير البحث
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function search($filters = [], $limit = 20, $offset = 0) {
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];
        
        if (isset($filters['status'])) {
            $sql .= " AND is_active = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (isset($filters['user_type'])) {
            $sql .= " AND user_type = :user_type";
            $params[':user_type'] = $filters['user_type'];
        }
        
        if (isset($filters['search'])) {
            $sql .= " AND (first_name LIKE :search OR last_name LIKE :search OR email LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        return $this->db->select($sql, $params);
    }
    
    /**
     * تحديث حالة المستخدم
     * @param int $userId معرف المستخدم
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateStatus($userId, $status) {
        $validStatuses = [self::STATUS_ACTIVE, self::STATUS_INACTIVE, 
                         self::STATUS_SUSPENDED, self::STATUS_PENDING];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }
        
        $sql = "UPDATE users SET is_active = :status, updated_at = :updated_at 
                WHERE id = :user_id";
        
        $params = [
            ':status' => $status,
            ':updated_at' => date('Y-m-d H:i:s'),
            ':user_id' => $userId
        ];
        
        return $this->db->update($sql, $params);
    }
    
    /**
     * حذف المستخدم
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function delete($userId) {
        // حذف منطقي - تحديث الحالة بدلاً من الحذف الفعلي
        return $this->updateStatus($userId, self::STATUS_INACTIVE);
    }
    
    /**
     * التحقق من وجود المستخدم
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function exists($userId) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE id = :user_id";
        $result = $this->db->selectOne($sql, [':user_id' => $userId]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود البريد الإلكتروني
     * @param string $email البريد الإلكتروني
     * @param int $excludeUserId معرف المستخدم المستثنى (للتحديث)
     * @return bool
     */
    public function emailExists($email, $excludeUserId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE email = :email";
        $params = [':email' => $email];
        
        if ($excludeUserId) {
            $sql .= " AND id != :exclude_user_id";
            $params[':exclude_user_id'] = $excludeUserId;
        }
        
        $result = $this->db->selectOne($sql, $params);
        return $result && $result['count'] > 0;
    }
    
    /**
     * التحقق من وجود اسم المستخدم
     * @param string $username اسم المستخدم
     * @param int $excludeUserId معرف المستخدم المستثنى (للتحديث)
     * @return bool
     */
    public function usernameExists($username, $excludeUserId = null) {
        $sql = "SELECT COUNT(*) as count FROM users WHERE username = :username";
        $params = [':username' => $username];
        
        if ($excludeUserId) {
            $sql .= " AND id != :exclude_user_id";
            $params[':exclude_user_id'] = $excludeUserId;
        }
        
        $result = $this->db->selectOne($sql, $params);
        return $result && $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات المستخدمين
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // إجمالي المستخدمين
        $sql = "SELECT COUNT(*) as total FROM users";
        $result = $this->db->selectOne($sql);
        $stats['total_users'] = $result['total'];
        
        // المستخدمين النشطين
        $sql = "SELECT COUNT(*) as active FROM users WHERE is_active = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_ACTIVE]);
        $stats['active_users'] = $result['active'];
        
        // المستخدمين الجدد هذا الشهر
        $sql = "SELECT COUNT(*) as new_this_month FROM users 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        $result = $this->db->selectOne($sql);
        $stats['new_this_month'] = $result['new_this_month'];
        
        // توزيع أنواع المستخدمين
        $sql = "SELECT user_type, COUNT(*) as count FROM users 
                GROUP BY user_type";
        $result = $this->db->select($sql);
        $stats['user_types'] = $result;
        
        return $stats;
    }
    
    /**
     * التحقق من صحة بيانات المستخدم
     * @param array $data البيانات
     * @return bool
     */
    private function validateUserData($data) {
        // التحقق من الحقول المطلوبة
        $required = ['first_name', 'last_name', 'username', 'email', 'password'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return false;
            }
        }
        
        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        // التحقق من صحة كلمة المرور
        if (!$this->validatePassword($data['password'])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة بيانات التحديث
     * @param array $data البيانات
     * @return bool
     */
    private function validateUpdateData($data) {
        // التحقق من صحة البريد الإلكتروني إذا كان موجوداً
        if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من كلمة المرور مقابل بيانات المستخدم
     * @param array $user بيانات المستخدم
     * @param string $password كلمة المرور للتحقق منها
     * @return bool
     */
    public function verifyPassword($user, $password) {
        if (!$user || !isset($user['password'])) {
            return false;
        }

        return password_verify($password, $user['password']);
    }

    /**
     * التحقق من صحة كلمة المرور (قوة كلمة المرور)
     * @param string $password كلمة المرور
     * @return bool
     */
    public function validatePassword($password) {
        // كلمة المرور يجب أن تكون 8 أحرف على الأقل
        if (strlen($password) < 8) {
            return false;
        }
        
        // يجب أن تحتوي على حرف كبير وحرف صغير ورقم
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * إرسال رسالة ترحيب
     * @param string $email البريد الإلكتروني
     * @param string $firstName الاسم الأول
     */
    private function sendWelcomeEmail($email, $firstName) {
        // سيتم تنفيذ هذا لاحقاً مع نظام البريد الإلكتروني
        // TODO: إرسال رسالة ترحيب
    }
}
?> 