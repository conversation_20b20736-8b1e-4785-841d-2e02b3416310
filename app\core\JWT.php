<?php
/**
 * كلاس JWT
 * JWT Class
 * 
 * هذا الكلاس يتعامل مع إنشاء وإدارة رموز JWT
 * للمصادقة الآمنة في النظام
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class JWT {
    private $secret;
    private $algorithm;
    private $expiration;
    
    /**
     * خوارزميات التوقيع المدعومة
     */
    const ALGORITHM_HS256 = 'HS256';
    const ALGORITHM_HS384 = 'HS384';
    const ALGORITHM_HS512 = 'HS512';
    
    public function __construct($secret = null, $algorithm = self::ALGORITHM_HS256, $expiration = 3600) {
        $this->secret = $secret ?? JWT_SECRET;
        $this->algorithm = $algorithm;
        $this->expiration = $expiration;
    }
    
    /**
     * إنشاء رمز JWT
     * @param array $payload البيانات
     * @param int $expiration مدة الصلاحية (بالثواني)
     * @return string
     */
    public function encode($payload, $expiration = null) {
        try {
            // إنشاء Header
            $header = [
                'typ' => 'JWT',
                'alg' => $this->algorithm
            ];
            
            // إعداد Payload
            $payload['iat'] = time(); // وقت الإنشاء
            $payload['exp'] = time() + ($expiration ?? $this->expiration); // وقت انتهاء الصلاحية
            $payload['iss'] = 'nafsi_platform'; // المصدر
            $payload['aud'] = 'nafsi_users'; // الجمهور المستهدف
            
            // ترميز Header و Payload
            $base64Header = $this->base64UrlEncode(json_encode($header));
            $base64Payload = $this->base64UrlEncode(json_encode($payload));
            
            // إنشاء التوقيع
            $signature = $this->createSignature($base64Header . '.' . $base64Payload);
            $base64Signature = $this->base64UrlEncode($signature);
            
            // تجميع الرمز
            return $base64Header . '.' . $base64Payload . '.' . $base64Signature;
            
        } catch (Exception $e) {
            error_log("JWT Encode Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فك تشفير رمز JWT
     * @param string $token الرمز
     * @return array|false
     */
    public function decode($token) {
        try {
            // تقسيم الرمز
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                throw new Exception('تنسيق الرمز غير صحيح');
            }
            
            list($base64Header, $base64Payload, $base64Signature) = $parts;
            
            // فك تشفير Header
            $header = json_decode($this->base64UrlDecode($base64Header), true);
            if (!$header) {
                throw new Exception('Header غير صحيح');
            }
            
            // التحقق من الخوارزمية
            if ($header['alg'] !== $this->algorithm) {
                throw new Exception('خوارزمية التوقيع غير مدعومة');
            }
            
            // فك تشفير Payload
            $payload = json_decode($this->base64UrlDecode($base64Payload), true);
            if (!$payload) {
                throw new Exception('Payload غير صحيح');
            }
            
            // التحقق من التوقيع
            $signature = $this->base64UrlDecode($base64Signature);
            $expectedSignature = $this->createSignature($base64Header . '.' . $base64Payload);
            
            if (!hash_equals($signature, $expectedSignature)) {
                throw new Exception('التوقيع غير صحيح');
            }
            
            // التحقق من انتهاء الصلاحية
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                throw new Exception('الرمز منتهي الصلاحية');
            }
            
            return $payload;
            
        } catch (Exception $e) {
            error_log("JWT Decode Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من صحة الرمز
     * @param string $token الرمز
     * @return bool
     */
    public function verify($token) {
        $payload = $this->decode($token);
        return $payload !== false;
    }
    
    /**
     * تجديد الرمز
     * @param string $token الرمز الحالي
     * @param int $newExpiration مدة الصلاحية الجديدة
     * @return string|false
     */
    public function refresh($token, $newExpiration = null) {
        $payload = $this->decode($token);
        if (!$payload) {
            return false;
        }
        
        // إزالة حقول الوقت من Payload
        unset($payload['iat'], $payload['exp']);
        
        // إنشاء رمز جديد
        return $this->encode($payload, $newExpiration ?? $this->expiration);
    }
    
    /**
     * إلغاء الرمز
     * @param string $token الرمز
     * @return bool
     */
    public function revoke($token) {
        try {
            // إضافة الرمز إلى قائمة الرموز الملغاة
            $sql = "INSERT INTO revoked_tokens (token, revoked_at) VALUES (:token, NOW())";
            return $this->db->insert($sql, [':token' => $token]);
            
        } catch (Exception $e) {
            error_log("JWT Revoke Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من إلغاء الرمز
     * @param string $token الرمز
     * @return bool
     */
    public function isRevoked($token) {
        try {
            $sql = "SELECT COUNT(*) as count FROM revoked_tokens WHERE token = :token";
            $result = $this->db->selectOne($sql, [':token' => $token]);
            return $result && $result['count'] > 0;
            
        } catch (Exception $e) {
            error_log("JWT Revoke Check Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء رمز للمستخدم
     * @param int $userId معرف المستخدم
     * @param array $additionalData بيانات إضافية
     * @param int $expiration مدة الصلاحية
     * @return string
     */
    public function createUserToken($userId, $additionalData = [], $expiration = null) {
        $payload = array_merge([
            'user_id' => $userId,
            'type' => 'user_token'
        ], $additionalData);
        
        return $this->encode($payload, $expiration);
    }
    
    /**
     * إنشاء رمز للوظيفة
     * @param string $job وظيفة محددة
     * @param array $data بيانات الوظيفة
     * @param int $expiration مدة الصلاحية
     * @return string
     */
    public function createJobToken($job, $data = [], $expiration = 300) { // 5 دقائق للوظائف
        $payload = array_merge([
            'job' => $job,
            'type' => 'job_token'
        ], $data);
        
        return $this->encode($payload, $expiration);
    }
    
    /**
     * إنشاء رمز للاستخدام لمرة واحدة
     * @param string $purpose الغرض
     * @param array $data البيانات
     * @return string
     */
    public function createOneTimeToken($purpose, $data = []) {
        $payload = array_merge([
            'purpose' => $purpose,
            'type' => 'one_time_token',
            'used' => false
        ], $data);
        
        return $this->encode($payload, 3600); // ساعة واحدة
    }
    
    /**
     * التحقق من استخدام الرمز لمرة واحدة
     * @param string $token الرمز
     * @return bool
     */
    public function useOneTimeToken($token) {
        $payload = $this->decode($token);
        if (!$payload || $payload['type'] !== 'one_time_token') {
            return false;
        }
        
        if ($payload['used']) {
            return false;
        }
        
        // تحديث حالة الاستخدام
        $payload['used'] = true;
        $newToken = $this->encode($payload, 60); // دقيقة واحدة للاستخدام
        
        // حفظ الرمز المستخدم
        $this->revoke($newToken);
        
        return true;
    }
    
    /**
     * إنشاء التوقيع
     * @param string $data البيانات
     * @return string
     */
    private function createSignature($data) {
        switch ($this->algorithm) {
            case self::ALGORITHM_HS256:
                return hash_hmac('sha256', $data, $this->secret, true);
            case self::ALGORITHM_HS384:
                return hash_hmac('sha384', $data, $this->secret, true);
            case self::ALGORITHM_HS512:
                return hash_hmac('sha512', $data, $this->secret, true);
            default:
                throw new Exception('خوارزمية غير مدعومة');
        }
    }
    
    /**
     * ترميز Base64URL
     * @param string $data البيانات
     * @return string
     */
    private function base64UrlEncode($data) {
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($data));
    }
    
    /**
     * فك تشفير Base64URL
     * @param string $data البيانات
     * @return string
     */
    private function base64UrlDecode($data) {
        $data = str_replace(['-', '_'], ['+', '/'], $data);
        $remainder = strlen($data) % 4;
        
        if ($remainder) {
            $data .= str_repeat('=', 4 - $remainder);
        }
        
        return base64_decode($data);
    }
    
    /**
     * تنظيف الرموز المنتهية الصلاحية
     * @return int عدد الرموز المنظفة
     */
    public function cleanupExpiredTokens() {
        try {
            $sql = "DELETE FROM revoked_tokens WHERE revoked_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            $this->db->execute($sql);
            
            // الحصول على عدد الرموز المنظفة
            $sql = "SELECT COUNT(*) as count FROM revoked_tokens";
            $result = $this->db->selectOne($sql);
            
            return $result['count'];
            
        } catch (Exception $e) {
            error_log("JWT Cleanup Error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * الحصول على معلومات الرمز
     * @param string $token الرمز
     * @return array|false
     */
    public function getTokenInfo($token) {
        $payload = $this->decode($token);
        if (!$payload) {
            return false;
        }
        
        $info = [
            'user_id' => $payload['user_id'] ?? null,
            'type' => $payload['type'] ?? null,
            'created_at' => date('Y-m-d H:i:s', $payload['iat']),
            'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
            'is_expired' => $payload['exp'] < time(),
            'is_revoked' => $this->isRevoked($token)
        ];
        
        return $info;
    }
    
    /**
     * إنشاء رمز للوصول المحدود
     * @param int $userId معرف المستخدم
     * @param array $permissions الصلاحيات
     * @param int $expiration مدة الصلاحية
     * @return string
     */
    public function createLimitedAccessToken($userId, $permissions = [], $expiration = 1800) { // 30 دقيقة
        $payload = [
            'user_id' => $userId,
            'type' => 'limited_access_token',
            'permissions' => $permissions
        ];
        
        return $this->encode($payload, $expiration);
    }
    
    /**
     * التحقق من الصلاحيات في الرمز
     * @param string $token الرمز
     * @param string $permission الصلاحية المطلوبة
     * @return bool
     */
    public function hasPermission($token, $permission) {
        $payload = $this->decode($token);
        if (!$payload || $payload['type'] !== 'limited_access_token') {
            return false;
        }
        
        $permissions = $payload['permissions'] ?? [];
        return in_array($permission, $permissions);
    }
    
    /**
     * إنشاء رمز للاستخدام في API
     * @param int $userId معرف المستخدم
     * @param string $apiKey مفتاح API
     * @param int $expiration مدة الصلاحية
     * @return string
     */
    public function createApiToken($userId, $apiKey, $expiration = 86400) { // 24 ساعة
        $payload = [
            'user_id' => $userId,
            'api_key' => $apiKey,
            'type' => 'api_token'
        ];
        
        return $this->encode($payload, $expiration);
    }
    
    /**
     * التحقق من صحة رمز API
     * @param string $token الرمز
     * @param string $apiKey مفتاح API
     * @return bool
     */
    public function verifyApiToken($token, $apiKey) {
        $payload = $this->decode($token);
        if (!$payload || $payload['type'] !== 'api_token') {
            return false;
        }
        
        return $payload['api_key'] === $apiKey;
    }
}
?> 