#!/bin/bash

# سكريبت تثبيت قاعدة البيانات - منصة نفسي
# Database Installation Script - Nafsi Platform

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة الرسائل
print_message() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_info() {
    echo -e "${BLUE}$1${NC}"
}

# دالة التحقق من وجود MySQL
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        print_error "❌ خطأ: MySQL غير مثبت أو غير متاح في PATH"
        print_info "يرجى تثبيت MySQL أو إضافته إلى متغير PATH"
        exit 1
    fi
    print_message "✅ تم العثور على MySQL"
}

# دالة إنشاء قاعدة البيانات
create_database() {
    local db_name=$1
    print_info "جاري إنشاء قاعدة البيانات: $db_name"
    
    mysql -u "$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS \`$db_name\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_message "✅ تم إنشاء قاعدة البيانات بنجاح"
        return 0
    else
        print_error "❌ خطأ في إنشاء قاعدة البيانات"
        return 1
    fi
}

# دالة تنفيذ ملف SQL
execute_sql_file() {
    local file=$1
    local db_name=$2
    
    if [ ! -f "$file" ]; then
        print_error "❌ الملف غير موجود: $file"
        return 1
    fi
    
    print_info "جاري تنفيذ: $file"
    
    mysql -u "$DB_USER" -p"$DB_PASS" "$db_name" < "$file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_message "✅ تم تنفيذ $file بنجاح"
        return 0
    else
        print_error "❌ خطأ في تنفيذ $file"
        return 1
    fi
}

# دالة التحقق من وجود قاعدة البيانات
check_database_exists() {
    local db_name=$1
    
    mysql -u "$DB_USER" -p"$DB_PASS" -e "USE \`$db_name\`;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# دالة إنشاء نسخة احتياطية
backup_database() {
    local db_name=$1
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    print_info "جاري إنشاء النسخة الاحتياطية: $backup_file"
    
    mysqldump -u "$DB_USER" -p"$DB_PASS" "$db_name" > "$backup_file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_message "✅ تم إنشاء النسخة الاحتياطية: $backup_file"
        return 0
    else
        print_error "❌ خطأ في إنشاء النسخة الاحتياطية"
        return 1
    fi
}

# دالة التحقق من حالة قاعدة البيانات
check_database_status() {
    local db_name=$1
    
    print_info "التحقق من حالة قاعدة البيانات..."
    
    if check_database_exists "$db_name"; then
        print_message "✅ قاعدة البيانات موجودة: $db_name"
        
        print_info "الجداول الموجودة:"
        mysql -u "$DB_USER" -p"$DB_PASS" "$db_name" -e "SHOW TABLES;" 2>/dev/null
        
        if [ $? -ne 0 ]; then
            print_error "❌ خطأ في عرض الجداول"
            return 1
        fi
    else
        print_error "❌ قاعدة البيانات غير موجودة: $db_name"
        return 1
    fi
}

# دالة عرض القائمة
show_menu() {
    echo
    echo "========================================"
    echo "اختر العملية المطلوبة:"
    echo "========================================"
    echo "1. إنشاء قاعدة البيانات الأساسية"
    echo "2. إضافة الجداول الإضافية"
    echo "3. تحديث نوع المستخدم"
    echo "4. تنفيذ جميع الخطوات"
    echo "5. إنشاء نسخة احتياطية"
    echo "6. التحقق من حالة قاعدة البيانات"
    echo "0. خروج"
    echo "========================================"
}

# دالة تنفيذ جميع الخطوات
install_all() {
    print_info "بدء التثبيت الكامل..."
    
    # 1. إنشاء قاعدة البيانات
    if ! create_database "$DB_NAME"; then
        return 1
    fi
    
    # 2. تنفيذ create_database.sql
    if ! execute_sql_file "create_database.sql" "$DB_NAME"; then
        return 1
    fi
    
    # 3. تنفيذ additional_tables.sql
    if ! execute_sql_file "additional_tables.sql" "$DB_NAME"; then
        return 1
    fi
    
    # 4. تنفيذ update_user_type.sql
    if ! execute_sql_file "update_user_type.sql" "$DB_NAME"; then
        return 1
    fi
    
    print_message "✅ تم التثبيت بنجاح!"
    return 0
}

# دالة طلب كلمة المرور
get_password() {
    if [ -z "$DB_PASS" ]; then
        echo -n "كلمة مرور MySQL (اتركها فارغة إذا لم تكن موجودة): "
        read -s DB_PASS
        echo
    fi
}

# دالة عرض معلومات الاتصال
show_connection_info() {
    echo
    print_info "معلومات الاتصال:"
    echo "- المضيف: $DB_HOST"
    echo "- قاعدة البيانات: $DB_NAME"
    echo "- المستخدم: $DB_USER"
    echo
    print_info "بيانات تسجيل الدخول الافتراضية:"
    echo "- البريد الإلكتروني: <EMAIL>"
    echo "- كلمة المرور: admin123"
    echo
}

# البرنامج الرئيسي
main() {
    echo
    echo "========================================"
    echo "   تثبيت قاعدة البيانات - منصة نفسي"
    echo "   Database Installation - Nafsi Platform"
    echo "========================================"
    echo
    
    # التحقق من وجود MySQL
    check_mysql
    
    # إعدادات قاعدة البيانات
    DB_HOST="localhost"
    DB_USER="root"
    DB_PASS=""
    DB_NAME="nafsi_platform"
    
    print_info "إعدادات قاعدة البيانات:"
    echo "- المضيف: $DB_HOST"
    echo "- المستخدم: $DB_USER"
    echo "- قاعدة البيانات: $DB_NAME"
    echo
    
    # طلب كلمة المرور
    get_password
    
    # عرض القائمة
    while true; do
        show_menu
        echo -n "اختر رقم العملية: "
        read choice
        
        case $choice in
            1)
                echo
                print_info "إنشاء قاعدة البيانات الأساسية..."
                if create_database "$DB_NAME"; then
                    execute_sql_file "create_database.sql" "$DB_NAME"
                fi
                ;;
            2)
                echo
                print_info "إضافة الجداول الإضافية..."
                if check_database_exists "$DB_NAME"; then
                    execute_sql_file "additional_tables.sql" "$DB_NAME"
                else
                    print_error "❌ خطأ: قاعدة البيانات $DB_NAME غير موجودة"
                    print_info "يرجى إنشاء قاعدة البيانات أولاً"
                fi
                ;;
            3)
                echo
                print_info "تحديث نوع المستخدم..."
                if check_database_exists "$DB_NAME"; then
                    execute_sql_file "update_user_type.sql" "$DB_NAME"
                else
                    print_error "❌ خطأ: قاعدة البيانات $DB_NAME غير موجودة"
                    print_info "يرجى إنشاء قاعدة البيانات أولاً"
                fi
                ;;
            4)
                install_all
                ;;
            5)
                echo
                print_info "إنشاء نسخة احتياطية..."
                if check_database_exists "$DB_NAME"; then
                    backup_database "$DB_NAME"
                else
                    print_error "❌ خطأ: قاعدة البيانات $DB_NAME غير موجودة"
                fi
                ;;
            6)
                check_database_status "$DB_NAME"
                ;;
            0)
                echo
                print_message "👋 شكراً لاستخدام السكريبت!"
                echo
                exit 0
                ;;
            *)
                echo
                print_error "❌ اختيار غير صحيح"
                print_info "يرجى اختيار رقم من 0 إلى 6"
                ;;
        esac
        
        echo
        echo -n "اضغط Enter للمتابعة... "
        read
    done
}

# تشغيل البرنامج الرئيسي
main 