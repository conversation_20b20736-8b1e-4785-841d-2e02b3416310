<?php
/**
 * ملف لإضافة معالجين إلى قاعدة البيانات
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>إضافة معالجين إلى قاعدة البيانات</h2>";
    
    // فحص المعالجين الموجودين
    echo "<h3>المعالجين الموجودين حالياً:</h3>";
            $existingTherapists = $db->select("
        SELECT id, username, email, first_name, last_name, user_type, specialization
        FROM users 
        WHERE user_type = 'specialist'
        ORDER BY first_name, last_name
    ");
    
    if ($existingTherapists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين الموجودين</p>";
    } elseif (empty($existingTherapists)) {
        echo "<p style='color: orange;'>لا يوجد معالجين حالياً</p>";
    } else {
        echo "<p>عدد المعالجين الموجودين: " . count($existingTherapists) . "</p>";
        foreach ($existingTherapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ")<br>";
        }
    }
    
    // إضافة معالجين جدد
    echo "<h3>إضافة معالجين جدد:</h3>";
    
    $therapists = [
        [
            'username' => 'dr.ahmed',
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'first_name' => 'د. أحمد',
            'last_name' => 'محمد',
            'phone' => '+966501234567',
            'user_type' => 'therapist',
            'specialization' => 'علم النفس السريري',
            'license_number' => 'PSY-2023-001',
            'experience_years' => 8,
            'hourly_rate' => 200.00,
            'bio' => 'معالج نفسي متخصص في العلاج المعرفي السلوكي مع خبرة 8 سنوات في مجال الصحة النفسية.'
        ],
        [
            'username' => 'dr.fatima',
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'first_name' => 'د. فاطمة',
            'last_name' => 'علي',
            'phone' => '+966502345678',
            'user_type' => 'therapist',
            'specialization' => 'العلاج الأسري',
            'license_number' => 'PSY-2023-002',
            'experience_years' => 6,
            'hourly_rate' => 180.00,
            'bio' => 'معالجة نفسية متخصصة في العلاج الأسري والعلاقات الزوجية مع خبرة 6 سنوات.'
        ],
        [
            'username' => 'dr.omar',
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'first_name' => 'د. عمر',
            'last_name' => 'حسن',
            'phone' => '+966503456789',
            'user_type' => 'therapist',
            'specialization' => 'علاج القلق والاكتئاب',
            'license_number' => 'PSY-2023-003',
            'experience_years' => 5,
            'hourly_rate' => 160.00,
            'bio' => 'معالج نفسي متخصص في علاج القلق والاكتئاب والاضطرابات النفسية مع خبرة 5 سنوات.'
        ],
        [
            'username' => 'dr.layla',
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'first_name' => 'د. ليلى',
            'last_name' => 'أحمد',
            'phone' => '+966504567890',
            'user_type' => 'therapist',
            'specialization' => 'علاج الأطفال والمراهقين',
            'license_number' => 'PSY-2023-004',
            'experience_years' => 7,
            'hourly_rate' => 190.00,
            'bio' => 'معالجة نفسية متخصصة في علاج الأطفال والمراهقين والاضطرابات السلوكية مع خبرة 7 سنوات.'
        ],
        [
            'username' => 'dr.youssef',
            'email' => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'first_name' => 'د. يوسف',
            'last_name' => 'محمد',
            'phone' => '+966505678901',
            'user_type' => 'therapist',
            'specialization' => 'العلاج النفسي الديناميكي',
            'license_number' => 'PSY-2023-005',
            'experience_years' => 10,
            'hourly_rate' => 220.00,
            'bio' => 'معالج نفسي متخصص في العلاج النفسي الديناميكي والتحليل النفسي مع خبرة 10 سنوات.'
        ]
    ];
    
    $addedCount = 0;
    foreach ($therapists as $therapist) {
        // التحقق من عدم وجود المستخدم
        $existing = $db->select("SELECT id FROM users WHERE username = ? OR email = ?", 
            [$therapist['username'], $therapist['email']]);
        
        if (empty($existing)) {
            $result = $db->insert("users", $therapist);
            if ($result) {
                echo "<p style='color: green;'>✓ تم إضافة المعالج: " . $therapist['first_name'] . ' ' . $therapist['last_name'] . "</p>";
                $addedCount++;
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة المعالج: " . $therapist['first_name'] . ' ' . $therapist['last_name'] . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ المعالج موجود مسبقاً: " . $therapist['first_name'] . ' ' . $therapist['last_name'] . "</p>";
        }
    }
    
    echo "<h3>نتائج الإضافة:</h3>";
    echo "<p style='color: green;'>تم إضافة $addedCount معالج جديد</p>";
    
    // عرض المعالجين بعد الإضافة
    echo "<h3>المعالجين بعد الإضافة:</h3>";
            $allTherapists = $db->select("
        SELECT id, username, email, first_name, last_name, user_type, specialization
        FROM users 
        WHERE user_type = 'specialist'
        ORDER BY first_name, last_name
    ");
    
    if ($allTherapists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
    } else {
        echo "<p>إجمالي المعالجين: " . count($allTherapists) . "</p>";
        foreach ($allTherapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ") - " . ($therapist['specialization'] ?? 'بدون تخصص') . "<br>";
        }
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إضافة المعالجين بنجاح!</p>";
    echo "<p>يمكنك الآن الوصول إلى صفحة إضافة المواعيد وستجد المعالجين متاحين.</p>";
    echo "<p><a href='/nafsi_platform/admin/add_appointment' style='color: blue;'>اذهب إلى صفحة إضافة المواعيد</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 