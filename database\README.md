# قاعدة بيانات منصة نفسي - Nafsi Platform Database

## نظرة عامة

هذا المجلد يحتوي على جميع ملفات قاعدة البيانات لمنصة نفسي، بما في ذلك مخطط قاعدة البيانات والجداول والبيانات الأولية.

## الملفات المتوفرة

### 1. `create_database.sql`
الملف الرئيسي لإنشاء قاعدة البيانات. يحتوي على:
- إنشاء قاعدة البيانات `nafsi_platform`
- جميع الجداول الأساسية
- الفهارس لتحسين الأداء
- البيانات الأولية (التصنيفات والمشرف الافتراضي)

### 2. `additional_tables.sql`
جداول إضافية للميزات المتقدمة:
- جدول المحفظة الإلكترونية
- جدول الاشتراكات
- جدول العروض والخصومات
- جدول التقارير الطبية
- جدول الملفات المرفوعة
- جدول الإحصائيات
- جدول إعدادات النظام

## كيفية التثبيت

### الخطوة 1: إنشاء قاعدة البيانات الأساسية
```bash
# باستخدام سطر الأوامر
mysql -u root -p < database/create_database.sql

# أو باستخدام phpMyAdmin
# 1. افتح phpMyAdmin
# 2. اذهب إلى تبويب Import
# 3. اختر ملف create_database.sql
# 4. اضغط Go
```

### الخطوة 2: إضافة الجداول الإضافية (اختياري)
```bash
# إذا كنت تريد الميزات المتقدمة
mysql -u root -p nafsi_platform < database/additional_tables.sql
```

## الجداول الأساسية

### 1. جدول المستخدمين (`users`)
```sql
- id: معرف فريد
- username: اسم المستخدم
- email: البريد الإلكتروني
- password_hash: كلمة المرور المشفرة
- first_name, last_name: الاسم الأول والأخير
- phone: رقم الهاتف
- date_of_birth: تاريخ الميلاد
- gender: الجنس
- user_type: نوع المستخدم (regular, specialist, admin)
- is_active: حالة الحساب
- is_verified: التحقق من البريد الإلكتروني
```

### 2. جدول الأخصائيين (`specialists`)
```sql
- user_id: معرف المستخدم المرتبط
- license_number: رقم الترخيص
- specialization: التخصص
- experience_years: سنوات الخبرة
- hourly_rate: السعر بالساعة
- is_approved: حالة الموافقة
- average_rating: متوسط التقييم
```

### 3. جدول الحجوزات (`bookings`)
```sql
- user_id: معرف المستخدم
- specialist_id: معرف الأخصائي
- session_date: تاريخ الجلسة
- session_time: وقت الجلسة
- duration: مدة الجلسة بالدقائق
- status: حالة الحجز
- session_type: نوع الجلسة (video, audio, chat)
- price: السعر
- payment_status: حالة الدفع
```

### 4. جدول الجلسات (`sessions`)
```sql
- booking_id: معرف الحجز المرتبط
- start_time: وقت بداية الجلسة
- end_time: وقت نهاية الجلسة
- status: حالة الجلسة
- session_url: رابط الجلسة
- session_key: مفتاح الجلسة
```

### 5. جدول الرسائل (`messages`)
```sql
- session_id: معرف الجلسة
- sender_id: معرف المرسل
- sender_type: نوع المرسل (user, specialist)
- message_type: نوع الرسالة (text, image, file, system)
- content: محتوى الرسالة
- is_read: حالة القراءة
```

## العلاقات بين الجداول

### العلاقات الرئيسية:
1. **users** ↔ **specialists** (1:1)
2. **users** ↔ **admins** (1:1)
3. **users** ↔ **bookings** (1:N)
4. **specialists** ↔ **bookings** (1:N)
5. **bookings** ↔ **sessions** (1:1)
6. **sessions** ↔ **messages** (1:N)
7. **sessions** ↔ **ratings** (1:1)

### العلاقات الإضافية:
8. **content** ↔ **categories** (N:1)
9. **users** ↔ **favorites** (1:N)
10. **content** ↔ **favorites** (1:N)
11. **users** ↔ **notifications** (1:N)

## الفهارس المهمة

### فهارس الأداء:
```sql
- idx_users_email: للبحث السريع بالبريد الإلكتروني
- idx_users_username: للبحث السريع باسم المستخدم
- idx_bookings_user_id: لحجوزات المستخدم
- idx_bookings_specialist_id: لحجوزات الأخصائي
- idx_bookings_session_date: للبحث بالتاريخ
- idx_messages_session_id: لرسائل الجلسة
- idx_content_status: للمحتوى المنشور
```

### فهارس البحث النصي:
```sql
- idx_content_search: للبحث في المحتوى
- idx_specialists_search: للبحث في الأخصائيين
```

## البيانات الأولية

### التصنيفات الأساسية:
1. الصحة النفسية العامة
2. الاكتئاب والقلق
3. العلاقات والأسرة
4. التنمية الذاتية
5. الأطفال والمراهقين

### المستخدم المشرف الافتراضي:
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`
- الدور: `super_admin`

## الأمان

### تشفير كلمات المرور:
```php
// تشفير كلمة المرور
$password_hash = password_hash($password, PASSWORD_DEFAULT);

// التحقق من كلمة المرور
if (password_verify($password, $user['password_hash'])) {
    // كلمة المرور صحيحة
}
```

### حماية البيانات:
- جميع كلمات المرور مشفرة
- استخدام Prepared Statements لمنع SQL Injection
- التحقق من صحة المدخلات
- تشفير البيانات الحساسة

## الأداء

### تحسين الاستعلامات:
- استخدام الفهارس المناسبة
- تجنب SELECT *
- استخدام LIMIT للنتائج الكبيرة
- تحسين JOIN queries

### إعدادات MySQL الموصى بها:
```sql
-- زيادة حجم الذاكرة المؤقتة
SET GLOBAL innodb_buffer_pool_size = 1G;

-- تحسين أداء الفهارس
SET GLOBAL innodb_file_per_table = 1;

-- تحسين أداء النصوص
SET GLOBAL innodb_file_format = Barracuda;
```

## النسخ الاحتياطي

### إنشاء نسخة احتياطية:
```bash
# نسخة احتياطية كاملة
mysqldump -u root -p nafsi_platform > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخة احتياطية للجداول المهمة فقط
mysqldump -u root -p nafsi_platform users specialists bookings sessions > important_tables_backup.sql
```

### استعادة النسخة الاحتياطية:
```bash
mysql -u root -p nafsi_platform < backup_file.sql
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات `app/config.php`
   - تأكد من تشغيل MySQL
   - تحقق من اسم المستخدم وكلمة المرور

2. **خطأ في أسماء الأعمدة**
   - تأكد من تطابق أسماء الأعمدة في الكود وقاعدة البيانات
   - راجع ملف `DATABASE_COLUMNS_FIX.md`

3. **مشاكل في الأداء**
   - تحقق من وجود الفهارس المطلوبة
   - راجع استعلامات قاعدة البيانات
   - تحقق من إعدادات MySQL

## التطوير المستقبلي

### ميزات مقترحة:
1. نظام المحفظة الإلكترونية
2. نظام الاشتراكات
3. نظام العروض والخصومات
4. التقارير الطبية
5. نظام الملفات المرفوعة
6. نظام الإحصائيات المتقدم

### جداول إضافية مطلوبة:
- جدول المدفوعات المتقدم
- جدول الإشعارات المتقدم
- جدول سجل الأمان
- جدول النسخ الاحتياطي

## الدعم

إذا واجهت أي مشاكل في قاعدة البيانات:
1. راجع ملفات السجلات في مجلد `logs/`
2. تحقق من إعدادات MySQL
3. راجع ملفات التوثيق في مجلد `docs/`
4. اتصل بفريق التطوير

---

**ملاحظة**: تأكد من تحديث قاعدة البيانات عند إضافة ميزات جديدة أو تعديل الجداول الموجودة. 