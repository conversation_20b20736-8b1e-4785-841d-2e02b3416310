<?php
/**
 * معالج إنشاء الحساب
 * Register Handler
 * 
 * هذا الملف يتعامل مع معالجة طلبات إنشاء الحسابات الجديدة
 * بما في ذلك التحقق من البيانات، تشفير كلمة المرور،
 * وإرسال رسالة ترحيب
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// التحقق من نوع الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die('Method Not Allowed');
}

// تضمين الملفات المطلوبة
require_once APP_ROOT . '/core/User.php';
require_once APP_ROOT . '/core/Auth.php';
require_once APP_ROOT . '/core/Session.php';

try {
    // تهيئة الكلاسات
    $user = new User();
    $auth = new Auth();
    
    // الحصول على البيانات المرسلة
    $firstName = trim($_POST['firstName'] ?? '');
    $lastName = trim($_POST['lastName'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirmPassword'] ?? '';
    $gender = $_POST['gender'] ?? '';
    $dateOfBirth = $_POST['dateOfBirth'] ?? '';
    $terms = isset($_POST['terms']) && $_POST['terms'] === 'on';
    $privacy = isset($_POST['privacy']) && $_POST['privacy'] === 'on';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrfToken)) {
        throw new Exception('رمز الأمان غير صحيح');
    }
    
    // التحقق من صحة البيانات الأساسية
    if (empty($firstName) || empty($lastName) || empty($username) || empty($email) || empty($password)) {
        throw new Exception('جميع الحقول المطلوبة يجب ملؤها');
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('صيغة البريد الإلكتروني غير صحيحة');
    }
    
    // التحقق من تطابق كلمات المرور
    if ($password !== $confirmPassword) {
        throw new Exception('كلمات المرور غير متطابقة');
    }
    
    // التحقق من قوة كلمة المرور
    if (!$user->validatePassword($password)) {
        throw new Exception('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، ورقم');
    }
    
    // التحقق من الموافقة على الشروط
    if (!$terms || !$privacy) {
        throw new Exception('يجب الموافقة على الشروط والأحكام وسياسة الخصوصية');
    }
    
    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    if ($user->emailExists($email)) {
        throw new Exception('البريد الإلكتروني مستخدم مسبقاً');
    }
    
    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if ($user->usernameExists($username)) {
        throw new Exception('اسم المستخدم مستخدم مسبقاً');
    }
    
    // إنشاء الحساب
    $userData = [
        'first_name' => $firstName,
        'last_name' => $lastName,
        'username' => $username,
        'email' => $email,
        'phone' => $phone,
        'password' => $password,
        'gender' => $gender,
        'date_of_birth' => $dateOfBirth,
        'user_type' => 'user',
        'status' => 'active'
    ];
    
    $userId = $user->create($userData);
    
    if ($userId) {
        // إرسال رسالة ترحيب
        // TODO: إرسال رسالة ترحيب عبر البريد الإلكتروني
        
        // تسجيل الدخول تلقائياً
        $loginResult = $auth->login($email, $password, false);
        
        if ($loginResult['status'] !== 'success') {
            throw new Exception('تم إنشاء الحساب بنجاح ولكن فشل في تسجيل الدخول التلقائي');
        }
        
        // إرسال استجابة JSON إذا كان الطلب AJAX
        if (isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم إنشاء الحساب بنجاح',
                'redirect' => '/dashboard'
            ]);
            exit;
        }
        
        // إعادة التوجيه للوحة التحكم
        setAlert('تم إنشاء الحساب بنجاح! مرحباً بك في منصة نفسي', 'success');
        header('Location: ' . getAppUrl('dashboard'));
        exit;
        
    } else {
        throw new Exception('فشل في إنشاء الحساب');
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
    
    // إرسال استجابة JSON إذا كان الطلب AJAX
    if (isAjaxRequest()) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $error
        ]);
        exit;
    }
    
    // حفظ الخطأ في الجلسة وإعادة التوجيه
    $_SESSION['register_error'] = $error;
    $_SESSION['register_data'] = $_POST;
    
    // إعادة التوجيه لصفحة التسجيل
    header('Location: ' . getAppUrl('register'));
    exit;
}

/**
 * التحقق من رمز CSRF
 * @param string $token الرمز
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * التحقق من أن الطلب AJAX
 * @return bool
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}
?> 