<?php
/**
 * ملف التكوين الأساسي لمنصة نفسي
 * Nafsi Platform Configuration File
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

// إعدادات قاعدة البيانات
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', 'nafsi_platform');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');
if (!defined('DB_CHARSET')) define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق
if (!defined('APP_NAME')) define('APP_NAME', 'منصة نفسي');
if (!defined('APP_VERSION')) define('APP_VERSION', '1.0.0');
if (!defined('APP_URL')) define('APP_URL', 'http://localhost/nafsi_platform');
if (!defined('APP_TIMEZONE')) define('APP_TIMEZONE', 'Asia/Riyadh');
if (!defined('APP_ROOT')) {
    define('APP_ROOT', __DIR__);
}

// إعدادات الأمان
if (!defined('JWT_SECRET')) define('JWT_SECRET', 'nafsi_platform_secret_key_2024');
if (!defined('SESSION_LIFETIME')) define('SESSION_LIFETIME', 3600); // ساعة واحدة
if (!defined('PASSWORD_COST')) define('PASSWORD_COST', 12); // تكلفة تشفير كلمة المرور

// إعدادات البريد الإلكتروني
if (!defined('SMTP_HOST')) define('SMTP_HOST', 'smtp.gmail.com');
if (!defined('SMTP_PORT')) define('SMTP_PORT', 587);
if (!defined('SMTP_USER')) define('SMTP_USER', '<EMAIL>');
if (!defined('SMTP_PASS')) define('SMTP_PASS', 'your-app-password');
if (!defined('SMTP_FROM')) define('SMTP_FROM', '<EMAIL>');
if (!defined('SMTP_FROM_NAME')) define('SMTP_FROM_NAME', 'منصة نفسي');

// إعدادات الملفات
if (!defined('UPLOAD_PATH')) define('UPLOAD_PATH', __DIR__ . '/../uploads/');
if (!defined('MAX_FILE_SIZE')) define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
if (!defined('ALLOWED_EXTENSIONS')) define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']);

// إعدادات التطوير
if (!defined('DEBUG_MODE')) define('DEBUG_MODE', true);
if (!defined('LOG_ERRORS')) define('LOG_ERRORS', true);
if (!defined('LOG_PATH')) define('LOG_PATH', __DIR__ . '/../logs/');

// إعدادات الجلسة (سيتم تطبيقها في init.php)
// ini_set('session.cookie_httponly', 1);
// ini_set('session.cookie_secure', 0); // تغيير إلى 1 في الإنتاج
// ini_set('session.use_strict_mode', 1);
// ini_set('session.cookie_samesite', 'Strict');

// تعيين المنطقة الزمنية
date_default_timezone_set(APP_TIMEZONE);

// إعدادات الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// دوال مساعدة للتكوين
function getConfig($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

function isDebugMode() {
    return DEBUG_MODE;
}

function getAppUrl($path = '') {
    $baseUrl = rtrim(APP_URL, '/');
    $cleanPath = ltrim($path, '/');
    
    // إذا كان المسار فارغاً، إرجاع الرابط الأساسي
    if (empty($cleanPath)) {
        return $baseUrl;
    }
    
    return $baseUrl . '/' . $cleanPath;
}

function getUploadUrl($file = '') {
    return getAppUrl('uploads/' . ltrim($file, '/'));
}

// إنشاء مجلدات مطلوبة إذا لم تكن موجودة
$requiredDirs = [
    UPLOAD_PATH,
    LOG_PATH
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
} 