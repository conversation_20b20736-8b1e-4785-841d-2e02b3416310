@echo off
chcp 65001 >nul
title تثبيت قاعدة البيانات - منصة نفسي

echo.
echo ========================================
echo    تثبيت قاعدة البيانات - منصة نفسي
echo    Database Installation - Nafsi Platform
echo ========================================
echo.

:: التحقق من وجود MySQL
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: MySQL غير مثبت أو غير متاح في PATH
    echo يرجى تثبيت MySQL أو إضافته إلى متغير PATH
    pause
    exit /b 1
)

echo ✅ تم العثور على MySQL
echo.

:: إعدادات قاعدة البيانات
set DB_HOST=localhost
set DB_USER=root
set DB_PASS=
set DB_NAME=nafsi_platform

echo إعدادات قاعدة البيانات:
echo - المضيف: %DB_HOST%
echo - المستخدم: %DB_USER%
echo - قاعدة البيانات: %DB_NAME%
echo.

:: طلب كلمة المرور إذا لزم الأمر
if "%DB_PASS%"=="" (
    set /p DB_PASS="كلمة مرور MySQL (اتركها فارغة إذا لم تكن موجودة): "
)

echo.
echo ========================================
echo اختر العملية المطلوبة:
echo ========================================
echo 1. إنشاء قاعدة البيانات الأساسية
echo 2. إضافة الجداول الإضافية
echo 3. تحديث نوع المستخدم
echo 4. تنفيذ جميع الخطوات
echo 5. إنشاء نسخة احتياطية
echo 6. التحقق من حالة قاعدة البيانات
echo 0. خروج
echo ========================================
set /p choice="اختر رقم العملية: "

if "%choice%"=="1" goto create_basic
if "%choice%"=="2" goto add_additional
if "%choice%"=="3" goto update_user_type
if "%choice%"=="4" goto install_all
if "%choice%"=="5" goto backup_database
if "%choice%"=="6" goto check_status
if "%choice%"=="0" goto exit
goto invalid_choice

:create_basic
echo.
echo ========================================
echo إنشاء قاعدة البيانات الأساسية...
echo ========================================
echo.

:: إنشاء قاعدة البيانات
echo جاري إنشاء قاعدة البيانات...
mysql -u %DB_USER% -p%DB_PASS% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء قاعدة البيانات
    pause
    exit /b 1
)
echo ✅ تم إنشاء قاعدة البيانات بنجاح

:: تنفيذ ملف create_database.sql
echo جاري تنفيذ create_database.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < create_database.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ create_database.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ create_database.sql بنجاح
goto success

:add_additional
echo.
echo ========================================
echo إضافة الجداول الإضافية...
echo ========================================
echo.

:: التحقق من وجود قاعدة البيانات
mysql -u %DB_USER% -p%DB_PASS% -e "USE %DB_NAME%;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: قاعدة البيانات %DB_NAME% غير موجودة
    echo يرجى إنشاء قاعدة البيانات أولاً
    pause
    exit /b 1
)

:: تنفيذ ملف additional_tables.sql
echo جاري تنفيذ additional_tables.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < additional_tables.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ additional_tables.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ additional_tables.sql بنجاح
goto success

:update_user_type
echo.
echo ========================================
echo تحديث نوع المستخدم...
echo ========================================
echo.

:: التحقق من وجود قاعدة البيانات
mysql -u %DB_USER% -p%DB_PASS% -e "USE %DB_NAME%;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: قاعدة البيانات %DB_NAME% غير موجودة
    echo يرجى إنشاء قاعدة البيانات أولاً
    pause
    exit /b 1
)

:: تنفيذ ملف update_user_type.sql
echo جاري تنفيذ update_user_type.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < update_user_type.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ update_user_type.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ update_user_type.sql بنجاح
goto success

:install_all
echo.
echo ========================================
echo تنفيذ جميع الخطوات...
echo ========================================
echo.

:: 1. إنشاء قاعدة البيانات
echo 1. إنشاء قاعدة البيانات...
mysql -u %DB_USER% -p%DB_PASS% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء قاعدة البيانات
    pause
    exit /b 1
)
echo ✅ تم إنشاء قاعدة البيانات

:: 2. تنفيذ create_database.sql
echo 2. تنفيذ create_database.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < create_database.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ create_database.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ create_database.sql

:: 3. تنفيذ additional_tables.sql
echo 3. تنفيذ additional_tables.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < additional_tables.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ additional_tables.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ additional_tables.sql

:: 4. تنفيذ update_user_type.sql
echo 4. تنفيذ update_user_type.sql...
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% < update_user_type.sql
if %errorlevel% neq 0 (
    echo ❌ خطأ في تنفيذ update_user_type.sql
    pause
    exit /b 1
)
echo ✅ تم تنفيذ update_user_type.sql

echo.
echo ========================================
echo ✅ تم التثبيت بنجاح!
echo ========================================
goto success

:backup_database
echo.
echo ========================================
echo إنشاء نسخة احتياطية...
echo ========================================
echo.

:: التحقق من وجود قاعدة البيانات
mysql -u %DB_USER% -p%DB_PASS% -e "USE %DB_NAME%;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: قاعدة البيانات %DB_NAME% غير موجودة
    pause
    exit /b 1
)

:: إنشاء اسم الملف
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "backup_file=backup_%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%.sql"

echo جاري إنشاء النسخة الاحتياطية: %backup_file%
mysqldump -u %DB_USER% -p%DB_PASS% %DB_NAME% > %backup_file%
if %errorlevel% neq 0 (
    echo ❌ خطأ في إنشاء النسخة الاحتياطية
    pause
    exit /b 1
)
echo ✅ تم إنشاء النسخة الاحتياطية: %backup_file%
goto success

:check_status
echo.
echo ========================================
echo التحقق من حالة قاعدة البيانات...
echo ========================================
echo.

:: التحقق من وجود قاعدة البيانات
mysql -u %DB_USER% -p%DB_PASS% -e "SHOW DATABASES LIKE '%DB_NAME%';" | findstr %DB_NAME% >nul
if %errorlevel% neq 0 (
    echo ❌ قاعدة البيانات %DB_NAME% غير موجودة
    pause
    exit /b 1
)
echo ✅ قاعدة البيانات موجودة: %DB_NAME%

:: عرض الجداول
echo.
echo الجداول الموجودة:
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SHOW TABLES;" 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ في عرض الجداول
    pause
    exit /b 1
)
goto success

:invalid_choice
echo.
echo ❌ اختيار غير صحيح
echo يرجى اختيار رقم من 0 إلى 6
goto end

:success
echo.
echo ========================================
echo ✅ تم تنفيذ العملية بنجاح!
echo ========================================
echo.
echo معلومات الاتصال:
echo - المضيف: %DB_HOST%
echo - قاعدة البيانات: %DB_NAME%
echo - المستخدم: %DB_USER%
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo - البريد الإلكتروني: <EMAIL>
echo - كلمة المرور: admin123
echo.

:end
echo اضغط أي مفتاح للخروج...
pause >nul

:exit
echo.
echo 👋 شكراً لاستخدام السكريبت!
echo. 