-- تحديث مباشر للمستخدمين الموجودين
-- Direct update for existing users

-- 1. تحديث العملاء
UPDATE users SET user_type = 'regular' WHERE username IN (
    'mariam', 'ali', 'nada', 'mohammed', 'reem', 
    'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user'
);

-- 2. تحديث المعالجين الموجودين
UPDATE users SET user_type = 'specialist' WHERE username LIKE 'dr.%';

-- 3. عرض النتائج
SELECT 
    id,
    username,
    email,
    first_name,
    last_name,
    user_type
FROM users 
WHERE user_type IN ('regular', 'specialist')
ORDER BY user_type, first_name, last_name; 