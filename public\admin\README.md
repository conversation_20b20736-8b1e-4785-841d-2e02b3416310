# لوحة تحكم المشرف - منصة نفسي

## نظرة عامة

لوحة تحكم المشرف هي الواجهة الإدارية لمنصة نفسي، تتيح للمشرفين إدارة جميع جوانب المنصة بما في ذلك المستخدمين، الأخصائيين، المحتوى، والتقارير.

## الملفات المطلوبة

### الصفحات الرئيسية
- `index.php` - لوحة التحكم الرئيسية
- `users.php` - إدارة المستخدمين
- `specialists.php` - إدارة الأخصائيين
- `content.php` - إدارة المحتوى
- `reports.php` - التقارير
- `settings.php` - الإعدادات

### ملفات الأصول
- `assets/css/admin.css` - أنماط لوحة التحكم

## الميزات المتاحة

### 1. لوحة التحكم الرئيسية (`index.php`)
- عرض الإحصائيات السريعة
- النشاطات الأخيرة
- الإجراءات السريعة
- الرسوم البيانية (قريباً)

### 2. إدارة المستخدمين (`users.php`)
- عرض قائمة المستخدمين
- البحث والتصفية
- تحديث حالة المستخدم
- حذف المستخدمين
- الإجراءات الجماعية

### 3. إدارة الأخصائيين (`specialists.php`)
- عرض قائمة الأخصائيين
- اعتماد/رفض الأخصائيين
- التحقق من الشهادات
- إدارة التقييمات
- الإجراءات الجماعية

### 4. إدارة المحتوى (`content.php`)
- عرض المقالات والفيديوهات
- نشر/إلغاء نشر المحتوى
- إدارة التعليقات
- تصفية المحتوى
- الإجراءات الجماعية

### 5. التقارير (`reports.php`)
- إنشاء تقارير مختلفة
- تصدير البيانات
- إحصائيات النظام
- التقارير المحفوظة

### 6. الإعدادات (`settings.php`)
- الإعدادات العامة
- إعدادات الأمان
- إعدادات البريد الإلكتروني
- إعدادات الإشعارات
- أدوات الصيانة

## متطلبات النظام

### المتطلبات التقنية
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- متصفح حديث يدعم JavaScript

### المتطلبات الأمنية
- تسجيل دخول المشرف
- صلاحيات المشرف
- حماية من CSRF
- تشفير البيانات الحساسة

## طريقة الاستخدام

### 1. الوصول للوحة التحكم
```
http://your-domain.com/public/admin/
```

### 2. تسجيل الدخول
- استخدم بيانات المشرف المسجلة
- تأكد من أن نوع المستخدم هو "admin"

### 3. التنقل
- استخدم الشريط الجانبي للتنقل بين الصفحات
- كل صفحة تحتوي على وظائف محددة

### 4. الإجراءات الأساسية
- **عرض**: انقر على أيقونة العين لعرض التفاصيل
- **تعديل**: انقر على أيقونة القلم للتعديل
- **حذف**: انقر على أيقونة السلة للحذف
- **تفعيل/تعطيل**: استخدم الأزرار المناسبة

## الأمان

### حماية الوصول
- التحقق من تسجيل الدخول
- التحقق من صلاحيات المشرف
- حماية من الوصول المباشر للملفات

### حماية البيانات
- تشفير كلمات المرور
- حماية من SQL Injection
- تنظيف المدخلات
- حماية من XSS

### النسخ الاحتياطية
- إنشاء نسخ احتياطية دورية
- حفظ النسخ في مكان آمن
- اختبار استعادة النسخ

## التخصيص

### الألوان والتصميم
يمكن تخصيص الألوان والتصميم من خلال تعديل ملف `assets/css/admin.css`:

```css
:root {
    --admin-primary: #2563eb;
    --admin-secondary: #64748b;
    --admin-success: #059669;
    --admin-warning: #d97706;
    --admin-danger: #dc2626;
}
```

### إضافة ميزات جديدة
1. أنشئ صفحة جديدة في مجلد `admin/`
2. أضف الرابط في الشريط الجانبي
3. اتبع نفس نمط التصميم والوظائف

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. عدم الوصول للوحة التحكم
- تأكد من تسجيل الدخول
- تأكد من صلاحيات المشرف
- تحقق من إعدادات الجلسة

#### 2. عدم ظهور البيانات
- تحقق من اتصال قاعدة البيانات
- تأكد من وجود البيانات
- تحقق من الأخطاء في السجلات

#### 3. مشاكل في التصميم
- تأكد من تحميل ملفات CSS
- تحقق من دعم المتصفح
- امسح الكاش

### سجلات الأخطاء
- راجع ملفات السجلات في `logs/`
- تحقق من إعدادات PHP
- راجع سجلات الخادم

## الدعم

### المساعدة
- راجع هذا الملف أولاً
- تحقق من الوثائق التقنية
- اتصل بفريق التطوير

### التحديثات
- احتفظ بنسخة احتياطية قبل التحديث
- اتبع تعليمات التحديث
- اختبر النظام بعد التحديث

## التطوير المستقبلي

### الميزات المخطط لها
- [ ] رسوم بيانية تفاعلية
- [ ] إشعارات في الوقت الفعلي
- [ ] تطبيق جوال للمشرفين
- [ ] نظام تقارير متقدم
- [ ] إدارة متقدمة للمحتوى

### التحسينات
- [ ] تحسين الأداء
- [ ] تحسين الأمان
- [ ] تحسين تجربة المستخدم
- [ ] دعم متعدد اللغات

---

**تم إنشاء هذا الملف بواسطة فريق منصة نفسي**  
**التاريخ**: يوليو 2024  
**الإصدار**: 1.0.0 