<?php
/**
 * ملف تشخيصي لفحص مشكلة إضافة الموعد
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>تشخيص مشكلة إضافة الموعد</h2>";
    
    // فحص 1: وجود جدول appointments
    echo "<h3>1. فحص جدول appointments:</h3>";
    $tables = $db->select("SHOW TABLES LIKE 'appointments'");
    if ($tables === false) {
        echo "<p style='color: red;'>خطأ في فحص الجداول</p>";
    } elseif (empty($tables)) {
        echo "<p style='color: red;'>جدول appointments غير موجود</p>";
    } else {
        echo "<p style='color: green;'>جدول appointments موجود</p>";
    }
    
    // فحص 2: هيكل جدول appointments
    echo "<h3>2. هيكل جدول appointments:</h3>";
    $columns = $db->select("DESCRIBE appointments");
    if ($columns === false) {
        echo "<p style='color: red;'>خطأ في فحص هيكل الجدول</p>";
    } else {
        echo "<p>أعمدة جدول appointments:</p>";
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        }
    }
    
    // فحص 3: اختبار إدراج موعد
    echo "<h3>3. اختبار إدراج موعد:</h3>";
    $testData = [
        'client_id' => 1,
        'therapist_id' => 1,
        'appointment_date' => '2024-12-25',
        'appointment_time' => '10:00',
        'status' => 'confirmed',
        'notes' => 'موعد تجريبي',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    echo "<p>محاولة إدراج موعد تجريبي...</p>";
    $result = $db->insert("appointments", $testData);
    
    if ($result === false) {
        echo "<p style='color: red;'>فشل في إدراج الموعد التجريبي</p>";
        // محاولة معرفة سبب الخطأ
        $error = $db->getLastError();
        if ($error) {
            echo "<p style='color: red;'>خطأ SQL: " . $error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>تم إدراج الموعد التجريبي بنجاح (ID: $result)</p>";
        
        // حذف الموعد التجريبي
        $db->delete("appointments", "id = ?", [$result]);
        echo "<p>تم حذف الموعد التجريبي</p>";
    }
    
    // فحص 4: فحص العملاء والمعالجين
    echo "<h3>4. فحص العملاء والمعالجين:</h3>";
    
    $clients = $db->select("SELECT id, first_name, last_name FROM users WHERE (user_type = 'regular' OR user_type IS NULL OR user_type = '') LIMIT 5");
    if ($clients === false) {
        echo "<p style='color: red;'>خطأ في استعلام العملاء</p>";
    } else {
        echo "<p>العملاء المتاحون:</p>";
        foreach ($clients as $client) {
            echo "- " . $client['first_name'] . ' ' . $client['last_name'] . " (ID: " . $client['id'] . ")<br>";
        }
    }
    
    $therapists = $db->select("SELECT id, first_name, last_name FROM users WHERE user_type = 'specialist' LIMIT 5");
    if ($therapists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
    } else {
        echo "<p>المعالجين المتاحون:</p>";
        foreach ($therapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (ID: " . $therapist['id'] . ")<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 