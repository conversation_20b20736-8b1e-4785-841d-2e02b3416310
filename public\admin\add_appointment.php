<?php
/**
 * إضافة موعد جديد - صفحة المدير
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/admin/add_appointment');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول وصلاحيات المدير
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

$currentUser = $auth->getCurrentUser();
if (!$currentUser || $currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// معالجة إضافة موعد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = db();
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['client_id', 'therapist_id', 'appointment_date', 'appointment_time'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("حقل $field مطلوب");
            }
        }
        
        // التحقق من أن العميل والمعالج موجودان
        $client = $db->select("SELECT id, first_name, last_name FROM users WHERE id = ? AND (user_type = 'regular' OR user_type IS NULL OR user_type = '')", [$_POST['client_id']]);
        if (empty($client)) {
            throw new Exception("العميل غير موجود أو غير صحيح");
        }
        
        $therapist = $db->select("SELECT id, first_name, last_name FROM users WHERE id = ? AND user_type = 'specialist'", [$_POST['therapist_id']]);
        if (empty($therapist)) {
            throw new Exception("المعالج غير موجود أو غير صحيح");
        }
        
        // التحقق من أن التاريخ والوقت في المستقبل
        $appointmentDateTime = $_POST['appointment_date'] . ' ' . $_POST['appointment_time'];
        if (strtotime($appointmentDateTime) <= time()) {
            throw new Exception("يجب أن يكون الموعد في المستقبل");
        }
        
        // التحقق من عدم وجود تعارض في المواعيد
        $conflictingAppointment = $db->select("
            SELECT id FROM appointments 
            WHERE therapist_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'
        ", [$_POST['therapist_id'], $_POST['appointment_date'], $_POST['appointment_time']]);
        
        if (!empty($conflictingAppointment)) {
            throw new Exception("هذا الموعد محجوز بالفعل للمعالج المحدد");
        }
        
        // إعداد البيانات للإدراج
        $appointmentData = [
            'client_id' => $_POST['client_id'],
            'therapist_id' => $_POST['therapist_id'],
            'appointment_date' => $_POST['appointment_date'],
            'appointment_time' => $_POST['appointment_time'],
            'status' => $_POST['status'] ?? 'confirmed',
            'notes' => trim($_POST['notes'] ?? ''),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // إدراج الموعد الجديد
        $columns = implode(', ', array_keys($appointmentData));
        $placeholders = ':' . implode(', :', array_keys($appointmentData));
        $sql = "INSERT INTO appointments ($columns) VALUES ($placeholders)";
        
        $appointmentId = $db->insert($sql, $appointmentData);
        
        if ($appointmentId) {
            setAlert('تم إضافة الموعد الجديد بنجاح', 'success');
            header('Location: /nafsi_platform/admin/appointments');
            exit;
        } else {
            throw new Exception("فشل في إضافة الموعد");
        }
        
    } catch (Exception $e) {
        setAlert('حدث خطأ: ' . $e->getMessage(), 'danger');
    }
}

// الحصول على قائمة العملاء
try {
    $db = db();
    $clients = $db->select("
        SELECT id, first_name, last_name, email, phone 
        FROM users 
        WHERE (user_type = 'regular' OR user_type IS NULL OR user_type = '')
        ORDER BY first_name, last_name
    ");
} catch (Exception $e) {
    $clients = [];
    setAlert('حدث خطأ في تحميل قائمة العملاء: ' . $e->getMessage(), 'danger');
}

// الحصول على قائمة المعالجين
try {
    $therapists = $db->select("
        SELECT id, first_name, last_name, email, phone
        FROM users 
        WHERE user_type = 'specialist'
        ORDER BY first_name, last_name
    ");
} catch (Exception $e) {
    $therapists = [];
    setAlert('حدث خطأ في تحميل قائمة المعالجين: ' . $e->getMessage(), 'danger');
}

// تعيين متغيرات الصفحة
$pageTitle = 'إضافة موعد جديد';
$currentPage = 'admin_add_appointment';

// تضمين header
include_once PUBLIC_ROOT . '/includes/header.php';
?>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    .form-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    .form-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card page-header">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-calendar-plus me-2"></i>
                                إضافة موعد جديد
                            </h2>
                            <p class="mb-0">إضافة موعد جديد بين عميل ومعالج</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= url('admin/appointments') ?>" class="btn btn-light">
                                <i class="fas fa-arrow-right"></i> العودة لقائمة المواعيد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسالة إذا لم يوجد عملاء أو معالجين -->
    <?php if (empty($clients) || empty($therapists)): ?>
    <div class="alert alert-warning text-center">
        <i class="fas fa-exclamation-triangle"></i>
        <?php if (empty($clients) && empty($therapists)): ?>
            لا يوجد عملاء أو معالجين متاحين. يرجى إضافة بيانات في لوحة المستخدمين والمعالجين أولاً.
        <?php elseif (empty($clients)): ?>
            لا يوجد عملاء متاحين. يرجى إضافة عملاء أولاً.
        <?php else: ?>
            لا يوجد معالجين متاحين. يرجى إضافة معالجين أولاً.
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- نموذج إضافة الموعد -->
    <div class="row">
        <div class="col-12">
            <div class="card form-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        بيانات الموعد الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="appointmentForm" <?= (empty($clients) || empty($therapists)) ? 'style="opacity: 0.6; pointer-events: none;"' : '' ?>>
                        <div class="row">
                            <!-- اختيار العميل -->
                            <div class="col-md-6 mb-3">
                                <label for="client_id" class="form-label">العميل *</label>
                                <select class="form-select" id="client_id" name="client_id" required <?= empty($clients) ? 'disabled' : '' ?>>
                                    <option value="">اختر العميل</option>
                                    <?php if (empty($clients)): ?>
                                    <option value="" disabled>لا يوجد عملاء متاحين</option>
                                    <?php else: ?>
                                    <?php foreach ($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>">
                                        <?= htmlspecialchars($client['first_name'] . ' ' . $client['last_name']) ?> 
                                        (<?= htmlspecialchars($client['email']) ?>)
                                    </option>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">
                                    <?php if (empty($clients)): ?>
                                    <span class="text-danger">لا يوجد عملاء متاحين. يرجى إضافة عملاء أولاً.</span>
                                    <?php else: ?>
                                    اختر العميل الذي سيحجز الموعد
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- اختيار المعالج -->
                            <div class="col-md-6 mb-3">
                                <label for="therapist_id" class="form-label">المعالج *</label>
                                <select class="form-select" id="therapist_id" name="therapist_id" required <?= empty($therapists) ? 'disabled' : '' ?>>
                                    <option value="">اختر المعالج</option>
                                    <?php if (empty($therapists)): ?>
                                    <option value="" disabled>لا يوجد معالجين متاحين</option>
                                    <?php else: ?>
                                    <?php foreach ($therapists as $therapist): ?>
                                    <option value="<?= $therapist['id'] ?>">
                                        <?= htmlspecialchars($therapist['first_name'] . ' ' . $therapist['last_name']) ?> 
                                        (<?= htmlspecialchars($therapist['specialization'] ?? 'عام') ?>)
                                    </option>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">
                                    <?php if (empty($therapists)): ?>
                                    <span class="text-danger">لا يوجد معالجين متاحين. يرجى إضافة معالجين أولاً.</span>
                                    <?php else: ?>
                                    اختر المعالج النفسي
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ الموعد -->
                            <div class="col-md-6 mb-3">
                                <label for="appointment_date" class="form-label">تاريخ الموعد *</label>
                                <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                       min="<?= date('Y-m-d') ?>" required <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>>
                                <div class="form-text">اختر تاريخ الموعد (يجب أن يكون في المستقبل)</div>
                            </div>

                            <!-- وقت الموعد -->
                            <div class="col-md-6 mb-3">
                                <label for="appointment_time" class="form-label">وقت الموعد *</label>
                                <select class="form-select" id="appointment_time" name="appointment_time" required <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>>
                                    <option value="">اختر الوقت</option>
                                    <option value="09:00">09:00 صباحاً</option>
                                    <option value="10:00">10:00 صباحاً</option>
                                    <option value="11:00">11:00 صباحاً</option>
                                    <option value="12:00">12:00 ظهراً</option>
                                    <option value="13:00">01:00 ظهراً</option>
                                    <option value="14:00">02:00 ظهراً</option>
                                    <option value="15:00">03:00 عصراً</option>
                                    <option value="16:00">04:00 عصراً</option>
                                    <option value="17:00">05:00 عصراً</option>
                                    <option value="18:00">06:00 مساءً</option>
                                    <option value="19:00">07:00 مساءً</option>
                                    <option value="20:00">08:00 مساءً</option>
                                </select>
                                <div class="form-text">اختر وقت الموعد المناسب</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- حالة الموعد -->
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">حالة الموعد</label>
                                <select class="form-select" id="status" name="status" <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>>
                                    <option value="confirmed">مؤكد</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                                <div class="form-text">حالة الموعد الافتراضية</div>
                            </div>

                            <!-- مدة الجلسة -->
                            <div class="col-md-6 mb-3">
                                <label for="duration" class="form-label">مدة الجلسة</label>
                                <select class="form-select" id="duration" name="duration" <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>>
                                    <option value="30">30 دقيقة</option>
                                    <option value="45">45 دقيقة</option>
                                    <option value="60" selected>60 دقيقة</option>
                                    <option value="90">90 دقيقة</option>
                                    <option value="120">120 دقيقة</option>
                                </select>
                                <div class="form-text">مدة الجلسة المحددة</div>
                            </div>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="أي ملاحظات إضافية حول الموعد..." <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>></textarea>
                            <div class="form-text">ملاحظات اختيارية حول الموعد</div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" <?= (empty($clients) || empty($therapists)) ? 'disabled' : '' ?>>
                                <i class="fas fa-save me-2"></i>
                                <?= (empty($clients) || empty($therapists)) ? 'لا يمكن الإضافة' : 'إضافة الموعد' ?>
                            </button>
                            <a href="<?= url('admin/appointments') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('appointmentForm');
    const clientSelect = document.getElementById('client_id');
    const therapistSelect = document.getElementById('therapist_id');
    const dateInput = document.getElementById('appointment_date');
    const timeSelect = document.getElementById('appointment_time');
    
    // التحقق من وجود بيانات
    const hasClients = <?= !empty($clients) ? 'true' : 'false' ?>;
    const hasTherapists = <?= !empty($therapists) ? 'true' : 'false' ?>;
    
    if (!hasClients || !hasTherapists) {
        return;
    }

    // التحقق من صحة البيانات قبل الإرسال
    form.addEventListener('submit', function(e) {
        if (!clientSelect.value) {
            e.preventDefault();
            alert('يرجى اختيار العميل');
            clientSelect.focus();
            return;
        }

        if (!therapistSelect.value) {
            e.preventDefault();
            alert('يرجى اختيار المعالج');
            therapistSelect.focus();
            return;
        }

        if (!dateInput.value) {
            e.preventDefault();
            alert('يرجى اختيار تاريخ الموعد');
            dateInput.focus();
            return;
        }

        if (!timeSelect.value) {
            e.preventDefault();
            alert('يرجى اختيار وقت الموعد');
            timeSelect.focus();
            return;
        }

        // التحقق من أن التاريخ في المستقبل
        const selectedDate = new Date(dateInput.value + ' ' + timeSelect.value);
        const now = new Date();
        
        if (selectedDate <= now) {
            e.preventDefault();
            alert('يجب أن يكون الموعد في المستقبل');
            return;
        }
    });

    // تعيين التاريخ الافتراضي كغد
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.value = tomorrow.toISOString().split('T')[0];

    // إضافة تأثيرات بصرية
    const selects = document.querySelectorAll('.form-select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            if (this.value) {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
});
</script>

<?php include_once PUBLIC_ROOT . '/includes/footer.php'; ?> 