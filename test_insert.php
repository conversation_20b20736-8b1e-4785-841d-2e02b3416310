<?php
/**
 * اختبار إدراج موعد
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>اختبار إدراج موعد</h2>";
    
    // بيانات الموعد التجريبي
    $appointmentData = [
        'client_id' => 13, // مريم علي
        'therapist_id' => 3, // أحمد محمد
        'appointment_date' => '2024-12-25',
        'appointment_time' => '10:00',
        'status' => 'confirmed',
        'notes' => 'موعد تجريبي للاختبار',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // بناء استعلام INSERT
    $columns = implode(', ', array_keys($appointmentData));
    $placeholders = ':' . implode(', :', array_keys($appointmentData));
    $sql = "INSERT INTO appointments ($columns) VALUES ($placeholders)";
    
    echo "<h3>استعلام SQL:</h3>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<h3>البيانات:</h3>";
    echo "<pre>" . print_r($appointmentData, true) . "</pre>";
    
    echo "<h3>محاولة الإدراج:</h3>";
    $result = $db->insert($sql, $appointmentData);
    
    if ($result === false) {
        echo "<p style='color: red;'>فشل في إدراج الموعد</p>";
        $error = $db->getLastError();
        if ($error) {
            echo "<p style='color: red;'>خطأ SQL: " . $error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>تم إدراج الموعد بنجاح! (ID: $result)</p>";
        
        // عرض الموعد المضاف
        $appointment = $db->selectOne("
            SELECT a.*, 
                   c.first_name as client_first_name, c.last_name as client_last_name,
                   t.first_name as therapist_first_name, t.last_name as therapist_last_name
            FROM appointments a
            JOIN users c ON a.client_id = c.id
            JOIN users t ON a.therapist_id = t.id
            WHERE a.id = ?
        ", [$result]);
        
        if ($appointment) {
            echo "<h3>تفاصيل الموعد المضاف:</h3>";
            echo "<p><strong>العميل:</strong> " . $appointment['client_first_name'] . ' ' . $appointment['client_last_name'] . "</p>";
            echo "<p><strong>المعالج:</strong> " . $appointment['therapist_first_name'] . ' ' . $appointment['therapist_last_name'] . "</p>";
            echo "<p><strong>التاريخ:</strong> " . $appointment['appointment_date'] . "</p>";
            echo "<p><strong>الوقت:</strong> " . $appointment['appointment_time'] . "</p>";
            echo "<p><strong>الحالة:</strong> " . $appointment['status'] . "</p>";
            echo "<p><strong>الملاحظات:</strong> " . $appointment['notes'] . "</p>";
        }
        
        // حذف الموعد التجريبي
        $db->delete("DELETE FROM appointments WHERE id = ?", [$result]);
        echo "<p>تم حذف الموعد التجريبي</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 