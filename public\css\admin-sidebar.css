/* ===== الشريط الجانبي الجديد - منصة نفسي ===== */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    direction: rtl;
    overflow-x: hidden;
}

/* ===== الشريط الجانبي الرئيسي ===== */
.admin-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: -5px 0 20px rgba(102, 126, 234, 0.2);
    display: flex;
    flex-direction: column;
}

/* حالة الطي */
.admin-sidebar.collapsed {
    width: 70px;
}

/* رأس الشريط الجانبي */
.sidebar-header {
    padding: 2rem 1rem;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.sidebar-header .logo {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.sidebar-header h3 {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
    transition: all 0.3s ease;
}

.sidebar-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0.5rem 0 0 0;
    transition: all 0.3s ease;
}

/* قائمة التنقل */
.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(-8px);
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.nav-link i {
    width: 22px;
    margin-left: 0.75rem;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.nav-link span {
    flex: 1;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

/* شارة التنبيه */
.notification-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 0.5rem;
    box-shadow: 0 2px 8px rgba(238, 90, 82, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* قسم المستخدم */
.sidebar-footer {
    padding: 1.5rem 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.user-avatar {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.user-details {
    flex: 1;
    color: white;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(238, 90, 82, 0.3);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #ee5a52 0%, #ff6b6b 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(238, 90, 82, 0.4);
}

.logout-btn i {
    margin-left: 0.5rem;
}

/* زر التبديل */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 290px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    font-size: 1.2rem;
}

.sidebar-toggle:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: scale(1.1) rotate(180deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.sidebar-toggle.collapsed {
    right: 80px;
}

/* المحتوى الرئيسي */
.admin-main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.admin-main-content.sidebar-collapsed {
    margin-right: 70px;
}

/* حالة الطي للعناصر */
.admin-sidebar.collapsed .sidebar-header h3,
.admin-sidebar.collapsed .sidebar-header p,
.admin-sidebar.collapsed .nav-link span,
.admin-sidebar.collapsed .user-details,
.admin-sidebar.collapsed .logout-btn span {
    opacity: 0;
    visibility: hidden;
    transform: translateX(20px);
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 1rem 0.5rem;
}

.admin-sidebar.collapsed .nav-link i {
    margin-left: 0;
    font-size: 1.3rem;
}

.admin-sidebar.collapsed .user-info {
    justify-content: center;
    padding: 0.5rem;
}

.admin-sidebar.collapsed .user-avatar {
    margin-left: 0;
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        width: 300px;
        box-shadow: -10px 0 30px rgba(102, 126, 234, 0.3);
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }

    .admin-sidebar.collapsed {
        width: 70px;
        transform: translateX(100%);
    }

    .admin-sidebar.collapsed.mobile-open {
        transform: translateX(0);
    }

    .sidebar-toggle {
        right: 20px;
        top: 20px;
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }

    .sidebar-toggle.collapsed {
        right: 20px;
    }

    .admin-main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .admin-main-content.sidebar-collapsed {
        margin-right: 0;
    }

    /* تحسين النصوص على الشاشات الصغيرة */
    .sidebar-header h3 {
        font-size: 1.1rem;
    }

    .nav-link {
        padding: 1.2rem;
        font-size: 0.9rem;
    }

    .nav-link i {
        font-size: 1.3rem;
        width: 25px;
    }
}

/* خلفية ضبابية للشاشات الصغيرة */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* تحسينات شريط التمرير */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* تأثيرات حركية للعناصر */
.nav-item {
    animation: slideInRight 0.3s ease forwards;
    opacity: 0;
    transform: translateX(30px);
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }
.nav-item:nth-child(6) { animation-delay: 0.6s; }
.nav-item:nth-child(7) { animation-delay: 0.7s; }
.nav-item:nth-child(8) { animation-delay: 0.8s; }
.nav-item:nth-child(9) { animation-delay: 0.9s; }
.nav-item:nth-child(10) { animation-delay: 1.0s; }

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسينات للطباعة */
@media print {
    .admin-sidebar,
    .sidebar-toggle,
    .sidebar-backdrop {
        display: none;
    }

    .admin-main-content {
        margin-right: 0;
        padding: 0;
    }
}
