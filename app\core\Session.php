<?php
/**
 * كلاس الجلسة
 * Session Class
 * 
 * هذا الكلاس يتعامل مع إدارة جلسات المستخدمين
 * بما في ذلك إنشاء الجلسات، تتبع النشاط،
 * إدارة الجلسات المتعددة، ونظام الأمان
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Session {
    private $db;
    private $sessionId;
    private $userId;
    private $startTime;
    private $lastActivity;
    
    /**
     * حالات الجلسة
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_DESTROYED = 'destroyed';
    
    /**
     * مدة صلاحية الجلسة (بالثواني)
     */
    const SESSION_LIFETIME = 1800; // 30 دقيقة
    
    public function __construct() {
        $this->db = db();
        $this->init();
    }
    
    /**
     * تهيئة الجلسة
     */
    private function init() {
        // ملاحظة: إعدادات الجلسة تتم في app/init.php

        // بدء الجلسة إذا لم تكن موجودة
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // التحقق من صحة الجلسة
        $this->validateSession();

        // تحديث آخر نشاط
        $this->updateLastActivity();
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    private function validateSession() {
        // التحقق من وجود معرف الجلسة
        if (!isset($_SESSION['session_id'])) {
            $this->createNewSession();
            return;
        }
        
        $this->sessionId = $_SESSION['session_id'];
        
        // التحقق من وجود الجلسة في قاعدة البيانات
        $sql = "SELECT * FROM user_sessions WHERE session_id = :session_id AND status = :status";
        $session = $this->db->selectOne($sql, [
            ':session_id' => $this->sessionId,
            ':status' => self::STATUS_ACTIVE
        ]);
        
        if (!$session) {
            // الجلسة غير صحيحة، إنشاء جلسة جديدة
            $this->createNewSession();
            return;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        if (time() - strtotime($session['last_activity']) > self::SESSION_LIFETIME) {
            $this->expireSession($this->sessionId);
            $this->createNewSession();
            return;
        }
        
        // تحديث آخر نشاط
        $this->updateSessionActivity($this->sessionId);
    }
    
    /**
     * إنشاء جلسة جديدة
     */
    private function createNewSession() {
        $this->sessionId = $this->generateSessionId();
        $_SESSION['session_id'] = $this->sessionId;
        $_SESSION['created_at'] = time();
        
        // حفظ الجلسة في قاعدة البيانات
        $sql = "INSERT INTO user_sessions (session_id, ip_address, user_agent, created_at, last_activity, status) 
                VALUES (:session_id, :ip_address, :user_agent, NOW(), NOW(), :status)";
        
        $params = [
            ':session_id' => $this->sessionId,
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            ':status' => self::STATUS_ACTIVE
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * توليد معرف جلسة فريد
     * @return string
     */
    private function generateSessionId() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * تعيين قيمة في الجلسة
     * @param string $key المفتاح
     * @param mixed $value القيمة
     */
    public function set($key, $value) {
        $_SESSION[$key] = $value;
        
        // إذا كان المستخدم مسجل دخول، تحديث معرف المستخدم في قاعدة البيانات
        if ($key === 'user_id' && $this->sessionId) {
            $this->updateSessionUserId($this->sessionId, $value);
        }
    }
    
    /**
     * الحصول على قيمة من الجلسة
     * @param string $key المفتاح
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    public function get($key, $default = null) {
        return $_SESSION[$key] ?? $default;
    }
    
    /**
     * التحقق من وجود مفتاح في الجلسة
     * @param string $key المفتاح
     * @return bool
     */
    public function has($key) {
        return isset($_SESSION[$key]);
    }
    
    /**
     * حذف مفتاح من الجلسة
     * @param string $key المفتاح
     */
    public function remove($key) {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * حذف جميع البيانات من الجلسة
     */
    public function clear() {
        $_SESSION = [];
    }
    
    /**
     * تدمير الجلسة
     */
    public function destroy() {
        // حذف الجلسة من قاعدة البيانات
        if ($this->sessionId) {
            $this->expireSession($this->sessionId);
        }
        
        // حذف كوكيز الجلسة
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
        
        // تدمير الجلسة
        session_destroy();
        
        // إعادة تهيئة الجلسة
        $this->init();
    }
    
    /**
     * تحديث آخر نشاط
     */
    private function updateLastActivity() {
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * تحديث نشاط الجلسة في قاعدة البيانات
     * @param string $sessionId معرف الجلسة
     */
    private function updateSessionActivity($sessionId) {
        $sql = "UPDATE user_sessions SET last_activity = NOW() WHERE session_id = :session_id";
        $this->db->execute($sql, [':session_id' => $sessionId]);
    }
    
    /**
     * تحديث معرف المستخدم في الجلسة
     * @param string $sessionId معرف الجلسة
     * @param int $userId معرف المستخدم
     */
    private function updateSessionUserId($sessionId, $userId) {
        $sql = "UPDATE user_sessions SET user_id = :user_id WHERE session_id = :session_id";
        $this->db->execute($sql, [
            ':user_id' => $userId,
            ':session_id' => $sessionId
        ]);
    }
    
    /**
     * انتهاء صلاحية الجلسة
     * @param string $sessionId معرف الجلسة
     */
    private function expireSession($sessionId) {
        $sql = "UPDATE user_sessions SET status = :status WHERE session_id = :session_id";
        $this->db->execute($sql, [
            ':status' => self::STATUS_EXPIRED,
            ':session_id' => $sessionId
        ]);
    }
    
    /**
     * الحصول على معلومات الجلسة الحالية
     * @return array
     */
    public function getSessionInfo() {
        $sql = "SELECT * FROM user_sessions WHERE session_id = :session_id";
        return $this->db->selectOne($sql, [':session_id' => $this->sessionId]);
    }
    
    /**
     * الحصول على جميع جلسات المستخدم
     * @param int $userId معرف المستخدم
     * @return array
     */
    public function getUserSessions($userId) {
        $sql = "SELECT * FROM user_sessions WHERE user_id = :user_id AND status = :status ORDER BY last_activity DESC";
        return $this->db->select($sql, [
            ':user_id' => $userId,
            ':status' => self::STATUS_ACTIVE
        ]);
    }
    
    /**
     * حذف جميع جلسات المستخدم
     * @param int $userId معرف المستخدم
     * @param string $excludeSessionId معرف الجلسة المستثناة
     * @return bool
     */
    public function destroyUserSessions($userId, $excludeSessionId = null) {
        $sql = "UPDATE user_sessions SET status = :status WHERE user_id = :user_id";
        $params = [
            ':status' => self::STATUS_DESTROYED,
            ':user_id' => $userId
        ];
        
        if ($excludeSessionId) {
            $sql .= " AND session_id != :exclude_session_id";
            $params[':exclude_session_id'] = $excludeSessionId;
        }
        
        return $this->db->execute($sql, $params);
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     * @return int عدد الجلسات المنظفة
     */
    public function cleanupExpiredSessions() {
        $sql = "UPDATE user_sessions SET status = :status 
                WHERE last_activity < DATE_SUB(NOW(), INTERVAL :lifetime SECOND) 
                AND status = :active_status";
        
        $params = [
            ':status' => self::STATUS_EXPIRED,
            ':lifetime' => self::SESSION_LIFETIME,
            ':active_status' => self::STATUS_ACTIVE
        ];
        
        $this->db->execute($sql, $params);
        
        // الحصول على عدد الجلسات المنظفة
        $sql = "SELECT COUNT(*) as count FROM user_sessions WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_EXPIRED]);
        
        return $result['count'];
    }
    
    /**
     * الحصول على إحصائيات الجلسات
     * @return array
     */
    public function getSessionStatistics() {
        $stats = [];
        
        // إجمالي الجلسات النشطة
        $sql = "SELECT COUNT(*) as active FROM user_sessions WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_ACTIVE]);
        $stats['active_sessions'] = $result ? $result['active'] : 0;

        // الجلسات المنتهية الصلاحية
        $sql = "SELECT COUNT(*) as expired FROM user_sessions WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_EXPIRED]);
        $stats['expired_sessions'] = $result ? $result['expired'] : 0;

        // الجلسات المدمرة
        $sql = "SELECT COUNT(*) as destroyed FROM user_sessions WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_DESTROYED]);
        $stats['destroyed_sessions'] = $result ? $result['destroyed'] : 0;

        // متوسط مدة الجلسة
        $sql = "SELECT AVG(TIMESTAMPDIFF(SECOND, created_at, last_activity)) as avg_duration
                FROM user_sessions WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_ACTIVE]);
        $stats['average_session_duration'] = $result ? round($result['avg_duration'] ?? 0, 2) : 0;
        
        return $stats;
    }
    
    /**
     * التحقق من تغيير عنوان IP
     * @return bool
     */
    public function isIPChanged() {
        $sessionInfo = $this->getSessionInfo();
        if (!$sessionInfo) {
            return false;
        }
        
        $currentIP = $_SERVER['REMOTE_ADDR'] ?? '';
        return $sessionInfo['ip_address'] !== $currentIP;
    }
    
    /**
     * التحقق من تغيير User Agent
     * @return bool
     */
    public function isUserAgentChanged() {
        $sessionInfo = $this->getSessionInfo();
        if (!$sessionInfo) {
            return false;
        }
        
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        return $sessionInfo['user_agent'] !== $currentUserAgent;
    }
    
    /**
     * تجديد الجلسة
     * @return bool
     */
    public function regenerate() {
        // حفظ البيانات المهمة
        $importantData = [];
        $importantKeys = ['user_id', 'user_type', 'user_name'];
        
        foreach ($importantKeys as $key) {
            if ($this->has($key)) {
                $importantData[$key] = $this->get($key);
            }
        }
        
        // تدمير الجلسة الحالية
        $this->destroy();
        
        // إنشاء جلسة جديدة
        $this->createNewSession();
        
        // استعادة البيانات المهمة
        foreach ($importantData as $key => $value) {
            $this->set($key, $value);
        }
        
        return true;
    }
    
    /**
     * الحصول على معرف الجلسة الحالية
     * @return string
     */
    public function getSessionId() {
        return $this->sessionId;
    }
    
    /**
     * التحقق من صحة الجلسة
     * @return bool
     */
    public function isValid() {
        if (!$this->sessionId) {
            return false;
        }
        
        $sessionInfo = $this->getSessionInfo();
        return $sessionInfo && $sessionInfo['status'] === self::STATUS_ACTIVE;
    }
    
    /**
     * تسجيل نشاط الجلسة
     * @param string $activity النشاط
     * @param array $data بيانات إضافية
     */
    public function logActivity($activity, $data = []) {
        if (!$this->sessionId) {
            return;
        }
        
        $sql = "INSERT INTO session_activities (session_id, activity, data, created_at) 
                VALUES (:session_id, :activity, :data, NOW())";
        
        $params = [
            ':session_id' => $this->sessionId,
            ':activity' => $activity,
            ':data' => json_encode($data)
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * الحصول على نشاطات الجلسة
     * @param int $limit عدد النتائج
     * @return array
     */
    public function getSessionActivities($limit = 50) {
        if (!$this->sessionId) {
            return [];
        }
        
        $sql = "SELECT * FROM session_activities WHERE session_id = :session_id 
                ORDER BY created_at DESC LIMIT :limit";
        
        return $this->db->select($sql, [
            ':session_id' => $this->sessionId,
            ':limit' => $limit
        ]);
    }
}
?> 