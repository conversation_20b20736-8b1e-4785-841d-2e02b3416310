<?php
/**
 * صفحة إنشاء حساب جديد
 * Register Page
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /register');
    exit;
}
?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h2 class="mb-3">إنشاء حساب جديد</h2>
                        <p class="text-muted">انضم إلى منصة نفسي وابدأ رحلتك نحو الصحة النفسية</p>
                    </div>
                    
                                    <form action="<?= url('handlers/register_handler') ?>" method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                    <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="firstName" name="firstName" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال الاسم الأول
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">الاسم الأخير *</label>
                                <input type="text" class="form-control" id="lastName" name="lastName" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال الاسم الأخير
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="05XXXXXXXX">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dateOfBirth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="dateOfBirth" name="dateOfBirth">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                    <option value="other">آخر</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted" id="passwordFeedback">أدخل كلمة المرور</small>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال كلمة المرور
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                كلمة المرور غير متطابقة
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="<?= url('terms') ?>" target="_blank">الشروط والأحكام</a> *
                                </label>
                                <div class="invalid-feedback">
                                    يجب الموافقة على الشروط والأحكام
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="privacy" name="privacy" required>
                                <label class="form-check-label" for="privacy">
                                    أوافق على <a href="<?= url('privacy') ?>" target="_blank">سياسة الخصوصية</a> *
                                </label>
                                <div class="invalid-feedback">
                                    يجب الموافقة على سياسة الخصوصية
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    أريد استلام النشرة الإخبارية
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" data-loading>
                                <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="text-muted mb-3">لديك حساب بالفعل؟</p>
                        <a href="<?= url('login') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Benefits -->
            <div class="text-center mt-4">
                <h5 class="mb-3">مزايا الانضمام إلى منصة نفسي</h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-user-md text-primary mb-2"></i>
                                <h6>أخصائيين معتمدين</h6>
                                <small class="text-muted">فريق من الخبراء</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-shield-alt text-primary mb-2"></i>
                                <h6>خصوصية تامة</h6>
                                <small class="text-muted">بياناتك محمية</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-clock text-primary mb-2"></i>
                                <h6>متاح 24/7</h6>
                                <small class="text-muted">خدمة مستمرة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle Password Visibility
function togglePasswordVisibility(inputId, buttonId) {
    const input = document.getElementById(inputId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    button.addEventListener('click', function() {
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
}

togglePasswordVisibility('password', 'togglePassword');
togglePasswordVisibility('confirmPassword', 'toggleConfirmPassword');

// Password Strength Checker
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('passwordStrength');
    const feedback = document.getElementById('passwordFeedback');
    
    let strength = 0;
    let feedbackText = '';
    
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    const percentage = (strength / 5) * 100;
    strengthBar.style.width = percentage + '%';
    
    if (percentage < 20) {
        strengthBar.className = 'progress-bar bg-danger';
        feedbackText = 'ضعيفة جداً';
    } else if (percentage < 40) {
        strengthBar.className = 'progress-bar bg-warning';
        feedbackText = 'ضعيفة';
    } else if (percentage < 60) {
        strengthBar.className = 'progress-bar bg-info';
        feedbackText = 'متوسطة';
    } else if (percentage < 80) {
        strengthBar.className = 'progress-bar bg-primary';
        feedbackText = 'قوية';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        feedbackText = 'قوية جداً';
    }
    
    feedback.textContent = feedbackText;
});

// Confirm Password Check
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});
</script> 