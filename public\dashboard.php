<?php
/**
 * لوحة التحكم الرئيسية المحدثة
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

// الحصول على بيانات المستخدم الحالي
$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('حدث خطأ في تحميل بيانات المستخدم', 'error');
    header('Location: /nafsi_platform/login');
    exit;
}

// إعادة توجيه المدير والمعالجين لوحات التحكم الخاصة بهم
if ($currentUser['user_type'] === 'admin') {
    header('Location: /nafsi_platform/admin/dashboard');
    exit;
} elseif ($currentUser['user_type'] === 'therapist') {
    header('Location: /nafsi_platform/therapist/dashboard');
    exit;
}

// الحصول على التنبيهات
$alert = getAlert();

// الحصول على إحصائيات المستخدم
try {
    $db = db();

    // عدد الجلسات المحجوزة
    $bookedSessions = $db->select("SELECT COUNT(*) as count FROM appointments WHERE client_id = ? AND status = 'confirmed'", [$currentUser['id']]);
    $bookedSessionsCount = $bookedSessions[0]['count'] ?? 0;

    // عدد الجلسات المكتملة
    $completedSessions = $db->select("SELECT COUNT(*) as count FROM appointments WHERE client_id = ? AND status = 'completed'", [$currentUser['id']]);
    $completedSessionsCount = $completedSessions[0]['count'] ?? 0;

    // الجلسات القادمة
    $upcomingSessions = $db->select("SELECT a.*, u.first_name, u.last_name FROM appointments a JOIN users u ON a.therapist_id = u.id WHERE a.client_id = ? AND a.status = 'confirmed' AND a.appointment_date >= CURDATE() ORDER BY a.appointment_date ASC LIMIT 3", [$currentUser['id']]);

} catch (Exception $e) {
    $bookedSessionsCount = 0;
    $completedSessionsCount = 0;
    $upcomingSessions = [];
}

// تعيين متغيرات الصفحة
$pageTitle = 'لوحة التحكم';
$currentPage = 'dashboard';

// تضمين header
include_once 'includes/header.php';
?>

<style>
    .welcome-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stats-card {
        border-radius: 15px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin: 0 auto 1rem;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .upcoming-session {
        border-left: 4px solid #667eea;
        background: #f8f9ff;
    }
</style>

<div class="container mt-4">
    <!-- عرض التنبيهات -->
    <?php if ($alert): ?>
    <div class="alert alert-<?= $alert['type'] === 'error' ? 'danger' : $alert['type'] ?> alert-dismissible fade show" role="alert">
        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?>"></i>
        <?= htmlspecialchars($alert['message']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- بطاقة الترحيب -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card welcome-card">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="col-md-10">
                            <h2 class="mb-2">مرحباً، <?= htmlspecialchars($currentUser['first_name']) ?>!</h2>
                            <p class="mb-1">
                                <i class="fas fa-envelope"></i> 
                                <?= htmlspecialchars($currentUser['email']) ?>
                            </p>
                            <p class="mb-1">
                                <i class="fas fa-user-tag"></i> 
                                <?php
                                $userTypeNames = [
                                    'admin' => 'مدير النظام',
                                    'therapist' => 'معالج نفسي',
                                    'user' => 'مستخدم'
                                ];
                                echo $userTypeNames[$currentUser['user_type']] ?? $currentUser['user_type'];
                                ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-calendar"></i> 
                                آخر تسجيل دخول: <?= $currentUser['last_login'] ? date('Y-m-d H:i', strtotime($currentUser['last_login'])) : 'لم يتم تسجيل الدخول من قبل' ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-primary text-white">
                <div class="card-body text-center">
                    <div class="card-icon bg-white text-primary mx-auto">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3 class="mb-1"><?= $bookedSessionsCount ?></h3>
                    <p class="mb-0">الجلسات المحجوزة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-success text-white">
                <div class="card-body text-center">
                    <div class="card-icon bg-white text-success mx-auto">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="mb-1"><?= $completedSessionsCount ?></h3>
                    <p class="mb-0">الجلسات المكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="card-icon bg-white text-warning mx-auto">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="mb-1">
                        <?php
                        try {
                            $therapists = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'therapist' AND status = 'active'");
                            echo $therapists[0]['count'] ?? 0;
                        } catch (Exception $e) {
                            echo '0';
                        }
                        ?>
                    </h3>
                    <p class="mb-0">المعالجين المتاحين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card bg-info text-white">
                <div class="card-body text-center">
                    <div class="card-icon bg-white text-info mx-auto">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="mb-1"><?= count($upcomingSessions) ?></h3>
                    <p class="mb-0">الجلسات القادمة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الأقسام الرئيسية -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> الجلسات القادمة</h5>
                    <a href="<?= url('appointments') ?>" class="btn btn-sm btn-light">
                        <i class="fas fa-eye"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($upcomingSessions)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                        <p>لا توجد جلسات مجدولة حالياً</p>
                        <a href="<?= url('specialists') ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> حجز جلسة جديدة
                        </a>
                    </div>
                    <?php else: ?>
                    <?php foreach ($upcomingSessions as $session): ?>
                    <div class="upcoming-session p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-user-md text-primary"></i>
                                    د. <?= htmlspecialchars($session['first_name'] . ' ' . $session['last_name']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <i class="fas fa-calendar"></i>
                                    <?= date('Y-m-d', strtotime($session['appointment_date'])) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="fas fa-clock"></i>
                                    <?= date('H:i', strtotime($session['appointment_time'])) ?>
                                </p>
                            </div>
                            <span class="badge bg-primary">مؤكدة</span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-bell"></i> الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= url('specialists') ?>" class="btn btn-primary">
                            <i class="fas fa-user-md"></i> تصفح المختصين
                        </a>
                        <a href="<?= url('appointments') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt"></i> إدارة المواعيد
                        </a>
                        <a href="<?= url('profile') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit"></i> تحديث الملف الشخصي
                        </a>
                        <a href="<?= url('library') ?>" class="btn btn-outline-info">
                            <i class="fas fa-book"></i> المكتبة النفسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5><i class="fas fa-link"></i> روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('profile') ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user"></i><br>
                                الملف الشخصي
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('appointments') ?>" class="btn btn-outline-success w-100">
                                <i class="fas fa-calendar"></i><br>
                                الجلسات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('specialists') ?>" class="btn btn-outline-info w-100">
                                <i class="fas fa-user-md"></i><br>
                                المعالجين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?= url('contact') ?>" class="btn btn-outline-warning w-100">
                                <i class="fas fa-life-ring"></i><br>
                                الدعم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once 'includes/footer.php'; ?>
