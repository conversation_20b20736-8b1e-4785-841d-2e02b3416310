# منصة نفسي - Nafsi Platform

منصة متخصصة في الاستشارات النفسية عبر الإنترنت، تتيح التواصل مع أخصائيين نفسيين معتمدين في بيئة آمنة ومهنية.

## الميزات الرئيسية

- ✅ نظام مصادقة آمن ومتطور
- ✅ إدارة المستخدمين والأخصائيين
- ✅ نظام حجز الجلسات
- ✅ مكتبة تعليمية
- ✅ نظام إشعارات متكامل
- ✅ واجهة مستخدم عربية جميلة
- ✅ نظام أمان قوي

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite مفعل
- Composer (اختياري)

## التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/nafsi_platform.git
cd nafsi_platform
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE nafsi_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد المخطط الأساسي
mysql -u root -p nafsi_platform < database/create_database.sql
```

### 3. تكوين التطبيق
عدّل ملف `app/config.php` حسب إعداداتك:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'nafsi_platform');
define('DB_USER', 'root');
define('DB_PASS', '');

// رابط التطبيق
define('APP_URL', 'http://localhost/nafsi_platform');
```

### 4. إعداد Apache
تأكد من تفعيل mod_rewrite في Apache:

```apache
# في ملف httpd.conf
LoadModule rewrite_module modules/mod_rewrite.so

# في إعدادات المجلد
<Directory "C:/xampp/htdocs">
    AllowOverride All
</Directory>
```

### 5. اختبار التثبيت
افتح المتصفح واذهب إلى:
```
http://localhost/nafsi_platform/test.php
```

## هيكل المشروع

```
nafsi_platform/
├── app/                    # منطق التطبيق
│   ├── core/              # كلاسات النواة
│   ├── handlers/          # معالجات الطلبات
│   ├── config.php         # إعدادات التطبيق
│   ├── functions.php      # دوال مساعدة
│   └── init.php          # تهيئة التطبيق
├── public/                # الملفات العامة
│   ├── includes/          # رأس وتذييل الصفحات
│   ├── components/        # مكونات قابلة لإعادة الاستخدام
│   ├── views/            # نماذج الصفحات
│   └── assets/           # الملفات الثابتة
├── database/              # ملفات قاعدة البيانات
├── uploads/              # الملفات المرفوعة
├── logs/                 # ملفات السجلات
├── index.php             # نقطة دخول التطبيق
├── .htaccess             # إعدادات Apache
└── test.php              # ملف اختبار
```

## كيفية الاستخدام

### الوصول للتطبيق
```
http://localhost/nafsi_platform/
```

### الروابط الرئيسية
- الصفحة الرئيسية: `/`
- تسجيل الدخول: `/login`
- إنشاء حساب: `/register`
- الأخصائيين: `/specialists`
- المكتبة: `/library`
- التواصل: `/contact`

### التطوير
```bash
# تشغيل خادم التطوير المحلي
php -S localhost:8000

# أو استخدام XAMPP/WAMP
# ثم الوصول إلى http://localhost/nafsi_platform/
```

## الأمان

- ✅ حماية من CSRF
- ✅ تشفير كلمات المرور
- ✅ حماية من SQL Injection
- ✅ حماية من XSS
- ✅ إدارة آمنة للجلسات
- ✅ تسجيل محاولات تسجيل الدخول

## المساهمة

1. Fork المشروع
2. أنشئ فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. أنشئ Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

- أنشئ Issue في GitHub
- راسلنا عبر البريد الإلكتروني: <EMAIL>

## التحديثات

راجع ملف `CHANGELOG.md` لمعرفة التحديثات والإضافات الجديدة.

---

**تم تطوير منصة نفسي بواسطة فريق متخصص في تطوير تطبيقات الويب** 