# دليل الشريط الجانبي للوحة تحكم المدير
# Admin Dashboard Sidebar Guide

## 📋 نظرة عامة

تم إنشاء شريط جانبي شامل للوحة تحكم المدير يوفر تنقل سلس بين جميع أقسام النظام مع تصميم عصري ومتجاوب.

## 🎯 الميزات الرئيسية

### ✅ التنقل الشامل
- **لوحة التحكم الرئيسية** - نظرة عامة على النظام
- **إدارة المستخدمين** - إضافة وتعديل وحذف المستخدمين
- **إدارة الأخصائيين** - إدارة المعالجين والأخصائيين النفسيين
- **إدارة المواعيد** - جدولة وإدارة المواعيد
- **إضافة موعد جديد** - إنشاء مواعيد جديدة
- **رسائل التواصل** - إدارة رسائل التواصل مع شارة تنبيه
- **إدارة المحتوى** - إدارة المقالات والمحتوى
- **التقارير** - إحصائيات وتقارير النظام
- **إعدادات النظام** - إعدادات الموقع والأمان
- **الملف الشخصي** - تحديث معلومات المدير

### ✅ التصميم المتقدم
- تصميم عصري مع تدرجات لونية جذابة
- تأثيرات حركية متقدمة
- أيقونات Font Awesome
- شارات تنبيه ملونة للرسائل الجديدة
- تصميم متجاوب للشاشات الصغيرة

### ✅ الوظائف الذكية
- إخفاء/إظهار الشريط الجانبي
- حفظ حالة الشريط الجانبي
- شارة تنبيه للرسائل الجديدة
- معلومات المستخدم الحالي
- روابط سريعة لجميع الأقسام

## 📁 الملفات المطلوبة

### الملفات الأساسية:
```
public/admin/includes/sidebar.php          # الشريط الجانبي الرئيسي
public/css/admin-dashboard.css             # أنماط CSS المشتركة
database/contact_messages_table.sql        # جدول رسائل التواصل
```

### الصفحات المحدثة:
```
public/admin/dashboard.php                 # لوحة التحكم الرئيسية
public/admin/contact_messages.php          # رسائل التواصل
public/admin/settings.php                  # إعدادات النظام
public/admin/profile.php                   # الملف الشخصي
public/admin/users.php                     # إدارة المستخدمين
public/admin/appointments.php              # إدارة المواعيد
public/admin/specialists.php               # إدارة الأخصائيين
public/admin/reports.php                   # التقارير
public/admin/content.php                   # إدارة المحتوى
```

## 🔧 كيفية الإضافة لصفحة جديدة

### 1. إضافة التحقق من الصلاحيات:
```php
$currentUser = $auth->getCurrentUser();
if (!$currentUser) {
    setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
    header('Location: /nafsi_platform/login');
    exit;
}

if ($currentUser['user_type'] !== 'admin') {
    setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    header('Location: /nafsi_platform/dashboard');
    exit;
}

// تضمين الشريط الجانبي
include_once 'includes/sidebar.php';
```

### 2. إضافة هيكل HTML:
```html
<div class="admin-main-content" id="adminMainContent">
    <div class="container-fluid">
        <!-- محتوى الصفحة هنا -->
    </div>
</div>
```

### 3. إضافة JavaScript لتحديث المحتوى:
```javascript
// تحديث المحتوى الرئيسي عند إخفاء/إظهار الشريط الجانبي
function updateMainContent() {
    const sidebar = document.getElementById('adminSidebar');
    const mainContent = document.getElementById('adminMainContent');
    
    if (sidebar && mainContent) {
        if (sidebar.classList.contains('collapsed')) {
            mainContent.classList.add('sidebar-collapsed');
        } else {
            mainContent.classList.remove('sidebar-collapsed');
        }
    }
}

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateMainContent();
});
```

## 🎨 تخصيص الشريط الجانبي

### إضافة رابط جديد:
```php
$navItems = [
    // ... الروابط الموجودة
    'new_page' => [
        'title' => 'اسم الصفحة الجديدة',
        'icon' => 'fas fa-icon-name',
        'url' => url('admin/new_page'),
        'active' => $currentPage === 'new_page'
    ]
];
```

### إضافة شارة تنبيه:
```php
<?php if ($key === 'your_page'): ?>
    <?php
    try {
        $db = Database::getInstance();
        $count = $db->select("SELECT COUNT(*) as count FROM your_table WHERE condition = 'value'");
        $badgeCount = $count[0]['count'] ?? 0;
        if ($badgeCount > 0):
    ?>
        <span class="notification-badge"><?= $badgeCount ?></span>
    <?php endif; ?>
    <?php } catch (Exception $e) {} ?>
<?php endif; ?>
```

## 📱 التصميم المتجاوب

### الشاشات الكبيرة:
- الشريط الجانبي بعرض 280px
- المحتوى الرئيسي مع هامش 280px من اليمين

### الشاشات الصغيرة:
- الشريط الجانبي يختفي تلقائياً
- المحتوى الرئيسي يأخذ العرض الكامل
- زر لإظهار/إخفاء الشريط الجانبي

## 🎯 الروابط المتاحة

| الصفحة | الرابط | الوصف |
|--------|--------|--------|
| لوحة التحكم | `/admin/dashboard` | نظرة عامة على النظام |
| إدارة المستخدمين | `/admin/users` | إدارة جميع المستخدمين |
| إدارة الأخصائيين | `/admin/specialists` | إدارة المعالجين |
| إدارة المواعيد | `/admin/appointments` | جدولة وإدارة المواعيد |
| إضافة موعد | `/admin/add_appointment` | إنشاء موعد جديد |
| رسائل التواصل | `/admin/contact_messages` | إدارة رسائل التواصل |
| إدارة المحتوى | `/admin/content` | إدارة المقالات والمحتوى |
| التقارير | `/admin/reports` | إحصائيات وتقارير |
| الإعدادات | `/admin/settings` | إعدادات النظام |
| الملف الشخصي | `/admin/profile` | تحديث معلومات المدير |

## 🔒 الأمان

### التحقق من الصلاحيات:
- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (admin)
- إعادة التوجيه في حالة عدم وجود صلاحيات

### حماية من الوصول المباشر:
```php
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}
```

## 🚀 الميزات المتقدمة

### شارة التنبيه للرسائل:
- عرض عدد الرسائل الجديدة
- تحديث تلقائي عند تحميل الصفحة
- تصميم جذاب مع لون أحمر

### حفظ الحالة:
- حفظ حالة الشريط الجانبي في localStorage
- استعادة الحالة عند إعادة تحميل الصفحة
- تجربة مستخدم محسنة

### التأثيرات البصرية:
- تأثيرات حركية عند التمرير
- تأثيرات عند النقر
- تصميم متدرج جذاب

## 📊 الإحصائيات

### الصفحات المحدثة:
- ✅ لوحة التحكم الرئيسية
- ✅ رسائل التواصل
- ✅ إعدادات النظام
- ✅ الملف الشخصي
- ✅ إدارة المستخدمين
- ✅ إدارة المواعيد
- ✅ إدارة الأخصائيين
- ✅ التقارير
- ✅ إدارة المحتوى

### الملفات المنشأة:
- ✅ الشريط الجانبي الرئيسي
- ✅ ملف CSS مشترك
- ✅ جدول رسائل التواصل
- ✅ سكريبت التثبيت

## 🎉 النتيجة النهائية

تم إنشاء شريط جانبي شامل ومتقدم للوحة تحكم المدير يوفر:

1. **تنقل سلس** بين جميع أقسام النظام
2. **تصميم عصري** مع تأثيرات بصرية متقدمة
3. **تجربة مستخدم محسنة** مع حفظ الحالة
4. **تصميم متجاوب** للشاشات المختلفة
5. **أمان محسن** مع فحوصات الصلاحيات
6. **وظائف ذكية** مثل شارات التنبيه

الشريط الجانبي الآن متاح في جميع صفحات لوحة تحكم المدير ويوفر تجربة مستخدم موحدة ومحسنة! 🚀 