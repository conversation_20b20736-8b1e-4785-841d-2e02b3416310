<?php
/**
 * ملف تشخيصي لفحص دالة asset
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

echo "<h2>تشخيص دالة asset</h2>";

echo "<h3>1. فحص دالة getAppUrl:</h3>";
echo "APP_URL: " . APP_URL . "<br>";
echo "getAppUrl(''): " . getAppUrl('') . "<br>";
echo "getAppUrl('test'): " . getAppUrl('test') . "<br>";

echo "<h3>2. فحص دالة asset:</h3>";
echo "asset('css/style.css'): " . asset('css/style.css') . "<br>";
echo "asset('js/main.js'): " . asset('js/main.js') . "<br>";

echo "<h3>3. المسارات المطلوبة:</h3>";
echo "المسار المطلوب لـ CSS: " . getAppUrl('public/assets/css/style.css') . "<br>";
echo "المسار المطلوب لـ JS: " . getAppUrl('public/assets/js/main.js') . "<br>";

echo "<h3>4. فحص وجود الملفات:</h3>";
$cssFile = PUBLIC_ROOT . '/assets/css/style.css';
$jsFile = PUBLIC_ROOT . '/assets/js/main.js';

echo "PUBLIC_ROOT: " . PUBLIC_ROOT . "<br>";
echo "ملف CSS موجود: " . (file_exists($cssFile) ? 'نعم' : 'لا') . " - " . $cssFile . "<br>";
echo "ملف JS موجود: " . (file_exists($jsFile) ? 'نعم' : 'لا') . " - " . $jsFile . "<br>";

echo "<h3>5. اختبار الوصول المباشر:</h3>";
echo "<a href='" . getAppUrl('public/assets/css/style.css') . "' target='_blank'>اختبار ملف CSS</a><br>";
echo "<a href='" . getAppUrl('public/assets/js/main.js') . "' target='_blank'>اختبار ملف JS</a><br>";
?> 