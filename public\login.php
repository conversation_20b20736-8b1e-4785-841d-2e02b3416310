<?php
/**
 * صفحة تسجيل الدخول الجديدة
 */

// التحقق من أن الصفحة تُحمل عبر Front Controller
if (!defined('NAFSI_APP')) {
    header('Location: /nafsi_platform/login');
    exit;
}

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تم نقل منطق التحقق من تسجيل الدخول إلى index.php لتجنب مشكلة headers already sent

// تعيين متغيرات الصفحة
$pageTitle = 'تسجيل الدخول';
$currentPage = 'login';
?>

<?php include 'includes/header.php'; ?>

<!-- Login Section -->
<section class="login-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-card">
                    <div class="login-header text-center mb-4">
                        <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                        <h2 class="text-primary mb-2">منصة نفسي</h2>
                        <p class="text-muted">تسجيل الدخول إلى حسابك</p>
                    </div>
                    
                    <?php
                    // عرض رسائل التنبيه
                    $alert = getAlert();
                    if ($alert):
                    ?>
                    <div class="alert alert-<?= $alert['type'] ?> alert-dismissible fade show mb-4">
                        <i class="fas fa-<?= $alert['type'] === 'success' ? 'check-circle' : ($alert['type'] === 'danger' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                        <?= $alert['message'] ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="/nafsi_platform/login" id="loginForm">
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
                        
                        <!-- البريد الإلكتروني -->
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                            </label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   placeholder="أدخل بريدك الإلكتروني"
                                   required>
                        </div>
                        
                        <!-- كلمة المرور -->
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <span class="input-group-text" id="togglePassword" style="cursor: pointer;">
                                    <i class="fas fa-eye"></i>
                                </span>
                            </div>
                        </div>
                        
                        <!-- تذكرني -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>
                        </div>
                        
                        <!-- زر تسجيل الدخول -->
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                <span id="loginText">تسجيل الدخول</span>
                                <span id="loginSpinner" class="spinner-border spinner-border-sm me-2 d-none"></span>
                            </button>
                        </div>
                        
                        <!-- روابط إضافية -->
                        <div class="text-center">
                            <a href="/nafsi_platform/register" class="text-decoration-none">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب جديد
                            </a>
                            <br>
                            <a href="/nafsi_platform/forgot-password" class="text-decoration-none mt-2 d-inline-block">
                                <i class="fas fa-key me-1"></i>نسيت كلمة المرور؟
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* تنسيق صفحة تسجيل الدخول */
.login-section {
    min-height: calc(100vh - 200px); /* ارتفاع الصفحة ناقص header و footer */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 2rem 0;
}

.login-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-left: none;
}

.form-control:focus + .input-group-text {
    border-color: #667eea;
}

.alert {
    border-radius: 10px;
    border: none;
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
        padding: 1.5rem;
    }
}
</style>

<script>
// Toggle Password Visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form Submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const loginText = document.getElementById('loginText');
    const loginSpinner = document.getElementById('loginSpinner');
    
    // Show loading state
    loginText.textContent = 'جاري تسجيل الدخول...';
    loginSpinner.classList.remove('d-none');
    submitBtn.disabled = true;
    
    // Re-enable after 5 seconds if no redirect happens
    setTimeout(() => {
        loginText.textContent = 'تسجيل الدخول';
        loginSpinner.classList.add('d-none');
        submitBtn.disabled = false;
    }, 5000);
});

// Auto-fill for testing (remove in production)
if (window.location.hostname === 'localhost') {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'test123';
}
</script>

<?php include 'includes/footer.php'; ?>
