<?php
/**
 * ملف تشخيصي لفحص قاعدة البيانات
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>تشخيص قاعدة البيانات</h2>";
    
    // فحص هيكل الجدول
    echo "<h3>1. فحص هيكل جدول users:</h3>";
    $columns = $db->select("DESCRIBE users");
    if ($columns === false) {
        echo "<p style='color: red;'>خطأ في فحص هيكل الجدول</p>";
    } else {
        echo "<p>أعمدة جدول users:</p>";
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        }
    }
    
    // فحص جميع المستخدمين
    echo "<h3>2. جميع المستخدمين:</h3>";
    $allUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users ORDER BY user_type, username");
    if ($allUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام جميع المستخدمين</p>";
    } else {
        echo "<p>إجمالي المستخدمين: " . count($allUsers) . "</p>";
        foreach ($allUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // فحص المعالجين فقط
    echo "<h3>3. المعالجين (user_type = 'specialist'):</h3>";
    $specialists = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE user_type = 'specialist'");
    if ($specialists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
    } elseif (empty($specialists)) {
        echo "<p style='color: orange;'>لا يوجد معالجين (user_type = 'specialist')</p>";
    } else {
        echo "<p>عدد المعالجين: " . count($specialists) . "</p>";
        foreach ($specialists as $specialist) {
            echo "- " . $specialist['first_name'] . ' ' . $specialist['last_name'] . " (" . $specialist['email'] . ")<br>";
        }
    }
    
    // فحص المستخدمين الذين يبدأ username بـ 'dr.'
    echo "<h3>4. المستخدمين الذين يبدأ username بـ 'dr.':</h3>";
    $drUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE username LIKE 'dr.%'");
    if ($drUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام المستخدمين dr.</p>";
    } elseif (empty($drUsers)) {
        echo "<p style='color: orange;'>لا يوجد مستخدمين يبدأ username بـ 'dr.'</p>";
    } else {
        echo "<p>عدد المستخدمين dr.: " . count($drUsers) . "</p>";
        foreach ($drUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // فحص العملاء
    echo "<h3>5. العملاء (user_type = 'regular'):</h3>";
    $regularUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE user_type = 'regular'");
    if ($regularUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام العملاء</p>";
    } elseif (empty($regularUsers)) {
        echo "<p style='color: orange;'>لا يوجد عملاء (user_type = 'regular')</p>";
    } else {
        echo "<p>عدد العملاء: " . count($regularUsers) . "</p>";
        foreach ($regularUsers as $user) {
            echo "- " . $user['first_name'] . ' ' . $user['last_name'] . " (" . $user['email'] . ")<br>";
        }
    }
    
    // فحص المستخدمين بدون user_type
    echo "<h3>6. المستخدمين بدون user_type:</h3>";
    $nullUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users WHERE user_type IS NULL OR user_type = ''");
    if ($nullUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام المستخدمين بدون user_type</p>";
    } elseif (empty($nullUsers)) {
        echo "<p style='color: green;'>لا يوجد مستخدمين بدون user_type</p>";
    } else {
        echo "<p>عدد المستخدمين بدون user_type: " . count($nullUsers) . "</p>";
        foreach ($nullUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 