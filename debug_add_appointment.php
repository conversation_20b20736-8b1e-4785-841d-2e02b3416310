<?php
/**
 * ملف تشخيصي لفحص صفحة إضافة الموعد
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>تشخيص صفحة إضافة الموعد</h2>";
    
    // اختبار 1: فحص استعلام العملاء
    echo "<h3>1. اختبار استعلام العملاء:</h3>";
    $clients = $db->select("
        SELECT id, first_name, last_name, email, phone 
        FROM users 
        WHERE (user_type = 'regular' OR user_type IS NULL OR user_type = '')
        ORDER BY first_name, last_name
    ");
    
    if ($clients === false) {
        echo "<p style='color: red;'>خطأ في استعلام العملاء</p>";
    } elseif (empty($clients)) {
        echo "<p style='color: orange;'>لا يوجد عملاء</p>";
    } else {
        echo "<p style='color: green;'>عدد العملاء: " . count($clients) . "</p>";
        foreach ($clients as $client) {
            echo "- " . $client['first_name'] . ' ' . $client['last_name'] . " (" . $client['email'] . ")<br>";
        }
    }
    
    // اختبار 2: فحص استعلام المعالجين
    echo "<h3>2. اختبار استعلام المعالجين:</h3>";
    $therapists = $db->select("
        SELECT id, first_name, last_name, email, phone, specialization
        FROM users 
        WHERE user_type = 'specialist'
        ORDER BY first_name, last_name
    ");
    
    if ($therapists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين</p>";
        // محاولة معرفة سبب الخطأ
        echo "<p>محاولة معرفة سبب الخطأ:</p>";
        $error = $db->getLastError();
        if ($error) {
            echo "<p style='color: red;'>خطأ SQL: " . $error . "</p>";
        }
    } elseif (empty($therapists)) {
        echo "<p style='color: orange;'>لا يوجد معالجين</p>";
    } else {
        echo "<p style='color: green;'>عدد المعالجين: " . count($therapists) . "</p>";
        foreach ($therapists as $therapist) {
            echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ") - " . ($therapist['specialization'] ?? 'بدون تخصص') . "<br>";
        }
    }
    
    // اختبار 3: فحص جميع المستخدمين
    echo "<h3>3. جميع المستخدمين:</h3>";
    $allUsers = $db->select("SELECT id, username, email, first_name, last_name, user_type FROM users ORDER BY user_type, username");
    if ($allUsers === false) {
        echo "<p style='color: red;'>خطأ في استعلام جميع المستخدمين</p>";
    } else {
        echo "<p>إجمالي المستخدمين: " . count($allUsers) . "</p>";
        foreach ($allUsers as $user) {
            echo "- " . $user['username'] . " (" . $user['email'] . ") - user_type: " . ($user['user_type'] ?? 'NULL') . "<br>";
        }
    }
    
    // اختبار 4: فحص المعالجين بطريقة أخرى
    echo "<h3>4. اختبار استعلام المعالجين بطريقة أخرى:</h3>";
    $specialists = $db->select("SELECT id, first_name, last_name, email, user_type FROM users WHERE user_type = 'specialist'");
    if ($specialists === false) {
        echo "<p style='color: red;'>خطأ في استعلام المعالجين (بدون specialization)</p>";
    } elseif (empty($specialists)) {
        echo "<p style='color: orange;'>لا يوجد معالجين (بدون specialization)</p>";
    } else {
        echo "<p style='color: green;'>عدد المعالجين: " . count($specialists) . "</p>";
        foreach ($specialists as $specialist) {
            echo "- " . $specialist['first_name'] . ' ' . $specialist['last_name'] . " (" . $specialist['email'] . ")<br>";
        }
    }
    
    // اختبار 5: فحص عمود specialization
    echo "<h3>5. فحص عمود specialization:</h3>";
    $columns = $db->select("DESCRIBE users");
    if ($columns === false) {
        echo "<p style='color: red;'>خطأ في فحص هيكل الجدول</p>";
    } else {
        $hasSpecialization = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'specialization') {
                $hasSpecialization = true;
                break;
            }
        }
        if ($hasSpecialization) {
            echo "<p style='color: green;'>عمود specialization موجود</p>";
        } else {
            echo "<p style='color: red;'>عمود specialization غير موجود</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 