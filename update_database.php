<?php
/**
 * تحديث قاعدة البيانات لإضافة حقل user_type
 * Database Update Script
 */

// تضمين ملف الإعدادات
require_once 'app/config.php';

try {
    $db = db();
    
    echo "بدء تحديث قاعدة البيانات...\n";
    
    // إضافة حقل user_type إلى جدول users
    $sql = "ALTER TABLE users ADD COLUMN user_type ENUM('regular', 'specialist', 'admin') DEFAULT 'regular' AFTER profile_image";
    $db->execute($sql);
    echo "✓ تم إضافة حقل user_type إلى جدول users\n";
    
    // تحديث المستخدم المشرف الموجود
    $sql = "UPDATE users SET user_type = 'admin' WHERE email = '<EMAIL>'";
    $db->execute($sql);
    echo "✓ تم تحديث المستخدم المشرف\n";
    
    // تحديث جميع المستخدمين الموجودين في جدول admins
    $sql = "UPDATE users u 
            JOIN admins a ON u.id = a.user_id 
            SET u.user_type = 'admin'";
    $db->execute($sql);
    echo "✓ تم تحديث جميع المشرفين\n";
    
    // تحديث جميع المستخدمين الموجودين في جدول specialists
    $sql = "UPDATE users u 
            JOIN specialists s ON u.id = s.user_id 
            SET u.user_type = 'specialist'";
    $db->execute($sql);
    echo "✓ تم تحديث جميع الأخصائيين\n";
    
    echo "\n✅ تم تحديث قاعدة البيانات بنجاح!\n";
    echo "يمكنك الآن تسجيل الدخول كأدمن وسيتم توجيهك إلى لوحة التحكم.\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "\n";
}
?> 