<?php
/**
 * كلاس المشرف
 * Admin Class
 * 
 * هذا الكلاس يتعامل مع جميع عمليات الإدارة
 * بما في ذلك إدارة المستخدمين، الأخصائيين، المحتوى،
 * التقارير، والإعدادات
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Admin {
    private $db;
    private $user;
    private $specialist;
    private $content;
    private $auth;
    
    /**
     * أنواع التقارير
     */
    const REPORT_TYPE_USERS = 'users';
    const REPORT_TYPE_SPECIALISTS = 'specialists';
    const REPORT_TYPE_CONTENT = 'content';
    const REPORT_TYPE_BOOKINGS = 'bookings';
    const REPORT_TYPE_REVENUE = 'revenue';
    
    /**
     * فترات التقارير
     */
    const PERIOD_DAILY = 'daily';
    const PERIOD_WEEKLY = 'weekly';
    const PERIOD_MONTHLY = 'monthly';
    const PERIOD_YEARLY = 'yearly';
    
    public function __construct() {
        $this->db = db();
        $this->user = new User();
        $this->specialist = new Specialist();
        $this->content = new Content();
        $this->auth = new Auth();
    }
    
    /**
     * التحقق من صلاحيات المشرف
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function isAdmin($userId) {
        $user = $this->user->getById($userId);
        return $user && $user['user_type'] === 'admin';
    }
    
    /**
     * الحصول على لوحة التحكم الرئيسية
     * @return array
     */
    public function getDashboard() {
        $dashboard = [];
        
        // إحصائيات المستخدمين
        $dashboard['users'] = $this->user->getStatistics();
        
        // إحصائيات الأخصائيين
        $dashboard['specialists'] = $this->specialist->getStatistics();
        
        // إحصائيات المحتوى
        $dashboard['content'] = $this->content->getStatistics();
        
        // إحصائيات الحجوزات
        $dashboard['bookings'] = $this->getBookingStatistics();
        
        // إحصائيات الإيرادات
        $dashboard['revenue'] = $this->getRevenueStatistics();
        
        // النشاطات الأخيرة
        $dashboard['recent_activities'] = $this->getRecentActivities();
        
        return $dashboard;
    }
    
    /**
     * إدارة المستخدمين
     */
    
    /**
     * الحصول على قائمة المستخدمين
     * @param array $filters معايير التصفية
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function getUsers($filters = [], $limit = 20, $offset = 0) {
        return $this->user->search($filters, $limit, $offset);
    }
    
    /**
     * تحديث حالة المستخدم
     * @param int $userId معرف المستخدم
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateUserStatus($userId, $status) {
        return $this->user->updateStatus($userId, $status);
    }
    
    /**
     * حذف المستخدم
     * @param int $userId معرف المستخدم
     * @return bool
     */
    public function deleteUser($userId) {
        return $this->user->delete($userId);
    }
    
    /**
     * إدارة الأخصائيين
     */
    
    /**
     * الحصول على قائمة الأخصائيين
     * @param array $filters معايير التصفية
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function getSpecialists($filters = [], $limit = 20, $offset = 0) {
        return $this->specialist->search($filters, $limit, $offset);
    }
    
    /**
     * تحديث حالة الأخصائي
     * @param int $specialistId معرف الأخصائي
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateSpecialistStatus($specialistId, $status) {
        return $this->specialist->updateStatus($specialistId, $status);
    }
    
    /**
     * التحقق من أوراق اعتماد الأخصائي
     * @param int $specialistId معرف الأخصائي
     * @param array $credentials الأوراق المطلوبة
     * @return bool
     */
    public function verifySpecialistCredentials($specialistId, $credentials) {
        try {
            $sql = "UPDATE specialists SET verification_status = :status, 
                    verified_at = NOW(), updated_at = NOW() 
                    WHERE id = :specialist_id";
            
            $params = [
                ':status' => 'verified',
                ':specialist_id' => $specialistId
            ];
            
            $result = $this->db->update($sql, $params);
            
            if ($result) {
                // تسجيل عملية التحقق
                $this->logAdminAction('verify_specialist', [
                    'specialist_id' => $specialistId,
                    'credentials' => $credentials
                ]);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Specialist Verification Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إدارة المحتوى
     */
    
    /**
     * الحصول على قائمة المحتوى
     * @param array $filters معايير التصفية
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function getContent($filters = [], $limit = 20, $offset = 0) {
        return $this->content->search($filters, $limit, $offset);
    }
    
    /**
     * تحديث حالة المحتوى
     * @param int $contentId معرف المحتوى
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateContentStatus($contentId, $status) {
        return $this->content->updateStatus($contentId, $status);
    }
    
    /**
     * حذف المحتوى
     * @param int $contentId معرف المحتوى
     * @return bool
     */
    public function deleteContent($contentId) {
        return $this->content->delete($contentId);
    }
    
    /**
     * إدارة التعليقات
     */
    
    /**
     * الحصول على التعليقات المعلقة
     * @param int $limit عدد النتائج
     * @return array
     */
    public function getPendingComments($limit = 50) {
        $sql = "SELECT cc.*, c.title as content_title, u.first_name, u.last_name 
                FROM content_comments cc 
                JOIN content c ON cc.content_id = c.id 
                JOIN users u ON cc.user_id = u.id 
                WHERE cc.status = :pending_status 
                ORDER BY cc.created_at DESC LIMIT :limit";
        
        return $this->db->select($sql, [
            ':pending_status' => 'pending',
            ':limit' => $limit
        ]);
    }
    
    /**
     * الموافقة على تعليق
     * @param int $commentId معرف التعليق
     * @return bool
     */
    public function approveComment($commentId) {
        $sql = "UPDATE content_comments SET status = :approved_status, 
                updated_at = NOW() WHERE id = :comment_id";
        
        $result = $this->db->update($sql, [
            ':approved_status' => 'approved',
            ':comment_id' => $commentId
        ]);
        
        if ($result) {
            $this->logAdminAction('approve_comment', ['comment_id' => $commentId]);
        }
        
        return $result;
    }
    
    /**
     * رفض تعليق
     * @param int $commentId معرف التعليق
     * @param string $reason سبب الرفض
     * @return bool
     */
    public function rejectComment($commentId, $reason = '') {
        $sql = "UPDATE content_comments SET status = :rejected_status, 
                rejection_reason = :reason, updated_at = NOW() 
                WHERE id = :comment_id";
        
        $result = $this->db->update($sql, [
            ':rejected_status' => 'rejected',
            ':reason' => $reason,
            ':comment_id' => $commentId
        ]);
        
        if ($result) {
            $this->logAdminAction('reject_comment', [
                'comment_id' => $commentId,
                'reason' => $reason
            ]);
        }
        
        return $result;
    }
    
    /**
     * إدارة التقارير
     */
    
    /**
     * إنشاء تقرير
     * @param string $type نوع التقرير
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    public function generateReport($type, $period = self::PERIOD_MONTHLY, $filters = []) {
        switch ($type) {
            case self::REPORT_TYPE_USERS:
                return $this->generateUsersReport($period, $filters);
            case self::REPORT_TYPE_SPECIALISTS:
                return $this->generateSpecialistsReport($period, $filters);
            case self::REPORT_TYPE_CONTENT:
                return $this->generateContentReport($period, $filters);
            case self::REPORT_TYPE_BOOKINGS:
                return $this->generateBookingsReport($period, $filters);
            case self::REPORT_TYPE_REVENUE:
                return $this->generateRevenueReport($period, $filters);
            default:
                return [];
        }
    }
    
    /**
     * إنشاء تقرير المستخدمين
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    private function generateUsersReport($period, $filters) {
        $report = [];
        
        // إجمالي المستخدمين
        $sql = "SELECT COUNT(*) as total FROM users";
        $result = $this->db->selectOne($sql);
        $report['total_users'] = $result['total'];
        
        // المستخدمين الجدد حسب الفترة
        $periodSql = $this->getPeriodSql($period);
        $sql = "SELECT COUNT(*) as new_users FROM users WHERE created_at >= $periodSql";
        $result = $this->db->selectOne($sql);
        $report['new_users'] = $result['new_users'];
        
        // توزيع أنواع المستخدمين
        $sql = "SELECT user_type, COUNT(*) as count FROM users GROUP BY user_type";
        $results = $this->db->select($sql);
        $report['user_types'] = $results;
        
        // توزيع الحالات
        $sql = "SELECT status, COUNT(*) as count FROM users GROUP BY status";
        $results = $this->db->select($sql);
        $report['user_statuses'] = $results;
        
        return $report;
    }
    
    /**
     * إنشاء تقرير الأخصائيين
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    private function generateSpecialistsReport($period, $filters) {
        $report = [];
        
        // إجمالي الأخصائيين
        $sql = "SELECT COUNT(*) as total FROM specialists";
        $result = $this->db->selectOne($sql);
        $report['total_specialists'] = $result['total'];
        
        // الأخصائيين النشطين
        $sql = "SELECT COUNT(*) as active FROM specialists WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'active']);
        $report['active_specialists'] = $result['active'];
        
        // الأخصائيين المصدق عليهم
        $sql = "SELECT COUNT(*) as verified FROM specialists WHERE verification_status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'verified']);
        $report['verified_specialists'] = $result['verified'];
        
        // متوسط التقييم
        $sql = "SELECT AVG(rating) as avg_rating FROM specialists WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'active']);
        $report['average_rating'] = round($result['avg_rating'] ?? 0, 2);
        
        return $report;
    }
    
    /**
     * إنشاء تقرير المحتوى
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    private function generateContentReport($period, $filters) {
        $report = [];
        
        // إجمالي المحتوى
        $sql = "SELECT COUNT(*) as total FROM content";
        $result = $this->db->selectOne($sql);
        $report['total_content'] = $result['total'];
        
        // المحتوى المنشور
        $sql = "SELECT COUNT(*) as published FROM content WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'published']);
        $report['published_content'] = $result['published'];
        
        // توزيع أنواع المحتوى
        $sql = "SELECT type, COUNT(*) as count FROM content GROUP BY type";
        $results = $this->db->select($sql);
        $report['content_types'] = $results;
        
        return $report;
    }
    
    /**
     * إنشاء تقرير الحجوزات
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    private function generateBookingsReport($period, $filters) {
        $report = [];
        
        // إجمالي الحجوزات
        $sql = "SELECT COUNT(*) as total FROM bookings";
        $result = $this->db->selectOne($sql);
        $report['total_bookings'] = $result['total'];
        
        // الحجوزات المؤكدة
        $sql = "SELECT COUNT(*) as confirmed FROM bookings WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'confirmed']);
        $report['confirmed_bookings'] = $result['confirmed'];
        
        // الحجوزات المعلقة
        $sql = "SELECT COUNT(*) as pending FROM bookings WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'pending']);
        $report['pending_bookings'] = $result['pending'];
        
        return $report;
    }
    
    /**
     * إنشاء تقرير الإيرادات
     * @param string $period فترة التقرير
     * @param array $filters معايير إضافية
     * @return array
     */
    private function generateRevenueReport($period, $filters) {
        $report = [];
        
        // إجمالي الإيرادات
        $sql = "SELECT SUM(amount) as total_revenue FROM payments WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'completed']);
        $report['total_revenue'] = $result['total_revenue'] ?? 0;
        
        // الإيرادات حسب الفترة
        $periodSql = $this->getPeriodSql($period);
        $sql = "SELECT SUM(amount) as period_revenue FROM payments 
                WHERE status = :status AND created_at >= $periodSql";
        $result = $this->db->selectOne($sql, [':status' => 'completed']);
        $report['period_revenue'] = $result['period_revenue'] ?? 0;
        
        return $report;
    }
    
    /**
     * الحصول على SQL للفترة المحددة
     * @param string $period فترة التقرير
     * @return string
     */
    private function getPeriodSql($period) {
        switch ($period) {
            case self::PERIOD_DAILY:
                return "DATE_SUB(NOW(), INTERVAL 1 DAY)";
            case self::PERIOD_WEEKLY:
                return "DATE_SUB(NOW(), INTERVAL 1 WEEK)";
            case self::PERIOD_MONTHLY:
                return "DATE_SUB(NOW(), INTERVAL 1 MONTH)";
            case self::PERIOD_YEARLY:
                return "DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            default:
                return "DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        }
    }
    
    /**
     * إدارة النظام
     */
    
    /**
     * الحصول على إحصائيات النظام
     * @return array
     */
    public function getSystemStatistics() {
        $stats = [];
        
        // إحصائيات قاعدة البيانات
        $stats['database'] = $this->getDatabaseStatistics();
        
        // إحصائيات الجلسات
        $session = new Session();
        $stats['sessions'] = $session->getSessionStatistics();
        
        // إحصائيات الأداء
        $stats['performance'] = $this->getPerformanceStatistics();
        
        return $stats;
    }
    
    /**
     * الحصول على إحصائيات قاعدة البيانات
     * @return array
     */
    private function getDatabaseStatistics() {
        $stats = [];
        
        // حجم قاعدة البيانات
        $sql = "SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'database_size_mb'
                FROM information_schema.tables 
                WHERE table_schema = :db_name";
        
        $result = $this->db->selectOne($sql, [':db_name' => DB_NAME]);
        $stats['database_size_mb'] = $result['database_size_mb'] ?? 0;
        
        // عدد الجداول
        $sql = "SELECT COUNT(*) as table_count FROM information_schema.tables 
                WHERE table_schema = :db_name";
        
        $result = $this->db->selectOne($sql, [':db_name' => DB_NAME]);
        $stats['table_count'] = $result['table_count'];
        
        return $stats;
    }
    
    /**
     * الحصول على إحصائيات الأداء
     * @return array
     */
    private function getPerformanceStatistics() {
        $stats = [];
        
        // وقت الاستجابة
        $startTime = microtime(true);
        $this->db->selectOne("SELECT 1");
        $endTime = microtime(true);
        $stats['response_time_ms'] = round(($endTime - $startTime) * 1000, 2);
        
        // استخدام الذاكرة
        $stats['memory_usage_mb'] = round(memory_get_usage(true) / 1024 / 1024, 2);
        
        // استخدام الذاكرة القصوى
        $stats['peak_memory_mb'] = round(memory_get_peak_usage(true) / 1024 / 1024, 2);
        
        return $stats;
    }
    
    /**
     * إدارة الإعدادات
     */
    
    /**
     * الحصول على إعدادات النظام
     * @return array
     */
    public function getSystemSettings() {
        $sql = "SELECT * FROM system_settings ORDER BY setting_key";
        return $this->db->select($sql);
    }
    
    /**
     * تحديث إعداد النظام
     * @param string $key مفتاح الإعداد
     * @param mixed $value القيمة الجديدة
     * @return bool
     */
    public function updateSystemSetting($key, $value) {
        $sql = "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                VALUES (:key, :value, NOW()) 
                ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()";
        
        $result = $this->db->execute($sql, [
            ':key' => $key,
            ':value' => $value
        ]);
        
        if ($result) {
            $this->logAdminAction('update_setting', [
                'setting_key' => $key,
                'setting_value' => $value
            ]);
        }
        
        return $result;
    }
    
    /**
     * الحصول على قيمة إعداد
     * @param string $key مفتاح الإعداد
     * @param mixed $default القيمة الافتراضية
     * @return mixed
     */
    public function getSystemSetting($key, $default = null) {
        $sql = "SELECT setting_value FROM system_settings WHERE setting_key = :key";
        $result = $this->db->selectOne($sql, [':key' => $key]);
        
        return $result ? $result['setting_value'] : $default;
    }
    
    /**
     * إدارة النسخ الاحتياطي
     */
    
    /**
     * إنشاء نسخة احتياطية
     * @param string $backupPath مسار النسخة الاحتياطية
     * @return bool
     */
    public function createBackup($backupPath = null) {
        try {
            if (!$backupPath) {
                $backupPath = 'backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
            }
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            $backupDir = dirname($backupPath);
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }
            
            // أمر إنشاء النسخة الاحتياطية
            $command = sprintf(
                'mysqldump -h %s -u %s -p%s %s > %s',
                DB_HOST,
                DB_USER,
                DB_PASS,
                DB_NAME,
                $backupPath
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->logAdminAction('create_backup', ['backup_path' => $backupPath]);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Backup Creation Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * استعادة نسخة احتياطية
     * @param string $backupPath مسار النسخة الاحتياطية
     * @return bool
     */
    public function restoreBackup($backupPath) {
        try {
            if (!file_exists($backupPath)) {
                throw new Exception('ملف النسخة الاحتياطية غير موجود');
            }
            
            // أمر استعادة النسخة الاحتياطية
            $command = sprintf(
                'mysql -h %s -u %s -p%s %s < %s',
                DB_HOST,
                DB_USER,
                DB_PASS,
                DB_NAME,
                $backupPath
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                $this->logAdminAction('restore_backup', ['backup_path' => $backupPath]);
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Backup Restoration Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على النشاطات الأخيرة
     * @param int $limit عدد النتائج
     * @return array
     */
    public function getRecentActivities($limit = 20) {
        $sql = "SELECT * FROM admin_activities ORDER BY created_at DESC LIMIT :limit";
        return $this->db->select($sql, [':limit' => $limit]);
    }
    
    /**
     * تسجيل نشاط إداري
     * @param string $action النشاط
     * @param array $data بيانات إضافية
     */
    private function logAdminAction($action, $data = []) {
        $currentUser = $this->auth->getCurrentUser();
        $userId = $currentUser ? $currentUser['id'] : 0;
        
        $sql = "INSERT INTO admin_activities (admin_id, action, data, ip_address, created_at) 
                VALUES (:admin_id, :action, :data, :ip_address, NOW())";
        
        $params = [
            ':admin_id' => $userId,
            ':action' => $action,
            ':data' => json_encode($data),
            ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        $this->db->insert($sql, $params);
    }
    
    /**
     * الحصول على إحصائيات الحجوزات
     * @return array
     */
    private function getBookingStatistics() {
        $stats = [];
        
        // إجمالي الحجوزات
        $sql = "SELECT COUNT(*) as total FROM bookings";
        $result = $this->db->selectOne($sql);
        $stats['total_bookings'] = $result['total'];
        
        // الحجوزات المؤكدة
        $sql = "SELECT COUNT(*) as confirmed FROM bookings WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'confirmed']);
        $stats['confirmed_bookings'] = $result['confirmed'];
        
        // الحجوزات المعلقة
        $sql = "SELECT COUNT(*) as pending FROM bookings WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'pending']);
        $stats['pending_bookings'] = $result['pending'];
        
        return $stats;
    }
    
    /**
     * الحصول على إحصائيات الإيرادات
     * @return array
     */
    private function getRevenueStatistics() {
        $stats = [];
        
        // إجمالي الإيرادات
        $sql = "SELECT SUM(amount) as total FROM payments WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => 'completed']);
        $stats['total_revenue'] = $result['total'] ?? 0;
        
        // الإيرادات هذا الشهر
        $sql = "SELECT SUM(amount) as monthly FROM payments 
                WHERE status = :status AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        $result = $this->db->selectOne($sql, [':status' => 'completed']);
        $stats['monthly_revenue'] = $result['monthly'] ?? 0;
        
        return $stats;
    }
}
?> 