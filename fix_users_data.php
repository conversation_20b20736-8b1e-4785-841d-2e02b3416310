<?php
/**
 * ملف لتحديث البيانات الموجودة في جدول users
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

try {
    $db = db();
    
    echo "<h2>تحديث البيانات في جدول users</h2>";
    
    // تحديث العملاء (المستخدمين الذين يبدأ username بـ mariam, ali, nada, mohammed, reem, ahmed_user, sara_user, omar_user, layla_user, khalid_user)
    echo "<h3>تحديث العملاء:</h3>";
    
    $clientUsernames = ['mariam', 'ali', 'nada', 'mohammed', 'reem', 'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user'];
    
    foreach ($clientUsernames as $username) {
        $result = $db->update("users", 
            ["user_type" => "user", "status" => "active"], 
            "username = ?", 
            [$username]
        );
        
        if ($result) {
            echo "<p style='color: green;'>تم تحديث المستخدم: $username</p>";
        } else {
            echo "<p style='color: orange;'>لم يتم تحديث المستخدم: $username (قد لا يكون موجوداً)</p>";
        }
    }
    
    // تحديث المعالجين (المستخدمين الذين يبدأ username بـ dr.)
    echo "<h3>تحديث المعالجين:</h3>";
    
    $result = $db->update("users", 
        ["user_type" => "therapist", "status" => "active"], 
        "username LIKE 'dr.%'", 
        []
    );
    
    if ($result) {
        echo "<p style='color: green;'>تم تحديث المعالجين</p>";
    } else {
        echo "<p style='color: orange;'>لم يتم تحديث المعالجين</p>";
    }
    
    // عرض النتائج بعد التحديث
    echo "<h3>النتائج بعد التحديث:</h3>";
    
    // العملاء
    $clients = $db->select("
        SELECT id, first_name, last_name, email, user_type, status 
        FROM users 
        WHERE (user_type = 'user' OR user_type IS NULL) AND status = 'active'
        ORDER BY first_name, last_name
    ");
    
    echo "<h4>العملاء (" . count($clients) . "):</h4>";
    foreach ($clients as $client) {
        echo "- " . $client['first_name'] . ' ' . $client['last_name'] . " (" . $client['email'] . ")<br>";
    }
    
    // المعالجين
    $therapists = $db->select("
        SELECT id, first_name, last_name, email, user_type, status, specialization
        FROM users 
        WHERE user_type = 'therapist' AND status = 'active'
        ORDER BY first_name, last_name
    ");
    
    echo "<h4>المعالجين (" . count($therapists) . "):</h4>";
    foreach ($therapists as $therapist) {
        echo "- " . $therapist['first_name'] . ' ' . $therapist['last_name'] . " (" . $therapist['email'] . ")<br>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>تم تحديث البيانات بنجاح!</p>";
    echo "<p>يمكنك الآن الوصول إلى صفحة إضافة المواعيد وستجد العملاء والمعالجين متاحين.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . $e->getMessage() . "</p>";
}
?> 