<?php
/**
 * الدوال المساعدة للتطبيق
 * Helper Functions
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

/**
 * تنظيف وتأمين المدخلات
 */
function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function isStrongPassword($password) {
    // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
    return strlen($password) >= 8 && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => PASSWORD_COST]);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * إنشاء رمز تحقق للبريد الإلكتروني
 */
function generateVerificationCode($length = 6) {
    return str_pad(rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    return $date->format($format);
}

/**
 * تنسيق التاريخ باللغة العربية
 */
function formatDateArabic($date) {
    $months = [
        'January' => 'يناير',
        'February' => 'فبراير',
        'March' => 'مارس',
        'April' => 'أبريل',
        'May' => 'مايو',
        'June' => 'يونيو',
        'July' => 'يوليو',
        'August' => 'أغسطس',
        'September' => 'سبتمبر',
        'October' => 'أكتوبر',
        'November' => 'نوفمبر',
        'December' => 'ديسمبر'
    ];
    
    $date = new DateTime($date);
    $month = $months[$date->format('F')];
    return $date->format('j') . ' ' . $month . ' ' . $date->format('Y');
}

/**
 * تحويل الوقت إلى نص نسبي
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'الآن';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ $minutes دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ $hours ساعة";
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return "منذ $days يوم";
    } else {
        return formatDateArabic($datetime);
    }
}

/**
 * تنسيق المبلغ المالي
 */
function formatMoney($amount, $currency = 'SAR') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * تحويل حجم الملف إلى نص مقروء
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * التحقق من نوع الملف المسموح
 */
function isAllowedFile($filename, $allowedExtensions = null) {
    if ($allowedExtensions === null) {
        $allowedExtensions = ALLOWED_EXTENSIONS;
    }
    
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedExtensions);
}

/**
 * رفع ملف آمن
 */
function uploadFile($file, $destination, $allowedExtensions = null) {
    if (!isset($file['error']) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    if (!isAllowedFile($file['name'], $allowedExtensions)) {
        return false;
    }
    
    $filename = generateToken(16) . '_' . time() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    $filepath = $destination . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filename;
    }
    
    return false;
}

/**
 * حذف ملف
 */
function deleteFile($filepath) {
    if (file_exists($filepath)) {
        return unlink($filepath);
    }
    return false;
}

/**
 * إنشاء رسالة تنبيه
 */
function setAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * التحقق من وجود رسالة تنبيه
 */
function hasAlert() {
    return isset($_SESSION['alert']);
}

/**
 * الحصول على رسالة التنبيه
 */
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    // إذا كان الرابط فارغاً، توجيه للصفحة الرئيسية
    if (empty($url)) {
        header("Location: " . getAppUrl());
        exit;
    }
    
    // إذا كان الرابط يبدأ بـ /، إضافة URL التطبيق
    if (strpos($url, '/') === 0) {
        header("Location: " . getAppUrl(ltrim($url, '/')));
        exit;
    }
    
    // إذا كان الرابط يحتوي على .php، إزالة .php
    $url = str_replace('.php', '', $url);
    
    // إذا كان الرابط يحتوي على http أو https، إعادة توجيه مباشرة
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        header("Location: " . $url);
        exit;
    }
    
    header("Location: " . getAppUrl($url));
    exit;
}

/**
 * التحقق من أن المستخدم مسجل دخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * الحصول على معرف المستخدم الحالي
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * الحصول على نوع المستخدم الحالي
 */
function getCurrentUserType() {
    return $_SESSION['user_type'] ?? null;
}

/**
 * الحصول على بيانات المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $userId = getCurrentUserId();
    if (!$userId) {
        return null;
    }
    
    // محاولة الحصول من الجلسة أولاً
    if (isset($_SESSION['user_data'])) {
        return $_SESSION['user_data'];
    }
    
    // الحصول من قاعدة البيانات
    try {
        $db = db();
        $sql = "SELECT * FROM users WHERE id = ? AND status = 'active'";
        $user = $db->selectOne($sql, [$userId]);
        
        if ($user) {
            // حفظ في الجلسة للاستخدام اللاحق
            $_SESSION['user_data'] = $user;
            return $user;
        }
    } catch (Exception $e) {
        logError('Error getting current user: ' . $e->getMessage());
    }
    
    return null;
}

/**
 * التحقق من صلاحيات المستخدم
 */
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $userType = getCurrentUserType();
    
    switch ($permission) {
        case 'admin':
            return $userType === 'admin';
        case 'specialist':
            return in_array($userType, ['admin', 'specialist']);
        case 'user':
            return in_array($userType, ['admin', 'specialist', 'user']);
        default:
            return false;
    }
}

/**
 * تسجيل الأخطاء
 */
function logError($message, $context = []) {
    if (LOG_ERRORS) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'message' => $message,
            'context' => $context,
            'user_id' => getCurrentUserId(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        $logFile = LOG_PATH . 'errors.log';
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
}

/**
 * إنشاء CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * تنظيف البيانات المدخلة
 * @param string $data البيانات
 * @return string
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * التحقق من كون الطلب AJAX
 * @return bool
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}







/**
 * التحقق من CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * تنظيف النص من HTML
 */
function stripHtml($text) {
    return strip_tags($text);
}

/**
 * تقصير النص
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * إنشاء slug للنص
 */
function createSlug($text) {
    // تحويل النص إلى أحرف صغيرة
    $text = mb_strtolower($text, 'UTF-8');
    
    // استبدال الأحرف العربية بأرقام
    $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
    $english = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'dh', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
    $text = str_replace($arabic, $english, $text);
    
    // إزالة الأحرف الخاصة
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    
    // استبدال المسافات بشرطة
    $text = preg_replace('/\s+/', '-', $text);
    
    // إزالة الشرطات المتكررة
    $text = preg_replace('/-+/', '-', $text);
    
    return trim($text, '-');
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone($phone) {
    // تنسيق رقم الهاتف السعودي: +966XXXXXXXXX أو 05XXXXXXXX
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) === 10 && substr($phone, 0, 1) === '0') {
        return true;
    }
    
    if (strlen($phone) === 12 && substr($phone, 0, 3) === '966') {
        return true;
    }
    
    return false;
}

/**
 * تنسيق رقم الهاتف السعودي
 */
function formatSaudiPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) === 10 && substr($phone, 0, 1) === '0') {
        return '+966' . substr($phone, 1);
    }
    
    if (strlen($phone) === 12 && substr($phone, 0, 3) === '966') {
        return '+' . $phone;
    }
    
    return $phone;
}

/**
 * الحصول على رابط لوحة التحكم المناسب حسب نوع المستخدم
 */
function getDashboardUrl() {
    if (!isLoggedIn()) {
        return url('login');
    }
    
    $userType = getCurrentUserType();
    
    switch ($userType) {
        case 'admin':
            return url('admin/dashboard');
        case 'therapist':
            return url('therapist/dashboard');
        case 'user':
        default:
            return url('dashboard');
    }
} 