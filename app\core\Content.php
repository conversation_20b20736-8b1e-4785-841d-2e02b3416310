<?php
/**
 * كلاس المحتوى
 * Content Class
 * 
 * هذا الكلاس يتعامل مع إدارة المحتوى
 * بما في ذلك المقالات، الفيديوهات، التصنيفات،
 * نظام البحث، والتعليقات
 */

// منع الوصول المباشر للملف
if (!defined('NAFSI_APP')) {
    die('Access Denied');
}

class Content {
    private $db;
    
    /**
     * أنواع المحتوى
     */
    const TYPE_ARTICLE = 'article';
    const TYPE_VIDEO = 'video';
    const TYPE_AUDIO = 'audio';
    const TYPE_DOCUMENT = 'document';
    
    /**
     * حالات المحتوى
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';
    const STATUS_DELETED = 'deleted';
    
    /**
     * مستويات الوصول
     */
    const ACCESS_PUBLIC = 'public';
    const ACCESS_PRIVATE = 'private';
    const ACCESS_MEMBERS = 'members';
    const ACCESS_SPECIALISTS = 'specialists';
    
    public function __construct() {
        $this->db = db();
    }
    
    /**
     * إنشاء محتوى جديد
     * @param array $data بيانات المحتوى
     * @return bool|int معرف المحتوى أو false في حالة الفشل
     */
    public function create($data) {
        try {
            // التحقق من صحة البيانات
            if (!$this->validateContentData($data)) {
                return false;
            }
            
            // إعداد البيانات الافتراضية
            $data['status'] = $data['status'] ?? self::STATUS_DRAFT;
            $data['access_level'] = $data['access_level'] ?? self::ACCESS_PUBLIC;
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "INSERT INTO content (title, slug, content, excerpt, type, category_id, 
                    author_id, status, access_level, featured_image, meta_title, meta_description, 
                    tags, created_at, updated_at) 
                    VALUES (:title, :slug, :content, :excerpt, :type, :category_id, 
                    :author_id, :status, :access_level, :featured_image, :meta_title, 
                    :meta_description, :tags, :created_at, :updated_at)";
            
            $params = [
                ':title' => $data['title'],
                ':slug' => $this->generateSlug($data['title']),
                ':content' => $data['content'],
                ':excerpt' => $data['excerpt'] ?? $this->generateExcerpt($data['content']),
                ':type' => $data['type'],
                ':category_id' => $data['category_id'] ?? null,
                ':author_id' => $data['author_id'],
                ':status' => $data['status'],
                ':access_level' => $data['access_level'],
                ':featured_image' => $data['featured_image'] ?? null,
                ':meta_title' => $data['meta_title'] ?? $data['title'],
                ':meta_description' => $data['meta_description'] ?? $data['excerpt'] ?? $this->generateExcerpt($data['content']),
                ':tags' => json_encode($data['tags'] ?? []),
                ':created_at' => $data['created_at'],
                ':updated_at' => $data['updated_at']
            ];
            
            $result = $this->db->insert($sql, $params);
            
            if ($result) {
                $contentId = $this->db->getConnection()->lastInsertId();
                
                // إضافة التصنيفات إذا كانت موجودة
                if (isset($data['categories']) && is_array($data['categories'])) {
                    $this->addContentCategories($contentId, $data['categories']);
                }
                
                return $contentId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Content Create Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث المحتوى
     * @param int $contentId معرف المحتوى
     * @param array $data البيانات المحدثة
     * @return bool
     */
    public function update($contentId, $data) {
        try {
            // التحقق من وجود المحتوى
            if (!$this->exists($contentId)) {
                throw new Exception('المحتوى غير موجود');
            }
            
            // التحقق من صحة البيانات
            if (!$this->validateUpdateData($data)) {
                return false;
            }
            
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE content SET ";
            $params = [];
            $updates = [];
            
            $allowedFields = ['title', 'content', 'excerpt', 'category_id', 'status', 
                            'access_level', 'featured_image', 'meta_title', 'meta_description', 
                            'tags', 'updated_at'];
            
            foreach ($data as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    if ($field === 'tags') {
                        $value = json_encode($value);
                    }
                    $updates[] = "$field = :$field";
                    $params[":$field"] = $value;
                }
            }
            
            if (empty($updates)) {
                return false;
            }
            
            $sql .= implode(', ', $updates);
            $sql .= " WHERE id = :content_id";
            $params[':content_id'] = $contentId;
            
            $result = $this->db->update($sql, $params);
            
            // تحديث التصنيفات إذا كانت موجودة
            if (isset($data['categories']) && is_array($data['categories'])) {
                $this->updateContentCategories($contentId, $data['categories']);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Content Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على المحتوى بالمعرف
     * @param int $contentId معرف المحتوى
     * @param bool $includeAuthor تضمين بيانات المؤلف
     * @return array|false
     */
    public function getById($contentId, $includeAuthor = false) {
        $sql = "SELECT c.*, cat.name as category_name";
        
        if ($includeAuthor) {
            $sql .= ", u.first_name, u.last_name, u.email as author_email";
        }
        
        $sql .= " FROM content c 
                  LEFT JOIN categories cat ON c.category_id = cat.id";
        
        if ($includeAuthor) {
            $sql .= " LEFT JOIN users u ON c.author_id = u.id";
        }
        
        $sql .= " WHERE c.id = :content_id";
        
        $result = $this->db->selectOne($sql, [':content_id' => $contentId]);
        
        if ($result) {
            $result['tags'] = json_decode($result['tags'], true);
            $result['categories'] = $this->getContentCategories($contentId);
        }
        
        return $result;
    }
    
    /**
     * الحصول على المحتوى بالرابط
     * @param string $slug الرابط
     * @return array|false
     */
    public function getBySlug($slug) {
        $sql = "SELECT c.*, cat.name as category_name, u.first_name, u.last_name 
                FROM content c 
                LEFT JOIN categories cat ON c.category_id = cat.id 
                LEFT JOIN users u ON c.author_id = u.id 
                WHERE c.slug = :slug AND c.status = :status";
        
        $result = $this->db->selectOne($sql, [
            ':slug' => $slug,
            ':status' => self::STATUS_PUBLISHED
        ]);
        
        if ($result) {
            $result['tags'] = json_decode($result['tags'], true);
            $result['categories'] = $this->getContentCategories($result['id']);
        }
        
        return $result;
    }
    
    /**
     * البحث في المحتوى
     * @param array $filters معايير البحث
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function search($filters = [], $limit = 20, $offset = 0) {
        $sql = "SELECT c.*, cat.name as category_name, u.first_name, u.last_name 
                FROM content c 
                LEFT JOIN categories cat ON c.category_id = cat.id 
                LEFT JOIN users u ON c.author_id = u.id 
                WHERE c.status = :published_status";
        
        $params = [':published_status' => self::STATUS_PUBLISHED];
        
        if (isset($filters['type'])) {
            $sql .= " AND c.type = :type";
            $params[':type'] = $filters['type'];
        }
        
        if (isset($filters['category_id'])) {
            $sql .= " AND c.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }
        
        if (isset($filters['author_id'])) {
            $sql .= " AND c.author_id = :author_id";
            $params[':author_id'] = $filters['author_id'];
        }
        
        if (isset($filters['access_level'])) {
            $sql .= " AND c.access_level = :access_level";
            $params[':access_level'] = $filters['access_level'];
        }
        
        if (isset($filters['search'])) {
            $sql .= " AND (c.title LIKE :search OR c.content LIKE :search OR c.excerpt LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        if (isset($filters['tags'])) {
            $sql .= " AND JSON_CONTAINS(c.tags, :tags)";
            $params[':tags'] = json_encode($filters['tags']);
        }
        
        $sql .= " ORDER BY c.created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;
        
        $results = $this->db->select($sql, $params);
        
        // تحويل البيانات JSON
        foreach ($results as &$result) {
            $result['tags'] = json_decode($result['tags'], true);
        }
        
        return $results;
    }
    
    /**
     * الحصول على المحتوى المميز
     * @param int $limit عدد النتائج
     * @return array
     */
    public function getFeatured($limit = 10) {
        $sql = "SELECT c.*, cat.name as category_name, u.first_name, u.last_name 
                FROM content c 
                LEFT JOIN categories cat ON c.category_id = cat.id 
                LEFT JOIN users u ON c.author_id = u.id 
                WHERE c.status = :published_status AND c.featured = 1 
                ORDER BY c.created_at DESC LIMIT :limit";
        
        $results = $this->db->select($sql, [
            ':published_status' => self::STATUS_PUBLISHED,
            ':limit' => $limit
        ]);
        
        foreach ($results as &$result) {
            $result['tags'] = json_decode($result['tags'], true);
        }
        
        return $results;
    }
    
    /**
     * الحصول على المحتوى حسب النوع
     * @param string $type نوع المحتوى
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function getByType($type, $limit = 20, $offset = 0) {
        return $this->search(['type' => $type], $limit, $offset);
    }
    
    /**
     * الحصول على المحتوى حسب التصنيف
     * @param int $categoryId معرف التصنيف
     * @param int $limit عدد النتائج
     * @param int $offset الإزاحة
     * @return array
     */
    public function getByCategory($categoryId, $limit = 20, $offset = 0) {
        return $this->search(['category_id' => $categoryId], $limit, $offset);
    }
    
    /**
     * تحديث حالة المحتوى
     * @param int $contentId معرف المحتوى
     * @param string $status الحالة الجديدة
     * @return bool
     */
    public function updateStatus($contentId, $status) {
        $validStatuses = [self::STATUS_DRAFT, self::STATUS_PUBLISHED, 
                         self::STATUS_ARCHIVED, self::STATUS_DELETED];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }
        
        $sql = "UPDATE content SET status = :status, updated_at = :updated_at 
                WHERE id = :content_id";
        
        $params = [
            ':status' => $status,
            ':updated_at' => date('Y-m-d H:i:s'),
            ':content_id' => $contentId
        ];
        
        return $this->db->update($sql, $params);
    }
    
    /**
     * حذف المحتوى
     * @param int $contentId معرف المحتوى
     * @return bool
     */
    public function delete($contentId) {
        // حذف منطقي - تحديث الحالة بدلاً من الحذف الفعلي
        return $this->updateStatus($contentId, self::STATUS_DELETED);
    }
    
    /**
     * إضافة تعليق
     * @param array $data بيانات التعليق
     * @return bool|int معرف التعليق أو false في حالة الفشل
     */
    public function addComment($data) {
        try {
            // التحقق من صحة البيانات
            if (!$this->validateCommentData($data)) {
                return false;
            }
            
            $sql = "INSERT INTO content_comments (content_id, user_id, parent_id, comment, 
                    status, created_at) VALUES (:content_id, :user_id, :parent_id, :comment, 
                    :status, NOW())";
            
            $params = [
                ':content_id' => $data['content_id'],
                ':user_id' => $data['user_id'],
                ':parent_id' => $data['parent_id'] ?? null,
                ':comment' => $data['comment'],
                ':status' => $data['status'] ?? 'approved'
            ];
            
            $result = $this->db->insert($sql, $params);
            
            if ($result) {
                return $this->db->getConnection()->lastInsertId();
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Comment Add Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على تعليقات المحتوى
     * @param int $contentId معرف المحتوى
     * @param bool $includeReplies تضمين الردود
     * @return array
     */
    public function getComments($contentId, $includeReplies = true) {
        $sql = "SELECT cc.*, u.first_name, u.last_name, u.profile_image 
                FROM content_comments cc 
                LEFT JOIN users u ON cc.user_id = u.id 
                WHERE cc.content_id = :content_id AND cc.status = :approved_status";
        
        if (!$includeReplies) {
            $sql .= " AND cc.parent_id IS NULL";
        }
        
        $sql .= " ORDER BY cc.created_at ASC";
        
        $results = $this->db->select($sql, [
            ':content_id' => $contentId,
            ':approved_status' => 'approved'
        ]);
        
        if ($includeReplies) {
            // تنظيم التعليقات في شكل هرمي
            $comments = [];
            $replies = [];
            
            foreach ($results as $comment) {
                if ($comment['parent_id']) {
                    $replies[$comment['parent_id']][] = $comment;
                } else {
                    $comments[] = $comment;
                }
            }
            
            // إضافة الردود للتعليقات
            foreach ($comments as &$comment) {
                $comment['replies'] = $replies[$comment['id']] ?? [];
            }
            
            return $comments;
        }
        
        return $results;
    }
    
    /**
     * إضافة تقييم للمحتوى
     * @param int $contentId معرف المحتوى
     * @param int $userId معرف المستخدم
     * @param int $rating التقييم (1-5)
     * @return bool
     */
    public function addRating($contentId, $userId, $rating) {
        try {
            if ($rating < 1 || $rating > 5) {
                return false;
            }
            
            // التحقق من عدم وجود تقييم مسبق
            $sql = "SELECT id FROM content_ratings WHERE content_id = :content_id AND user_id = :user_id";
            $existing = $this->db->selectOne($sql, [
                ':content_id' => $contentId,
                ':user_id' => $userId
            ]);
            
            if ($existing) {
                // تحديث التقييم الموجود
                $sql = "UPDATE content_ratings SET rating = :rating, updated_at = NOW() 
                        WHERE content_id = :content_id AND user_id = :user_id";
            } else {
                // إضافة تقييم جديد
                $sql = "INSERT INTO content_ratings (content_id, user_id, rating, created_at) 
                        VALUES (:content_id, :user_id, :rating, NOW())";
            }
            
            $params = [
                ':content_id' => $contentId,
                ':user_id' => $userId,
                ':rating' => $rating
            ];
            
            return $this->db->execute($sql, $params);
            
        } catch (Exception $e) {
            error_log("Rating Add Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على متوسط تقييم المحتوى
     * @param int $contentId معرف المحتوى
     * @return float
     */
    public function getAverageRating($contentId) {
        $sql = "SELECT AVG(rating) as avg_rating FROM content_ratings WHERE content_id = :content_id";
        $result = $this->db->selectOne($sql, [':content_id' => $contentId]);
        
        return round($result['avg_rating'] ?? 0, 2);
    }
    
    /**
     * التحقق من وجود المحتوى
     * @param int $contentId معرف المحتوى
     * @return bool
     */
    public function exists($contentId) {
        $sql = "SELECT COUNT(*) as count FROM content WHERE id = :content_id";
        $result = $this->db->selectOne($sql, [':content_id' => $contentId]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * الحصول على إحصائيات المحتوى
     * @return array
     */
    public function getStatistics() {
        $stats = [];
        
        // إجمالي المحتوى
        $sql = "SELECT COUNT(*) as total FROM content";
        $result = $this->db->selectOne($sql);
        $stats['total_content'] = $result['total'];
        
        // المحتوى المنشور
        $sql = "SELECT COUNT(*) as published FROM content WHERE status = :status";
        $result = $this->db->selectOne($sql, [':status' => self::STATUS_PUBLISHED]);
        $stats['published_content'] = $result['published'];
        
        // توزيع أنواع المحتوى
        $sql = "SELECT type, COUNT(*) as count FROM content GROUP BY type";
        $results = $this->db->select($sql);
        $stats['content_types'] = $results;
        
        // متوسط التقييم
        $sql = "SELECT AVG(rating) as avg_rating FROM content_ratings";
        $result = $this->db->selectOne($sql);
        $stats['average_rating'] = round($result['avg_rating'] ?? 0, 2);
        
        return $stats;
    }
    
    /**
     * توليد رابط فريد
     * @param string $title العنوان
     * @return string
     */
    private function generateSlug($title) {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // التحقق من عدم وجود الرابط مسبقاً
        $baseSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * التحقق من وجود الرابط
     * @param string $slug الرابط
     * @return bool
     */
    private function slugExists($slug) {
        $sql = "SELECT COUNT(*) as count FROM content WHERE slug = :slug";
        $result = $this->db->selectOne($sql, [':slug' => $slug]);
        return $result && $result['count'] > 0;
    }
    
    /**
     * توليد ملخص من المحتوى
     * @param string $content المحتوى
     * @param int $length الطول المطلوب
     * @return string
     */
    private function generateExcerpt($content, $length = 150) {
        $excerpt = strip_tags($content);
        $excerpt = trim($excerpt);
        
        if (strlen($excerpt) <= $length) {
            return $excerpt;
        }
        
        $excerpt = substr($excerpt, 0, $length);
        $excerpt = substr($excerpt, 0, strrpos($excerpt, ' '));
        
        return $excerpt . '...';
    }
    
    /**
     * إضافة تصنيفات للمحتوى
     * @param int $contentId معرف المحتوى
     * @param array $categoryIds معرفات التصنيفات
     */
    private function addContentCategories($contentId, $categoryIds) {
        foreach ($categoryIds as $categoryId) {
            $sql = "INSERT INTO content_categories (content_id, category_id) 
                    VALUES (:content_id, :category_id)";
            $this->db->insert($sql, [
                ':content_id' => $contentId,
                ':category_id' => $categoryId
            ]);
        }
    }
    
    /**
     * تحديث تصنيفات المحتوى
     * @param int $contentId معرف المحتوى
     * @param array $categoryIds معرفات التصنيفات
     */
    private function updateContentCategories($contentId, $categoryIds) {
        // حذف التصنيفات الحالية
        $sql = "DELETE FROM content_categories WHERE content_id = :content_id";
        $this->db->execute($sql, [':content_id' => $contentId]);
        
        // إضافة التصنيفات الجديدة
        $this->addContentCategories($contentId, $categoryIds);
    }
    
    /**
     * الحصول على تصنيفات المحتوى
     * @param int $contentId معرف المحتوى
     * @return array
     */
    private function getContentCategories($contentId) {
        $sql = "SELECT c.* FROM categories c 
                JOIN content_categories cc ON c.id = cc.category_id 
                WHERE cc.content_id = :content_id";
        
        return $this->db->select($sql, [':content_id' => $contentId]);
    }
    
    /**
     * التحقق من صحة بيانات المحتوى
     * @param array $data البيانات
     * @return bool
     */
    private function validateContentData($data) {
        // التحقق من الحقول المطلوبة
        $required = ['title', 'content', 'type', 'author_id'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return false;
            }
        }
        
        // التحقق من صحة النوع
        $validTypes = [self::TYPE_ARTICLE, self::TYPE_VIDEO, self::TYPE_AUDIO, self::TYPE_DOCUMENT];
        if (!in_array($data['type'], $validTypes)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة بيانات التحديث
     * @param array $data البيانات
     * @return bool
     */
    private function validateUpdateData($data) {
        // التحقق من صحة النوع إذا كان موجوداً
        if (isset($data['type'])) {
            $validTypes = [self::TYPE_ARTICLE, self::TYPE_VIDEO, self::TYPE_AUDIO, self::TYPE_DOCUMENT];
            if (!in_array($data['type'], $validTypes)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة بيانات التعليق
     * @param array $data البيانات
     * @return bool
     */
    private function validateCommentData($data) {
        // التحقق من الحقول المطلوبة
        $required = ['content_id', 'user_id', 'comment'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return false;
            }
        }
        
        return true;
    }
}
?> 