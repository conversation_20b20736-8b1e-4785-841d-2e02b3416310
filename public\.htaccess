# منصة نفسي - إعدادات Apache للمجلد العام

# إعادة توجيه الملفات الثابتة
RewriteEngine On

# إعادة توجيه ملفات الأصول إلى المجلد الصحيح
RewriteRule ^assets/(.*)$ public/assets/$1 [L]

# إعادة توجيه الصفحات إلى index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# إعدادات MIME Types
AddType text/css .css
AddType application/javascript .js
AddType image/png .png
AddType image/jpg .jpg
AddType image/jpeg .jpeg
AddType image/gif .gif
AddType image/svg+xml .svg
AddType image/x-icon .ico
AddType font/woff .woff
AddType font/woff2 .woff2
AddType font/ttf .ttf
AddType font/eot .eot

# إعدادات الأمان
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# منع الوصول لملفات النظام
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files> 