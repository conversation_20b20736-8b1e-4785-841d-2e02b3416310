<?php
/**
 * Create system_settings table if it doesn't exist
 */

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'nafsi_platform';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "Connected to database successfully\n";
    
    // Check if system_settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_settings'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "system_settings table already exists\n";
        
        // Check if it has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_settings");
        $count = $stmt->fetch()['count'];
        echo "Table has $count settings\n";
        
        if ($count == 0) {
            echo "Inserting default settings...\n";
            
            // Insert default settings
            $defaultSettings = [
                ['site_name', 'منصة نفسي', 'اسم الموقع'],
                ['site_description', 'منصة متخصصة في الاستشارات النفسية عبر الإنترنت', 'وصف الموقع'],
                ['maintenance_mode', 'false', 'وضع الصيانة'],
                ['registration_enabled', 'true', 'تفعيل التسجيل'],
                ['email_verification_required', 'true', 'التحقق من البريد الإلكتروني مطلوب'],
                ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
                ['session_timeout', '3600', 'مهلة الجلسة بالثواني'],
                ['default_currency', 'SAR', 'العملة الافتراضية'],
                ['min_session_duration', '30', 'الحد الأدنى لمدة الجلسة بالدقائق'],
                ['max_session_duration', '120', 'الحد الأقصى لمدة الجلسة بالدقائق']
            ];
            
            $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            
            echo "Default settings inserted successfully\n";
        }
    } else {
        echo "Creating system_settings table...\n";
        
        // Create the table
        $createTableSQL = "
        CREATE TABLE system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($createTableSQL);
        echo "Table created successfully\n";
        
        // Insert default settings
        echo "Inserting default settings...\n";
        
        $defaultSettings = [
            ['site_name', 'منصة نفسي', 'اسم الموقع'],
            ['site_description', 'منصة متخصصة في الاستشارات النفسية عبر الإنترنت', 'وصف الموقع'],
            ['maintenance_mode', 'false', 'وضع الصيانة'],
            ['registration_enabled', 'true', 'تفعيل التسجيل'],
            ['email_verification_required', 'true', 'التحقق من البريد الإلكتروني مطلوب'],
            ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
            ['session_timeout', '3600', 'مهلة الجلسة بالثواني'],
            ['default_currency', 'SAR', 'العملة الافتراضية'],
            ['min_session_duration', '30', 'الحد الأدنى لمدة الجلسة بالدقائق'],
            ['max_session_duration', '120', 'الحد الأقصى لمدة الجلسة بالدقائق']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
        echo "Default settings inserted successfully\n";
    }
    
    echo "Done!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
} 