-- تحديث البيانات في جدول users
-- Update users data

-- تحديث العملاء (المستخدمين الذين يبدأ username بـ mariam, ali, nada, mohammed, reem, ahmed_user, sara_user, omar_user, layla_user, khalid_user)
UPDATE users SET user_type = 'regular' WHERE username IN ('mariam', 'ali', 'nada', 'mohammed', 'reem', 'ahmed_user', 'sara_user', 'omar_user', 'layla_user', 'khalid_user');

-- تحديث المعالجين
UPDATE users SET user_type = 'specialist' WHERE username LIKE 'dr.%';

-- عرض النتائج
SELECT 
    id,
    username,
    email,
    first_name,
    last_name,
    user_type
FROM users 
ORDER BY user_type, first_name, last_name; 