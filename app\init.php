<?php
/**
 * ملف تهيئة التطبيق
 * Application Initialization
 */

// تعريف ثابت التطبيق (إذا لم يكن معرفاً مسبقاً)
if (!defined('NAFSI_APP')) {
    define('NAFSI_APP', true);
}

// تعريف مجلد الملفات العامة
if (!defined('PUBLIC_ROOT')) {
    define('PUBLIC_ROOT', __DIR__ . '/../public');
}

// إعدادات الجلسة (قبل بدء الجلسة)
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في الإنتاج
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تحميل ملفات التكوين والدوال
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

// تحميل كلاسات النواة
require_once __DIR__ . '/core/User.php';
require_once __DIR__ . '/core/Specialist.php';
require_once __DIR__ . '/core/Admin.php';
require_once __DIR__ . '/core/Session.php';
require_once __DIR__ . '/core/Content.php';
require_once __DIR__ . '/core/Auth.php';
require_once __DIR__ . '/core/JWT.php';

// إعداد معالج الأخطاء
if (isDebugMode()) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return;
    }
    
    $error = [
        'severity' => $severity,
        'message' => $message,
        'file' => $file,
        'line' => $line
    ];
    
    logError('PHP Error: ' . $message, $error);
    
    if (isDebugMode()) {
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
});

// معالج الاستثناءات
set_exception_handler(function($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    logError('Uncaught Exception: ' . $exception->getMessage(), $error);
    
    if (isDebugMode()) {
        echo '<h1>خطأ في التطبيق</h1>';
        echo '<p><strong>الرسالة:</strong> ' . $exception->getMessage() . '</p>';
        echo '<p><strong>الملف:</strong> ' . $exception->getFile() . '</p>';
        echo '<p><strong>السطر:</strong> ' . $exception->getLine() . '</p>';
        echo '<h2>التتبع:</h2>';
        echo '<pre>' . $exception->getTraceAsString() . '</pre>';
    } else {
        http_response_code(500);
        echo '<h1>خطأ في الخادم</h1>';
        echo '<p>عذراً، حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>';
    }
    
    exit;
});

// معالج الأخطاء القاتلة
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        logError('Fatal Error: ' . $error['message'], $error);
        
        if (isDebugMode()) {
            echo '<h1>خطأ قاتل</h1>';
            echo '<p><strong>الرسالة:</strong> ' . $error['message'] . '</p>';
            echo '<p><strong>الملف:</strong> ' . $error['file'] . '</p>';
            echo '<p><strong>السطر:</strong> ' . $error['line'] . '</p>';
        } else {
            http_response_code(500);
            echo '<h1>خطأ في الخادم</h1>';
            echo '<p>عذراً، حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>';
        }
    }
});

// دالة لتحميل الصفحة
function loadPage($page, $data = []) {
    // استخراج المتغيرات للاستخدام في الصفحة
    extract($data);
    
    // تحميل رأس الصفحة
    if (file_exists(PUBLIC_ROOT . '/includes/header.php')) {
        include PUBLIC_ROOT . '/includes/header.php';
    }
    
    // تحميل الصفحة المطلوبة
    $pagePath = PUBLIC_ROOT . '/' . $page . '.php';
    if (file_exists($pagePath)) {
        include $pagePath;
    } else {
        // صفحة 404
        http_response_code(404);
        include PUBLIC_ROOT . '/404.php';
    }
    
    // تحميل تذييل الصفحة
    if (file_exists(PUBLIC_ROOT . '/includes/footer.php')) {
        include PUBLIC_ROOT . '/includes/footer.php';
    }
}

// دالة لتحميل مكون
function loadComponent($component, $data = []) {
    extract($data);
    $componentPath = PUBLIC_ROOT . '/components/' . $component . '.php';
    if (file_exists($componentPath)) {
        include $componentPath;
    }
}

// دالة لتحميل نموذج
function loadView($view, $data = []) {
    extract($data);
    $viewPath = PUBLIC_ROOT . '/views/' . $view . '.php';
    if (file_exists($viewPath)) {
        include $viewPath;
    }
}

// دالة للتحقق من وجود الملف
function asset($path) {
    // استخدام مسار مباشر بدلاً من إعادة التوجيه
    return getAppUrl('assets/' . ltrim($path, '/'));
}

// دالة للحصول على URL الصفحة
function url($path = '') {
    // إذا كان المسار يحتوي على .php، نزيله
    $path = str_replace('.php', '', $path);
    return getAppUrl($path);
}

// دالة للتحقق من الصلاحيات (استخدم Auth class للوظائف المتقدمة)
function requireAuth() {
    if (!isLoggedIn()) {
        setAlert('يجب تسجيل الدخول للوصول لهذه الصفحة', 'warning');
        redirect('login');
    }
}

function requirePermission($permission) {
    requireAuth();
    if (!hasPermission($permission)) {
        setAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
        redirect('dashboard');
    }
}

// تهيئة قاعدة البيانات
try {
    $db = db();
} catch (Exception $e) {
    if (isDebugMode()) {
        die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
    } else {
        die('خطأ في الاتصال بقاعدة البيانات');
    }
}

// إعدادات إضافية
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// إذا كان المستخدم مسجل دخول، تحديث آخر نشاط
if (isLoggedIn()) {
    $userId = getCurrentUserId();
    $userType = getCurrentUserType();
    
    // تحديث آخر نشاط للمستخدم
    $sql = "UPDATE users SET last_activity = NOW() WHERE id = ?";
    db()->update($sql, [$userId]);
} 