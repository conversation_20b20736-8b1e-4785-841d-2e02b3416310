/* ملف CSS مشترك للوحة تحكم المدير */
/* Admin Dashboard Shared CSS */

/* التخطيط الأساسي */
body {
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    overflow-x: hidden;
}

/* المحتوى الرئيسي */
.admin-main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
    background: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.admin-main-content.sidebar-collapsed {
    margin-right: 70px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .admin-main-content.sidebar-collapsed {
        margin-right: 0;
    }
}

/* تحسينات للبطاقات */
.admin-card {
    border-radius: 15px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.admin-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* تحسينات للرؤوس */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* تحسينات للإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

/* تحسينات للأزرار */
.btn-admin {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسينات للنماذج */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسينات للجداول */
.admin-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* تحسينات للقوائم */
.admin-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-list li:last-child {
    border-bottom: none;
}

.admin-list .label {
    font-weight: 600;
    color: #495057;
}

.admin-list .value {
    color: #6c757d;
}

/* ===== الشريط الجانبي المحسن ===== */
.admin-sidebar {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.admin-sidebar.collapsed {
    width: 70px;
}

.admin-sidebar.collapsed .admin-sidebar-header h4,
.admin-sidebar.collapsed .admin-sidebar-header small,
.admin-sidebar.collapsed .nav-link span,
.admin-sidebar.collapsed .user-details,
.admin-sidebar.collapsed .logout-btn span {
    display: none;
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.admin-sidebar.collapsed .nav-link i {
    margin-left: 0;
    font-size: 1.2rem;
}

.admin-sidebar.collapsed .user-info {
    justify-content: center;
    padding: 0.5rem;
}

.admin-sidebar.collapsed .user-avatar {
    margin-left: 0;
}

.admin-sidebar-header {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
    flex-shrink: 0;
}

.admin-sidebar-header h4 {
    color: white;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.admin-sidebar-header .logo-icon {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.admin-sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    color: rgba(255,255,255,0.8) !important;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1);
    transform: translateX(-5px);
}

.nav-link.active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white !important;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.nav-link i {
    width: 20px;
    margin-left: 0.75rem;
    text-align: center;
    transition: all 0.3s ease;
}

.nav-link span {
    flex: 1;
    transition: all 0.3s ease;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

/* شارات التنبيه */
.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

/* معلومات المستخدم */
.admin-sidebar-footer {
    padding: 1rem;
    background: rgba(0,0,0,0.2);
    border-top: 1px solid rgba(255,255,255,0.1);
    flex-shrink: 0;
}

.user-info {
    display: flex;
    align-items: center;
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #3498db;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    transition: all 0.3s ease;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin: 0;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
    margin: 0;
}

.logout-btn {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-top: 0.5rem;
}

.logout-btn:hover {
    background: #e74c3c;
    color: white;
    text-decoration: none;
}

/* زر إخفاء/إظهار الشريط الجانبي */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 290px;
    z-index: 1001;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    border: none;
    background: #3498db;
    color: white;
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.sidebar-toggle:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.sidebar-toggle.collapsed {
    right: 80px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        width: 280px;
    }
    
    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .admin-sidebar.collapsed {
        width: 70px;
    }
    
    .sidebar-toggle {
        right: 20px;
        top: 20px;
    }
    
    .sidebar-toggle.collapsed {
        right: 20px;
    }
    
    .admin-main-content {
        margin-right: 0;
        padding: 1rem;
    }
    
    .admin-main-content.sidebar-collapsed {
        margin-right: 0;
    }
}

/* تحسينات للطباعة */
@media print {
    .admin-sidebar,
    .sidebar-toggle {
        display: none;
    }
    
    .admin-main-content {
        margin-right: 0;
        padding: 0;
    }
    
    .admin-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .btn {
        display: none;
    }
}

/* تحسينات للأداء */
* {
    box-sizing: border-box;
}

/* تحسين الخطوط */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    font-display: swap;
} 