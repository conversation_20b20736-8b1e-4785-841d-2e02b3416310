/* ملف CSS مشترك للوحة تحكم المدير */
/* Admin Dashboard Shared CSS */

/* التخطيط الأساسي */
body {
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    overflow-x: hidden;
}

/* المحتوى الرئيسي */
.admin-main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
    overflow-x: hidden;
}

.admin-main-content.sidebar-collapsed {
    margin-right: 70px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .admin-main-content.sidebar-collapsed {
        margin-right: 0;
    }
}

/* تحسينات إضافية للتصميم */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* تحسين الانتقالات */
* {
    box-sizing: border-box;
}

.admin-sidebar * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إضافة تأثير الضبابية للخلفية عند فتح الشريط الجانبي على الشاشات الصغيرة */
.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* تحسينات للبطاقات */
.admin-card {
    border-radius: 15px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.admin-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* تحسينات للرؤوس */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* تحسينات للإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

/* تحسينات للأزرار */
.btn-admin {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسينات للنماذج */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسينات للجداول */
.admin-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* تحسينات للقوائم */
.admin-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-list li:last-child {
    border-bottom: none;
}

.admin-list .label {
    font-weight: 600;
    color: #495057;
}

.admin-list .value {
    color: #6c757d;
}

/* ===== الشريط الجانبي المحسن ===== */
.admin-sidebar {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: -5px 0 20px rgba(102, 126, 234, 0.15);
    display: flex;
    flex-direction: column;
    border-left: 3px solid rgba(255, 255, 255, 0.1);
}

.admin-sidebar.collapsed {
    width: 70px;
}

.admin-sidebar.collapsed .admin-sidebar-header h4,
.admin-sidebar.collapsed .admin-sidebar-header small,
.admin-sidebar.collapsed .nav-link span,
.admin-sidebar.collapsed .user-details,
.admin-sidebar.collapsed .logout-btn span {
    opacity: 0;
    visibility: hidden;
    transform: translateX(20px);
}

.admin-sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.admin-sidebar.collapsed .nav-link i {
    margin-left: 0;
    font-size: 1.2rem;
}

.admin-sidebar.collapsed .user-info {
    justify-content: center;
    padding: 0.5rem;
}

.admin-sidebar.collapsed .user-avatar {
    margin-left: 0;
}

.admin-sidebar-header {
    background: rgba(255,255,255,0.15);
    padding: 2rem 1rem;
    border-bottom: 2px solid rgba(255,255,255,0.2);
    text-align: center;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.admin-sidebar-header h4 {
    color: white;
    margin: 0.5rem 0 0 0;
    font-size: 1.3rem;
    font-weight: 700;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.admin-sidebar-header small {
    color: rgba(255,255,255,0.8);
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.admin-sidebar-header .logo-icon {
    font-size: 2.5rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.admin-sidebar-nav {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
}

.nav-item {
    margin: 0.25rem 1rem;
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
    padding: 0.875rem 1rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 0.25rem;
    backdrop-filter: blur(5px);
}

.nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.2);
    transform: translateX(-8px);
    box-shadow: 0 4px 15px rgba(255,255,255,0.1);
}

.nav-link.active {
    background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.9) 100%);
    color: #667eea !important;
    box-shadow: 0 6px 20px rgba(255,255,255,0.3);
    font-weight: 600;
}

.nav-link i {
    width: 22px;
    margin-left: 0.75rem;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.nav-link.active i {
    color: #667eea;
    transform: scale(1.1);
}

.nav-link span {
    flex: 1;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.nav-link:hover::before {
    left: 100%;
}

/* شارات التنبيه */
.notification-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(238, 90, 82, 0.4);
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
}

/* معلومات المستخدم */
.admin-sidebar-footer {
    padding: 1.5rem 1rem;
    background: rgba(0,0,0,0.3);
    border-top: 2px solid rgba(255,255,255,0.2);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.user-info {
    display: flex;
    align-items: center;
    color: white;
    padding: 0.75rem;
    border-radius: 12px;
    background: rgba(255,255,255,0.15);
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
}

.user-info:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-size: 1.1rem;
    color: #667eea;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(255,255,255,0.2);
}

.user-details {
    flex: 1;
    transition: all 0.3s ease;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.9;
    margin: 0;
    color: rgba(255,255,255,0.8);
}

.logout-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(238, 90, 82, 0.3);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #ee5a52 0%, #ff6b6b 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(238, 90, 82, 0.4);
}

.logout-btn i {
    margin-left: 0.5rem;
}

/* زر إخفاء/إظهار الشريط الجانبي */
.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 290px;
    z-index: 1001;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.sidebar-toggle:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: scale(1.1) rotate(180deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.sidebar-toggle.collapsed {
    right: 80px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        width: 300px;
        box-shadow: -10px 0 30px rgba(102, 126, 234, 0.3);
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
    }

    .admin-sidebar.collapsed {
        width: 70px;
        transform: translateX(100%);
    }

    .admin-sidebar.collapsed.mobile-open {
        transform: translateX(0);
    }

    .sidebar-toggle {
        right: 20px;
        top: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .sidebar-toggle.collapsed {
        right: 20px;
    }

    .admin-main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .admin-main-content.sidebar-collapsed {
        margin-right: 0;
    }

    /* تحسين النصوص على الشاشات الصغيرة */
    .admin-sidebar-header h4 {
        font-size: 1.1rem;
    }

    .nav-link {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .nav-link i {
        font-size: 1.2rem;
        width: 25px;
    }
}

/* تحسينات للطباعة */
@media print {
    .admin-sidebar,
    .sidebar-toggle {
        display: none;
    }

    .admin-main-content {
        margin-right: 0;
        padding: 0;
    }

    .admin-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    .btn {
        display: none;
    }
}

/* تحسينات إضافية للأداء والجمال */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* تأثيرات حركية إضافية */
.nav-item {
    animation: slideInRight 0.3s ease forwards;
    opacity: 0;
    transform: translateX(30px);
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }
.nav-item:nth-child(6) { animation-delay: 0.6s; }
.nav-item:nth-child(7) { animation-delay: 0.7s; }
.nav-item:nth-child(8) { animation-delay: 0.8s; }
.nav-item:nth-child(9) { animation-delay: 0.9s; }
.nav-item:nth-child(10) { animation-delay: 1.0s; }

@keyframes slideInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسينات للأداء */
* {
    box-sizing: border-box;
}

/* تحسين الخطوط */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    font-display: swap;
} 