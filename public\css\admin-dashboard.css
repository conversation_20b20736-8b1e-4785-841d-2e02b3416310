/* ===== تنسيقات لوحة تحكم المدير ===== */

/* إعدادات عامة */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    direction: rtl;
    overflow-x: hidden;
}

/* تحسينات إضافية للوحة التحكم */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    padding: 2rem;
}

.admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.7s;
}

.admin-header:hover::before {
    left: 100%;
}

.admin-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* بطاقات الإحصائيات */
.stats-card {
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
    background: white;
    margin-bottom: 1.5rem;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.stats-card .card-body {
    padding: 2rem;
}

.stats-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.stats-icon.users {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.therapists {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.appointments {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.messages {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.stats-change {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    display: inline-block;
}

.stats-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.stats-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.chart-container h3 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
    position: relative;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.table-container .table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
}

.table-container .table-header h3 {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
    font-size: 1.3rem;
}

.table-responsive {
    border-radius: 0 0 20px 20px;
}

.table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border: none;
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
}

.table td {
    padding: 1rem 1.5rem;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* التنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.1) 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(111, 66, 193, 0.1) 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .admin-header h1 {
        font-size: 2rem;
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .chart-container,
    .table-container {
        padding: 1.5rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}
