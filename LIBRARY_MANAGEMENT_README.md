# نظام إدارة المكتبة - منصة نفسي

## نظرة عامة

تم إنشاء نظام شامل لإدارة المكتبة في لوحة الإدارة، يتيح للمديرين إدارة المواد التعليمية والكتب والموارد المختلفة.

## الميزات الرئيسية

### 📚 إدارة المواد
- إضافة مواد جديدة (كتب، فيديوهات، ملفات صوتية، روابط)
- تعديل المواد الموجودة
- حذف المواد
- أرشفة المواد

### 🔍 البحث والتصفية
- البحث في العنوان والوصف والمؤلف
- تصفية حسب التصنيف
- تصفية حسب نوع الملف
- تصفية حسب الحالة

### 📊 الإحصائيات
- إحصائيات شاملة للمكتبة
- عدد التحميلات والمشاهدات
- تقييمات المستخدمين
- رسوم بيانية للتحميلات

### ⭐ نظام التقييم
- تقييم المواد من 1 إلى 5 نجوم
- إضافة مراجعات نصية
- عرض متوسط التقييمات

### 🏷️ التصنيف والعلامات
- تصنيفات متعددة (علم النفس، العلاج النفسي، إلخ)
- نظام علامات مرن
- دعم متعدد اللغات

## هيكل قاعدة البيانات

### جدول `library`
```sql
CREATE TABLE library (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    author VARCHAR(100),
    publisher VARCHAR(100),
    isbn VARCHAR(20),
    publication_year INT,
    language VARCHAR(50) DEFAULT 'Arabic',
    category VARCHAR(100),
    tags JSON,
    file_type ENUM('pdf', 'doc', 'docx', 'epub', 'video', 'audio', 'link') NOT NULL,
    file_url VARCHAR(500),
    file_size INT,
    thumbnail VARCHAR(255),
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_ratings INT DEFAULT 0,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    is_free BOOLEAN DEFAULT TRUE,
    price DECIMAL(10,2) DEFAULT 0.00,
    created_by INT,
    approved_by INT,
    approved_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### جداول إضافية
- `library_ratings`: تقييمات المستخدمين
- `library_downloads`: سجل التحميلات
- `library_views`: سجل المشاهدات

## الصفحات المتاحة

### 1. صفحة إدارة المكتبة الرئيسية
**الملف:** `public/admin/library.php`
- عرض جميع المواد
- إحصائيات شاملة
- أدوات البحث والتصفية
- إجراءات سريعة (موافقة، رفض، تمييز، إلخ)

### 2. صفحة إضافة مادة جديدة
**الملف:** `public/admin/library_add.php`
- نموذج شامل لإضافة المواد
- دعم أنواع ملفات متعددة
- معاينة نوع الملف
- إدارة السعر والحالة

### 3. صفحة تعديل المادة
**الملف:** `public/admin/library_edit.php`
- تعديل جميع بيانات المادة
- عرض إحصائيات المادة
- تحديث الحالة والإعدادات

### 4. صفحة عرض تفاصيل المادة
**الملف:** `public/admin/library_view.php`
- عرض تفاصيل شاملة للمادة
- إحصائيات التحميلات والمشاهدات
- آخر التقييمات
- رسوم بيانية للتحميلات

## التثبيت والإعداد

### 1. إنشاء جداول قاعدة البيانات
```bash
php database/install_library_tables.php
```

### 2. التحقق من الصلاحيات
تأكد من أن المستخدم لديه صلاحيات المدير للوصول لصفحات المكتبة.

### 3. إعداد الروابط
أضف رابط المكتبة في قائمة لوحة الإدارة:
```php
<a href="<?= url('admin/library') ?>" class="nav-link">
    <i class="fas fa-book-open"></i>
    المكتبة
</a>
```

## أنواع الملفات المدعومة

- **PDF**: ملفات PDF
- **DOC/DOCX**: ملفات Word
- **EPUB**: كتب إلكترونية
- **Video**: ملفات فيديو
- **Audio**: ملفات صوتية
- **Link**: روابط خارجية

## التصنيفات المتاحة

- علم النفس
- العلاج النفسي
- التنمية البشرية
- العلاقات
- الأسرة
- الأطفال
- المراهقة
- القلق والاكتئاب
- الإدمان
- أخرى

## الإجراءات المتاحة

### إدارة الحالة
- **موافقة**: تفعيل المادة
- **رفض**: إلغاء تفعيل المادة
- **أرشفة**: نقل المادة للأرشيف

### إدارة التمييز
- **تمييز**: جعل المادة مميزة
- **إلغاء التمييز**: إلغاء تمييز المادة

### إدارة المحتوى
- **تعديل**: تعديل بيانات المادة
- **حذف**: حذف المادة نهائياً

## الأمان والصلاحيات

- التحقق من تسجيل الدخول
- التحقق من صلاحيات المدير
- حماية من CSRF
- تنظيف المدخلات
- التحقق من صحة البيانات

## التخصيص والتطوير

### إضافة تصنيفات جديدة
```php
// في ملف library_add.php و library_edit.php
<option value="تصنيف جديد">تصنيف جديد</option>
```

### إضافة أنواع ملفات جديدة
```sql
-- تحديث ENUM في قاعدة البيانات
ALTER TABLE library MODIFY COLUMN file_type ENUM('pdf', 'doc', 'docx', 'epub', 'video', 'audio', 'link', 'new_type') NOT NULL;
```

### إضافة حقول جديدة
```sql
ALTER TABLE library ADD COLUMN new_field VARCHAR(255) AFTER existing_field;
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات قاعدة البيانات في `app/config/config.php`

2. **خطأ في الصلاحيات**
   - تأكد من أن المستخدم لديه `user_type = 'admin'`

3. **خطأ في إنشاء الجداول**
   - تأكد من وجود جدول `users` قبل إنشاء جداول المكتبة

### سجلات الأخطاء
راجع ملفات السجلات في مجلد `logs/` للاطلاع على الأخطاء المفصلة.

## الدعم والمساعدة

للمساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الملف أولاً
2. تحقق من سجلات الأخطاء
3. تأكد من إعدادات قاعدة البيانات
4. تحقق من صلاحيات الملفات

## التحديثات المستقبلية

- [ ] نظام تعليقات متقدم
- [ ] نظام تنبيهات للمواد الجديدة
- [ ] دعم الملفات المرفوعة مباشرة
- [ ] نظام نسخ احتياطي للمكتبة
- [ ] تصدير واستيراد البيانات
- [ ] نظام توصيات ذكي
- [ ] دعم الكتب التفاعلية
- [ ] نظام اشتراكات متقدم

---

**تم تطوير هذا النظام بواسطة فريق منصة نفسي**
**آخر تحديث:** <?= date('Y-m-d') ?> 