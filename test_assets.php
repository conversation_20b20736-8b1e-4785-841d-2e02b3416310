<?php
/**
 * ملف اختبار المسارات الجديدة
 */

// تضمين ملف التهيئة
require_once __DIR__ . '/app/init.php';

echo "<h2>اختبار المسارات الجديدة</h2>";

echo "<h3>1. المسارات الجديدة:</h3>";
echo "CSS: " . asset('css/style.css') . "<br>";
echo "JS: " . asset('js/main.js') . "<br>";

echo "<h3>2. اختبار الوصول المباشر:</h3>";
echo "<a href='" . asset('css/style.css') . "' target='_blank'>اختبار ملف CSS</a><br>";
echo "<a href='" . asset('js/main.js') . "' target='_blank'>اختبار ملف JS</a><br>";

echo "<h3>3. فحص وجود الملفات:</h3>";
$cssFile = __DIR__ . '/assets/css/style.css';
$jsFile = __DIR__ . '/assets/js/main.js';

echo "ملف CSS موجود: " . (file_exists($cssFile) ? 'نعم' : 'لا') . " - " . $cssFile . "<br>";
echo "ملف JS موجود: " . (file_exists($jsFile) ? 'نعم' : 'لا') . " - " . $jsFile . "<br>";

echo "<h3>4. اختبار صفحة إضافة الموعد:</h3>";
echo "<a href='admin/add_appointment' target='_blank'>اختبار صفحة إضافة الموعد</a><br>";
?> 