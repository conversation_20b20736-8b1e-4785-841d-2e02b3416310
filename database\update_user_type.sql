-- تحديث قاعدة البيانات لإضافة حقل user_type
USE nafsi_platform;

-- التحقق من وجود عمود user_type قبل إضافته
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'nafsi_platform' 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'user_type') = 0,
    'ALTER TABLE users ADD COLUMN user_type ENUM(\'regular\', \'specialist\', \'admin\') DEFAULT \'regular\' AFTER profile_image;',
    'SELECT \'Column user_type already exists\' as message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- تحديث المستخدم المشرف الموجود
UPDATE users SET user_type = 'admin' WHERE email = '<EMAIL>';

-- تحدي<PERSON> جميع المستخدمين الموجودين في جدول admins
UPDATE users u 
JOIN admins a ON u.id = a.user_id 
SET u.user_type = 'admin';

-- تحديث جميع المستخدمين الموجودين في جدول specialists
UPDATE users u 
JOIN specialists s ON u.id = s.user_id 
SET u.user_type = 'specialist'; 